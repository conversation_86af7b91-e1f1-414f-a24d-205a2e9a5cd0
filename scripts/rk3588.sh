#!/bin/bash
if [ -f /tmp/cpu_temper.log ];then
	rm -rf /tmp/cpu_temper.log
fi

for i in {1..200}
do
	echo  "$(date +%Y-%m-%d\ %H:%M:%S)" >> /tmp/cpu_temper.log
	soc_thermal=`cat /sys/class/thermal/thermal_zone0/temp`
	soc_thermal_interger=${soc_thermal:0:2}
	soc_thermal_decimal=${soc_thermal:2:3}
	soc_thermal_float="soc_thermal="${soc_thermal_interger}"."${soc_thermal_decimal}
	CPU_A76_01=`cat /sys/class/thermal/thermal_zone1/temp`
	CPU_A76_01_interger=${CPU_A76_01:0:2}
	CPU_A76_01_decimal=${CPU_A76_01:2:3}
	CPU_A76_01_float="CPU_A76_01="${CPU_A76_01_interger}"."${CPU_A76_01_decimal}
        CPU_A76_23=`cat /sys/class/thermal/thermal_zone2/temp`
	CPU_A76_23_interger=${CPU_A76_23:0:2}
        CPU_A76_23_decimal=${CPU_A76_23:2:3}
        CPU_A76_23_float="CPU_A76_23="${CPU_A76_23_interger}"."${CPU_A76_23_decimal}
	CPU_A55_0123=`cat /sys/class/thermal/thermal_zone3/temp`
	CPU_A55_0123_interger=${CPU_A55_0123:0:2}
        CPU_A55_0123_decimal=${CPU_A55_0123:2:3}
        CPU_A55_0123_float="CPU_A55_0123="${CPU_A55_0123_interger}"."${CPU_A55_0123_decimal}
	PD_CENTER=`cat /sys/class/thermal/thermal_zone4/temp`
        PD_CENTER_interger=${PD_CENTER:0:2}
        PD_CENTER_decimal=${PD_CENTER:2:3}
        PD_CENTER_float="PD_CENTER="${PD_CENTER_interger}"."${PD_CENTER_decimal}
	GPU=`cat /sys/class/thermal/thermal_zone5/temp`
        GPU_interger=${GPU:0:2}
        GPU_decimal=${GPU:2:3}
        GPU_float="GPU="${GPU_interger}"."${GPU_decimal}
	NPU=`cat /sys/class/thermal/thermal_zone6/temp`
        NPU_interger=${NPU:0:2}
        NPU_decimal=${NPU:2:3}
        NPU_float="NPU="${NPU_interger}"."${NPU_decimal}
  NPU_Occupancy_Rate=`cat /sys/kernel/debug/rknpu/load`
        NPU_Occupancy_Rate_interger=${NPU_Occupancy_Rate:0:2}
        NPU_Occupancy_Rate_decimal=${NPU_Occupancy_Rate:2:3}
        NPU_Occupancy_Rate_float="NPU_Occupancy_Rate="${NPU_Occupancy_Rate_interger}"."${NPU_Occupancy_Rate_decimal}
  GPU_Occupancy_Rate=`cat /sys/class/devfreq/fb000000.gpu/load`
        GPU_Occupancy_Rate_interger=${GPU2:0:2}
        GPU_Occupancy_Rate_decimal=${GPU2:2:3}
        GPU_Occupancy_Rate_float="GPU_Occupancy_Rate="${GPU_Occupancy_Rate_interger}"."${GPU_Occupancy_Rate_decimal}
	line=${soc_thermal_float}"  "${CPU_A76_01_float}"  "${CPU_A76_23_float}"  "${CPU_A55_0123_float}"  "${PD_CENTER_float}"  "${GPU_float}"  "${NPU_float}"  "${NPU_Occupancy_Rate}"  "${GPU_Occupancy_Rate}
	echo $line >> /tmp/cpu_temper.log
	sync
	sleep 2s
done
