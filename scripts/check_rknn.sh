# check npu driver version
sudo cat /sys/kernel/debug/rknpu/version
sudo cat /sys/kernel/debug/rkrga/driver_version
# 查询rknn_server版本
strings /usr/bin/rknn_server | grep -i "rknn_server version"
# 显示rknn_server版本为X.X.X
# rknn_server version: X.X.X
# 查询librknnrt.so库版本
strings /usr/lib/librknnrt.so | grep -i "librknnrt version"
# 显示librknnrt库版本为X.X.X
# librknnrt version: X.X.X
echo "Yolvv5 Model version:" `strings ../bin/model/RK3588/yolov5s-640-640.rknn | grep "compiler version:"`
# check ffmedia version
echo "FFMedia version:" `strings /lib/aarch64-linux-gnu/libff_media.so | grep "v2.3"`
strings /lib/aarch64-linux-gnu/librockchip_mpp.so.1 | grep "tag:"
strings /lib/aarch64-linux-gnu/librga.so.2 | grep "rga_api version"



