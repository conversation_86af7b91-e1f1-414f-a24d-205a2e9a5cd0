#!/bin/bash

# 默认配置
SEARCH_DIR="/opt/xiaolu/"
OLD_IP="*************"
#OLD_IP="*************"
RECURSIVE=true
DRY_RUN=false
BACKUP=false

NEW_IP="$1"
REDIS_SYNC_IP="$2"

# 检查是否提供了新IP
if [ -z "$NEW_IP" ]; then
  echo "用法: $0 新IP地址 [RedisDataSync_IP地址] [--no-recursion] [--dry-run]"
  exit 1
fi

# 解析命令行参数
shift
if [ -n "$REDIS_SYNC_IP" ]; then
  shift
fi
for arg in "$@"; do
  case $arg in
    --no-recursion)
      RECURSIVE=false
      ;;
    --dry-run)
      DRY_RUN=true
      ;;
    *)
      echo "未知参数: $arg"
      exit 1
      ;;
  esac
done

# 检查目录是否存在
if [ ! -d "$SEARCH_DIR" ]; then
  echo "错误：目录 $SEARCH_DIR 不存在。"
  exit 1
fi

echo "开始处理目录: $SEARCH_DIR"
echo "旧IP地址: $OLD_IP"
echo "新IP地址: $NEW_IP"
if [ -n "$REDIS_SYNC_IP" ]; then
  echo "RedisDataSync IP地址: $REDIS_SYNC_IP"
fi
echo "递归搜索: ${RECURSIVE}"
echo "Dry-run模式: ${DRY_RUN}"

# 处理单个文件的函数
process_file() {
  local file="$1"

  # 检查是否包含目标 IP
  if grep -q "$OLD_IP" "$file"; then
    echo "📄 匹配到目标IP: $file"

    if [ "$DRY_RUN" = true ]; then
      echo "  🔍 [dry-run] 将替换 '$OLD_IP' -> '$NEW_IP'"
    else
      if [ "$BACKUP" = true ]; then
        cp "$file" "$file.bak"
        echo "  💾 已备份文件: $file.bak"
      fi

      # 使用 sed 替换 IP（使用 | 作为分隔符避免冲突）
      sed -i "s|$OLD_IP|$NEW_IP|g" "$file"
      echo "  ✅ 已完成替换: $file"
    fi
  fi
}

# 处理RedisDataSync配置文件的函数
process_redis_sync_config() {
  local config_file="/opt/xiaolu/RedisDataSync/cfg/syscfg.xml"

  if [ ! -f "$config_file" ]; then
    echo "⚠️  警告：RedisDataSync配置文件不存在: $config_file"
    return
  fi

  # 检查是否包含{{IP_ADDRESS}}标识符
  if grep -q "{{IP_ADDRESS}}" "$config_file"; then
    echo "📄 匹配到{{IP_ADDRESS}}标识符: $config_file"

    if [ "$DRY_RUN" = true ]; then
      echo "  🔍 [dry-run] 将替换 '{{IP_ADDRESS}}' -> '$REDIS_SYNC_IP'"
    else
      if [ "$BACKUP" = true ]; then
        cp "$config_file" "$config_file.bak"
        echo "  💾 已备份文件: $config_file.bak"
      fi

      # 使用 sed 替换 {{IP_ADDRESS}} 标识符
      sed -i "s|{{IP_ADDRESS}}|$REDIS_SYNC_IP|g" "$config_file"
      echo "  ✅ 已完成{{IP_ADDRESS}}替换: $config_file"
    fi
  else
    echo "ℹ️  RedisDataSync配置文件中未找到{{IP_ADDRESS}}标识符: $config_file"
  fi
}

# 查找并处理文件
if [ "$RECURSIVE" = true ]; then
  find "$SEARCH_DIR" -type f -name "*.xml" -o -name "*.json" | while read file; do
    process_file "$file"
  done
else
  find "$SEARCH_DIR" -maxdepth 1 -type f -name "*.xml" -o -name "*.json" | while read file; do
    process_file "$file"
  done
fi

# 处理RedisDataSync配置文件（如果提供了IP地址）
if [ -n "$REDIS_SYNC_IP" ]; then
  echo ""
  echo "开始处理RedisDataSync配置文件..."
  process_redis_sync_config
fi

echo "🎉 处理完成。"

