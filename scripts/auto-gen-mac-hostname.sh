#!/bin/bash

# 检查是否已执行过
if [ -f /etc/hostname_set ]; then
    echo "主机名已设置，跳过执行"
    exit 0
fi

# 从eth0接口获取MAC地址（确保去掉冒号和空白符）
MAC=$(ip link show eth0 2>/dev/null | awk '/ether/ {print $2}' | tr -d ':')

# 检查是否成功获取MAC地址
if [ -z "$MAC" ]; then
    echo "错误：未检测到有效MAC地址！可能是网络接口未启用，请检查eth0是否存在。" >&2
    MAC="FFFFFF"
fi

# 生成新主机名
NEW_HOSTNAME="xiaolu-$MAC"

# 修改系统主机名配置
#echo "$NEW_HOSTNAME" > /etc/hostname  # 更新系统文件
hostnamectl set-hostname "$NEW_HOSTNAME"   # 即时生效

# 修改hosts文件中的主机名（替换*********行）
#sed -i "s/\(^127\.0\.1\.1.*\)/*********\t$NEW_HOSTNAME/" /etc/hosts

# 标记成功设置
touch /etc/hostname_set

systemctl restart avahi-daemon.service &> /dev/null

echo "主机名已更新为：$NEW_HOSTNAME"