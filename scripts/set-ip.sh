#!/bin/bash

##################################################
#      完整自动配置静态IP的nmcli脚本（中文版）     #
##################################################

# 修改以下参数以适应你的环境
INTERFACE="eth0"          # 网卡名称（如 eth0、enp0s3）
IP="*************"        # 目标静态IP地址
NETMASK="24"              # 子网掩码（如24代表*************）
GATEWAY="***********"    # 网关地址
DNS="*******,*******"    # DNS服务器，多个用逗号分隔
CONNECTION_NAME=""        # 自动检测（勿改动）

###########################################
# 自动获取对应连接名称（兼容中文名称）
###########################################

# 方法1：通过NMCLI字段模式（推荐）
CONNECTION_NAME=$(nmcli -t -f NAME,DEVICE con show | awk -F: -v dev="$INTERFACE" '$2 == dev {print $1}' | head -n1)

# 方法2：兼容性方案（若方法1失败时尝试）
if [ -z "$CONNECTION_NAME" ]; then
    CONNECTION_NAME=$(nmcli con show | awk -v dev="$INTERFACE" '$4 == dev {print $1}')
fi

# 如果仍未找到
if [ -z "$CONNECTION_NAME" ]; then
    echo "致命错误：未找到网卡 $INTERFACE 对应的连接名称"
    exit 1
else
    echo "检测到的网络连接配置名称：$CONNECTION_NAME"
    echo "-------------------"
fi

###########################################
# 网络配置核心部分
###########################################

echo "正在配置静态IP..."
nmcli con mod "$CONNECTION_NAME" ipv4.method manual
nmcli con mod "$CONNECTION_NAME" ipv4.address "$IP/$NETMASK"
nmcli con mod "$CONNECTION_NAME" ipv4.gateway "$GATEWAY"
nmcli con mod "$CONNECTION_NAME" ipv4.dns "$DNS"

echo "正在应用DNS设置..."
nmcli con mod "$CONNECTION_NAME" ipv4.ignore-auto-dns yes # 禁用自动DNS获取

###########################################
# 重启网络连接使配置生效
###########################################

echo "正在激活新配置..."
nmcli con up "$CONNECTION_NAME"

echo "-------------------"
echo "验证配置结果："
nmcli con show "$CONNECTION_NAME" | grep -E 'ipv4.method|ipv4.address|ipv4.gateway|ipv4.dns'