# generate_version.ps1
# 在 Windows 本地运行的 PowerShell 脚本

# 获取 Git 信息
$gitHash = git rev-parse --short HEAD
$gitBranch = git rev-parse --abbrev-ref HEAD
$gitTag = git describe --tags --abbrev=0 2>$null
if ($LASTEXITCODE -ne 0) { $gitTag = "no-tag" }

# 生成 C++ 源文件
$content = @"
// autogenerated, DO NOT CHANGE BY MANNUAL.
#pragma once
#include <string_view>
#include <string>

namespace version {
    inline constexpr std::string_view GIT_HASH = "$gitHash";
    inline constexpr std::string_view GIT_BRANCH = "$gitBranch";
    inline constexpr std::string_view GIT_TAG = "$gitTag";

    #define BUILD_TIMESTAMP __DATE__ " " __TIME__
    inline constexpr std::string_view BUILD_TIME = BUILD_TIMESTAMP;

    inline std::string getVersionInfo() {
        return std::string("Git Branch: ") + std::string(GIT_BRANCH) + "\n"
               + "Git Commit: " + std::string(GIT_HASH) + "\n"
               + "Git Tag: " + std::string(GIT_TAG) + "\n"
               + "Build Time: " + std::string(BUILD_TIME);
    }
}
"@

# 写入到文件
$content | Out-File -Encoding utf8 "src/utils/version_info.hpp"