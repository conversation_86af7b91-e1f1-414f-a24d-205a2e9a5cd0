# 查看NPU占用
while :
do
	clear
	echo "---------------$(date)--------------------"
	echo "当前NPU占用:"
	sudo cat /sys/kernel/debug/rknpu/load
	echo "当前NPU温度:"
	temp=$(cat /sys/class/thermal/thermal_zone6/temp)
	converted_temp=$(echo "scale=3; $temp/1000" | bc)
	echo "NPU Temperature: $converted_temp°C"

	echo "当前CPU占用:"
	#cpu_usage=$(mpstat 1 1 | awk '/Average/ {print 100 - $NF"%"}')
	cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print 100 - $8"%"}')
	echo "CPU Usage: $cpu_usage"

	echo "当前内存占用:"
	mem_usage=$(free | awk '/Mem/{used=$2-$7; printf("Used Memory: %.2fGB/%.2fGB (%.2f%%)\n", used/1024/1024, $2/1024/1024, used/$2 * 100)}')
	echo $mem_usage

	echo "当前SOC温度:"
	temp=$(cat /sys/class/thermal/thermal_zone0/temp)
	converted_temp=$(echo "scale=3; $temp/1000" | bc)
	echo "SOC Temperature: $converted_temp°C"

	#echo "当前GPU占用:"
	#cat /sys/class/devfreq/fb000000.gpu/load
	sleep 1
done

