cmake_minimum_required(VERSION 3.10)

if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm" OR CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
    set(OPENAPI_LIB_DIR ${PROJECT_SOURCE_DIR}/third_party/OpenAPI_C/aarch64/public/lib/linux/Release)
    set(OPENAPI_INCLUDE_DIR ${PROJECT_SOURCE_DIR}/third_party/OpenAPI_C/aarch64/public/include/)
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64" OR CMAKE_SYSTEM_PROCESSOR MATCHES "i686")
    set(OPENAPI_LIB_DIR ${PROJECT_SOURCE_DIR}/third_party/OpenAPI_C/x86_64/public/lib/linux/Release)
    set(OPENAPI_INCLUDE_DIR ${PROJECT_SOURCE_DIR}/third_party/OpenAPI_C/x86_64/public/include/)
else()
    message(STATUS "Current platform is neither ARM nor x86")
endif()

set(OPENAPI_LIBS "-lHttpUtil")

# 查找 nats库和头文件
#find_path(NATS_INCLUDE_DIR
#        NAMES nats/nats.h
#        PATHS /opt/xiaolu/pubLib/nats_clt/include
#)
#
#find_library(NATS_LIBRARIES
#        NAMES libnats.so.3.9.0
#        PATHS /opt/xiaolu/pubLib/nats_clt/lib
#)
#message(STATUS "NATS include dirs:" ${NATS_INCLUDE_DIR})
#message(STATUS "NATS library dirs:" ${NATS_LIBRARIES})

# 包含目录
include_directories(
        ${PROJECT_SOURCE_DIR}/src
        ${OPENAPI_INCLUDE_DIR}
#        ${NATS_INCLUDE_DIR}
)
link_directories(
        ${OPENAPI_LIB_DIR}
)

# 添加可执行文件
add_executable(hk_signal_tester hk_signal_tester.cpp
        ${PROJECT_SOURCE_DIR}/src/components/signal_controllers/SignalDevices/SignalDeviceHKOPENAPI.cpp)

# 添加HiSense20999测试工具
add_executable(hisense20999_tester hisense20999_tester.cpp
        ${PROJECT_SOURCE_DIR}/src/components/signal_controllers/SignalDevices/SignalDeviceHiSense20999.cpp)

add_executable(chainzone_tester
        chainzone_signal_tester.cpp
        ${PROJECT_SOURCE_DIR}/src/components/signal_controllers/SignalDevices/ChainzoneProtocol.cpp
        ${PROJECT_SOURCE_DIR}/src/components/signal_controllers/SignalDevices/SignalDeviceChainzone.cpp
)

# 链接库
#target_link_libraries(hk_signal_tester STNCore STNUtils Threads::Threads ${OPENAPI_LIBS} ${NATS_LIBRARIES})
target_link_libraries(hk_signal_tester Threads::Threads ${OPENAPI_LIBS})
target_link_libraries(hisense20999_tester Threads::Threads)
target_link_libraries(chainzone_tester Threads::Threads)

# 安装规则
install(TARGETS hk_signal_tester hisense20999_tester DESTINATION ${CMAKE_INSTALL_PREFIX}/bin)
install(FILES hk_signal_config.json hisense20999_config.json DESTINATION ${CMAKE_INSTALL_PREFIX}/config)
