//
// HiSense20999信号机接口测试工具
//

#include "components/signal_controllers/SignalDevices/SignalDeviceHiSense20999.hpp"
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <fstream>
#include <sstream>
#include <vector>
#include <map>
#include <functional>
#include <memory>
#include <csignal>
#include <atomic>
#include <sys/select.h>
#include <unistd.h>
#include <errno.h>
#include <cstring>
//#include "components/signal_controllers/SignalDevices/SignalDeviceHiSense.hpp"

// 全局变量
std::atomic<bool> g_running(true);
std::shared_ptr<SignalDeviceHiSense20999> g_device;
std::shared_ptr<SignalCtl> g_signalCtl;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "接收到信号 " << signal << "，正在退出..." << std::endl;
    g_running = false;

    // 强制断开连接，确保资源释放
    if (g_device) {
        try {
            std::cout << "强制断开信号机连接..." << std::endl;
            g_device.reset();
        } catch (const std::exception& e) {
            std::cerr << "断开连接时发生异常: " << e.what() << std::endl;
        }
    }

    // 如果是SIGINT (Ctrl+C)，设置一个定时器，如果程序没有在合理时间内退出，则强制退出
    if (signal == SIGINT) {
        static bool force_exit = false;
        if (force_exit) {
            std::cout << "强制退出程序" << std::endl;
            std::exit(1);
        }
        force_exit = true;

        // 创建一个线程，如果主线程在3秒内没有退出，则强制退出程序
        std::thread([](){
            std::this_thread::sleep_for(std::chrono::seconds(3));
            std::cout << "程序未能在3秒内正常退出，强制退出" << std::endl;
            std::exit(1);
        }).detach();
    }
}

// 帮助信息
void printHelp() {
    std::cout << "\nHiSense20999信号机接口测试工具\n" << std::endl;
    std::cout << "命令列表：" << std::endl;
    std::cout << "  help                - 显示帮助信息" << std::endl;
    std::cout << "  load <config_file>  - 从文件加载配置" << std::endl;
    std::cout << "  connect             - 连接到信号机" << std::endl;
    std::cout << "  disconnect          - 断开与信号机的连接" << std::endl;
    std::cout << "  status              - 查询信号机状态" << std::endl;
    std::cout << "  phase <number>      - 设置相位" << std::endl;
    std::cout << "  reset               - 复位信号机" << std::endl;
    std::cout << "  reconnect <seconds> - 重连信号机，等待指定秒数" << std::endl;
    std::cout << "  mode <0|1>          - 设置控制模式（0=监控，1=手动）" << std::endl;
    std::cout << "  monitor <seconds>   - 监控信号机状态，每隔指定秒数查询一次" << std::endl;
    std::cout << "  threshold <seconds> - 设置重连阈值（秒）" << std::endl;
    std::cout << "  tt_threshold <sec>  - 设置timetable模式重连阈值（秒）" << std::endl;
    std::cout << "  exit                - 退出程序" << std::endl;
    std::cout << std::endl;
}

// 从文件加载配置
bool loadConfig(const std::string& filename) {
    try {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cerr << "无法打开配置文件: " << filename << std::endl;
            return false;
        }

        std::stringstream buffer;
        buffer << file.rdbuf();
        std::string config = buffer.str();

        g_signalCtl = std::make_shared<SignalCtl>();
        g_signalCtl->fromJson(config);

        std::cout << "配置加载成功" << std::endl;
        std::cout << "  主机IP: " << g_signalCtl->hostIP << std::endl;
        std::cout << "  主机端口: " << g_signalCtl->hostPort << std::endl;
        std::cout << "  超时时间: " << g_signalCtl->timeout << " 秒" << std::endl;
        std::cout << "  轮询周期: " << g_signalCtl->tscPollingPeriod_ms << " 毫秒" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "加载配置失败: " << e.what() << std::endl;
        return false;
    }
}

// 连接到信号机
bool connect() {
    try {
        if (!g_signalCtl) {
            std::cerr << "请先加载配置" << std::endl;
            return false;
        }

        g_device = std::make_shared<SignalDeviceHiSense20999>(*g_signalCtl);
        std::cout << "信号机连接成功" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "连接信号机失败: " << e.what() << std::endl;
        return false;
    }
}

// 断开与信号机的连接
bool disconnect() {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "正在断开信号机连接..." << std::endl;

        // 先尝试将信号机设置为监控模式
        try {
            g_device->setControlMode(0); // 设置为监控模式
        } catch (...) {
            // 忽略异常，继续断开连接
        }

        // 使用智能指针的reset方法释放资源
        g_device.reset();

        // 确保内存被释放
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        std::cout << "已断开与信号机的连接" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "断开连接失败: " << e.what() << std::endl;

        // 强制释放资源
        g_device.reset();
        return false;
    }
}

// 查询信号机状态
bool queryStatus() {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        SignalEnvState state = g_device->getSignalState();
        state.updatePhaseInfo(*g_signalCtl);
        std::string ses_str = state.toJson().dump();
        std::cout << "信号机状态：" << std::endl;
        std::cout << "  SignalEnvState String: " << ses_str << std::endl;
        std::cout << "  当前相位: " << state.currentPhase << std::endl;
        std::cout << "  相位时间: " << state.phaseTime << " 秒" << std::endl;
        std::cout << "  控制状态: " << (state.signalCtlStatus ? "可控" : "不可控") << std::endl;
        std::cout << "  当前计划: " << state.currentPlan << std::endl;
        std::cout << "  设备状态: " << ISignalDevice::toString(g_device->getCurDeviceState()) << std::endl;

        return true;
    } catch (const std::exception& e) {
        std::cerr << "查询状态失败: " << e.what() << std::endl;
        return false;
    }
}

// 设置相位
bool setPhase(int phase) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        g_device->controlSignal(phase);
        std::cout << "已发送相位设置命令: " << phase << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "设置相位失败: " << e.what() << std::endl;
        return false;
    }
}

// 复位信号机
bool resetSignal() {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        bool result = g_device->reset();
        if (result) {
            std::cout << "信号机复位成功" << std::endl;
        } else {
            std::cout << "信号机复位失败" << std::endl;
        }
        return result;
    } catch (const std::exception& e) {
        std::cerr << "复位信号机失败: " << e.what() << std::endl;
        return false;
    }
}

// 重连信号机
bool reconnectSignal(int waitSeconds) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "正在重连信号机，等待 " << waitSeconds << " 秒..." << std::endl;
        bool result = g_device->reconnect(waitSeconds);
        if (result) {
            std::cout << "信号机重连成功" << std::endl;
        } else {
            std::cout << "信号机重连失败" << std::endl;
        }
        return result;
    } catch (const std::exception& e) {
        std::cerr << "重连信号机失败: " << e.what() << std::endl;
        return false;
    }
}

// 设置控制模式
bool setControlMode(int mode) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        bool result = g_device->setControlMode(mode);
        if (result) {
            std::cout << "设置控制模式成功: " << (mode == 1 ? "手动控制" : "监控模式") << std::endl;
        } else {
            std::cout << "设置控制模式失败" << std::endl;
        }
        return result;
    } catch (const std::exception& e) {
        std::cerr << "设置控制模式失败: " << e.what() << std::endl;
        return false;
    }
}

// 设置重连阈值
bool setReconnectThreshold(int seconds) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        g_device->setReconnectThreshold(seconds);
        std::cout << "设置重连阈值成功: " << seconds << " 秒" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "设置重连阈值失败: " << e.what() << std::endl;
        return false;
    }
}

// 设置timetable模式重连阈值
bool setTimetableModeReconnectThreshold(int seconds) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        g_device->setTimetableModeReconnectThreshold(seconds);
        std::cout << "设置timetable模式重连阈值成功: " << seconds << " 秒" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "设置timetable模式重连阈值失败: " << e.what() << std::endl;
        return false;
    }
}

// 监控信号机状态
bool monitorStatus(int interval) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "开始监控信号机状态，每 " << interval << " 秒查询一次" << std::endl;
        std::cout << "按Ctrl+C停止监控" << std::endl;

        while (g_running) {
            try {
                // 查询状态
                if (!queryStatus()) {
                    std::cout << "查询状态失败，尝试重连..." << std::endl;

                    // 尝试重连
                    try {
                        g_device->reconnect(1);
                    } catch (...) {
                        // 忽略重连异常
                    }
                }

                std::cout << "-----------------------------------" << std::endl;

                // 使用小时间间隔的睡眠，以便可以更快响应Ctrl+C
                for (int i = 0; i < interval * 10 && g_running; i++) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }

                // 如果程序正在退出，立即跳出循环
                if (!g_running) break;

            } catch (const std::exception& e) {
                std::cerr << "监控异常: " << e.what() << std::endl;
                // 等待一秒后继续
                std::this_thread::sleep_for(std::chrono::seconds(1));
            } catch (...) {
                std::cerr << "监控时发生未知异常" << std::endl;
                // 等待一秒后继续
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }

        std::cout << "监控已停止" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "监控信号机状态失败: " << e.what() << std::endl;
        return false;
    }
}

// 主函数
int main(int argc, char* argv[]) {
    // 设置信号处理
    std::signal(SIGINT, signalHandler);
    std::signal(SIGTERM, signalHandler);

    std::cout << "HiSense20999信号机接口测试工具" << std::endl;
    std::cout << "输入 'help' 获取帮助信息" << std::endl;

    // 命令映射
    std::map<std::string, std::function<bool(const std::vector<std::string>&)>> commands;

    commands["help"] = [](const std::vector<std::string>& args) {
        printHelp();
        return true;
    };

    commands["load"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: load <config_file>" << std::endl;
            return false;
        }
        return loadConfig(args[1]);
    };

    commands["connect"] = [](const std::vector<std::string>& args) {
        return connect();
    };

    commands["disconnect"] = [](const std::vector<std::string>& args) {
        return disconnect();
    };

    commands["status"] = [](const std::vector<std::string>& args) {
        return queryStatus();
    };

    commands["phase"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: phase <number>" << std::endl;
            return false;
        }
        try {
            int phase = std::stoi(args[1]);
            return setPhase(phase);
        } catch (const std::exception& e) {
            std::cerr << "无效的相位值: " << args[1] << std::endl;
            return false;
        }
    };

    commands["reset"] = [](const std::vector<std::string>& args) {
        return resetSignal();
    };

    commands["reconnect"] = [](const std::vector<std::string>& args) {
        int seconds = 3; // 默认等待3秒
        if (args.size() >= 2) {
            try {
                seconds = std::stoi(args[1]);
            } catch (const std::exception& e) {
                std::cerr << "无效的等待时间: " << args[1] << "，使用默认值3秒" << std::endl;
            }
        }
        return reconnectSignal(seconds);
    };

    commands["mode"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: mode <0|1>" << std::endl;
            return false;
        }
        try {
            int mode = std::stoi(args[1]);
            if (mode != 0 && mode != 1) {
                std::cerr << "无效的模式值: " << mode << "，必须是0或1" << std::endl;
                return false;
            }
            return setControlMode(mode);
        } catch (const std::exception& e) {
            std::cerr << "无效的模式值: " << args[1] << std::endl;
            return false;
        }
    };

    commands["threshold"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: threshold <seconds>" << std::endl;
            return false;
        }
        try {
            int seconds = std::stoi(args[1]);
            return setReconnectThreshold(seconds);
        } catch (const std::exception& e) {
            std::cerr << "无效的阈值: " << args[1] << std::endl;
            return false;
        }
    };

    commands["tt_threshold"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: tt_threshold <seconds>" << std::endl;
            return false;
        }
        try {
            int seconds = std::stoi(args[1]);
            return setTimetableModeReconnectThreshold(seconds);
        } catch (const std::exception& e) {
            std::cerr << "无效的阈值: " << args[1] << std::endl;
            return false;
        }
    };

    commands["monitor"] = [](const std::vector<std::string>& args) {
        int interval = 5; // 默认5秒
        if (args.size() >= 2) {
            try {
                interval = std::stoi(args[1]);
                if (interval < 1) {
                    std::cerr << "间隔时间太短，设置为1秒" << std::endl;
                    interval = 1;
                }
            } catch (const std::exception& e) {
                std::cerr << "无效的间隔时间: " << args[1] << "，使用默认值5秒" << std::endl;
            }
        }
        return monitorStatus(interval);
    };

    commands["exit"] = [](const std::vector<std::string>& args) {
        g_running = false;
        return true;
    };

    // 如果有命令行参数，尝试加载配置文件
    if (argc > 1) {
        std::cout << "尝试加载配置文件: " << argv[1] << std::endl;
        loadConfig(argv[1]);
    } else {
        // 默认加载当前目录下的配置文件
        const std::vector<std::string> defaultConfigFiles = {
            "hisense20999_config.json",
            "config/hisense20999_config.json",
            "../config/hisense20999_config.json"
        };

        bool configLoaded = false;
        for (const auto& configFile : defaultConfigFiles) {
            std::ifstream f(configFile);
            if (f.good()) {
                std::cout << "自动加载配置文件: " << configFile << std::endl;
                configLoaded = loadConfig(configFile);
                if (configLoaded) break;
            }
        }

        if (!configLoaded) {
            std::cout << "未找到默认配置文件，请使用 'load <config_file>' 命令加载配置文件" << std::endl;
        } else {
            // 如果成功加载配置，尝试自动连接
            std::cout << "尝试自动连接到信号机..." << std::endl;
            if (connect()) {
                std::cout << "自动连接成功，请使用 'status' 命令查询状态" << std::endl;
            } else {
                std::cout << "自动连接失败，请使用 'connect' 命令手动连接" << std::endl;
            }
        }
    }

    // 主循环
    while (g_running) {
        std::cout << "> " << std::flush;

        // 使用带超时的输入方式，以便可以响应Ctrl+C
        std::string line;

        // 设置输入的非阻塞模式
        fd_set readfds;
        struct timeval tv;
        int stdin_fd = fileno(stdin);

        while (g_running) {
            FD_ZERO(&readfds);
            FD_SET(stdin_fd, &readfds);

            // 设置超时为0.5秒
            tv.tv_sec = 0;
            tv.tv_usec = 500000;

            int ret = select(stdin_fd + 1, &readfds, NULL, NULL, &tv);

            if (ret == -1) {
                // 错误发生
                if (errno != EINTR) { // 如果不是因为信号中断，则报错
                    std::cerr << "输入错误: " << strerror(errno) << std::endl;
                }
                break;
            } else if (ret == 0) {
                // 超时，检查g_running标志
                if (!g_running) break;
                continue;
            } else {
                // 有输入可用
                if (FD_ISSET(stdin_fd, &readfds)) {
                    std::getline(std::cin, line);
                    break;
                }
            }
        }

        // 如果程序正在退出，跳过命令处理
        if (!g_running) {
            break;
        }

        if (line.empty()) {
            continue;
        }

        // 解析命令
        std::istringstream iss(line);
        std::vector<std::string> args;
        std::string arg;
        while (iss >> arg) {
            args.push_back(arg);
        }

        if (args.empty()) {
            continue;
        }

        std::string cmd = args[0];
        auto it = commands.find(cmd);
        if (it != commands.end()) {
            try {
                it->second(args);
            } catch (const std::exception& e) {
                std::cerr << "命令执行异常: " << e.what() << std::endl;
            } catch (...) {
                std::cerr << "命令执行时发生未知异常" << std::endl;
            }
        } else {
            std::cerr << "未知命令: " << cmd << std::endl;
            std::cout << "输入 'help' 获取帮助信息" << std::endl;
        }
    }

    // 清理资源
    std::cout << "正在清理资源..." << std::endl;

    // 先尝试正常断开连接
    if (g_device) {
        try {
            disconnect();
        } catch (...) {
            // 如果正常断开失败，强制释放资源
            std::cerr << "正常断开连接失败，强制释放资源" << std::endl;
            g_device.reset();
        }
    }

    // 确保所有资源都被释放
    g_device = nullptr;
    g_signalCtl = nullptr;

    // 等待一小段时间，确保所有资源都被正确释放
    std::this_thread::sleep_for(std::chrono::milliseconds(200));

    std::cout << "程序退出" << std::endl;
    return 0;
}
