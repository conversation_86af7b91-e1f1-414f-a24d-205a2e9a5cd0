//
// Chainzone信号机接口测试工具
//

#include "components/signal_controllers/SignalDevices/SignalDeviceChainzone.hpp"
#include "utils/UDPClient.hpp"
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <fstream>
#include <sstream>
#include <vector>
#include <map>
#include <functional>
#include <memory>
#include <csignal>
#include <atomic>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>
#include <errno.h>

// 全局变量
std::atomic<bool> g_running(true);
std::shared_ptr<SignalDeviceChainzone> g_device;
std::shared_ptr<SignalCtl> g_signalCtl;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "Received signal " << signal << ", exiting..." << std::endl;
    g_running = false;
}

// 帮助信息
void printHelp() {
    std::cout << "\nChainzone信号机接口测试工具\n" << std::endl;
    std::cout << "命令列表：" << std::endl;
    std::cout << "  help                - 显示帮助信息" << std::endl;
    std::cout << "  load <config_file>  - 从文件加载配置" << std::endl;
    std::cout << "  connect             - 连接到信号机" << std::endl;
    std::cout << "  disconnect          - 断开与信号机的连接" << std::endl;
    std::cout << "  status              - 查询信号机状态" << std::endl;
    std::cout << "  phase <number>      - 设置相位" << std::endl;
    std::cout << "  reset               - 复位信号机" << std::endl;
    std::cout << "  reconnect <seconds> - 重连信号机，等待指定秒数" << std::endl;
    std::cout << "  mode <0|1>          - 设置控制模式（0=自动，1=手动）" << std::endl;
    std::cout << "  monitor <seconds>   - 监控信号机状态，每隔指定秒数查询一次" << std::endl;
    std::cout << "  synctime            - 同步系统时间到信号机" << std::endl;
    std::cout << "  exit                - 退出程序" << std::endl;
    std::cout << "  test                - 测试网络连接" << std::endl;
    std::cout << "  scan                - 扫描信号机上的常见端口" << std::endl;
    std::cout << std::endl;
    std::cout << "默认配置文件: config/chainzone_signal_config.json" << std::endl;
    std::cout << "默认IP地址: *************" << std::endl;
    std::cout << "默认端口: 9520" << std::endl;
    std::cout << std::endl;
}

// 从文件加载配置
bool loadConfig(const std::string& filename) {
    try {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cerr << "无法打开配置文件: " << filename << std::endl;
            return false;
        }

        std::stringstream buffer;
        buffer << file.rdbuf();
        std::string config = buffer.str();

        g_signalCtl = std::make_shared<SignalCtl>();
        g_signalCtl->fromJson(config);

        std::cout << "配置加载成功" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "加载配置失败: " << e.what() << std::endl;
        return false;
    }
}

// 连接到信号机
bool connect() {
    try {
        if (!g_signalCtl) {
            std::cerr << "请先加载配置" << std::endl;
            return false;
        }

        g_device = std::make_shared<SignalDeviceChainzone>(*g_signalCtl);
        std::cout << "信号机连接成功" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "连接信号机失败: " << e.what() << std::endl;
        return false;
    }
}

// 断开与信号机的连接
bool disconnect() {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        g_device.reset();
        std::cout << "已断开与信号机的连接" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "断开连接失败: " << e.what() << std::endl;
        return false;
    }
}

// 查询信号机状态
bool queryStatus() {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        SignalEnvState state = g_device->getSignalState();
        state.updatePhaseInfo(*g_signalCtl);
        std::string ses_str = state.toJson().dump();
        std::cout << "信号机状态：" << std::endl;
        std::cout << "  SignalEnvState String: " << ses_str << std::endl;
        std::cout << "  当前相位: " << state.currentPhase << std::endl;
        std::cout << "  相位时间: " << state.phaseTime << " 秒" << std::endl;
        std::cout << "  控制状态: " << (state.signalCtlStatus ? "可控" : "不可控") << std::endl;
        std::cout << "  当前计划: " << state.currentPlan << std::endl;
        std::cout << "  设备状态: " << ISignalDevice::toString(g_device->getCurDeviceState()) << std::endl;

        return true;
    } catch (const std::exception& e) {
        std::cerr << "查询状态失败: " << e.what() << std::endl;
        return false;
    }
}

// 设置相位
bool setPhase(int phase) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "设置相位为 " << phase << std::endl;
        g_device->controlSignal(phase);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "设置相位失败: " << e.what() << std::endl;
        return false;
    }
}

// 复位信号机
bool resetSignal() {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "复位信号机" << std::endl;
        bool result = g_device->reset();
        std::cout << "复位结果: " << (result ? "成功" : "失败") << std::endl;
        return result;
    } catch (const std::exception& e) {
        std::cerr << "复位信号机失败: " << e.what() << std::endl;
        return false;
    }
}

// 重连信号机
bool reconnectSignal(int seconds) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "重连信号机，等待 " << seconds << " 秒" << std::endl;
        bool result = g_device->reconnect(seconds);
        std::cout << "重连结果: " << (result ? "成功" : "失败") << std::endl;
        return result;
    } catch (const std::exception& e) {
        std::cerr << "重连信号机失败: " << e.what() << std::endl;
        return false;
    }
}

// 设置控制模式
bool setControlMode(int mode) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "设置控制模式为 " << (mode == 0 ? "自动" : "手动") << std::endl;
        bool result = g_device->setControlMode(mode);
        std::cout << "设置结果: " << (result ? "成功" : "失败") << std::endl;
        return result;
    } catch (const std::exception& e) {
        std::cerr << "设置控制模式失败: " << e.what() << std::endl;
        return false;
    }
}

// 同步时间到信号机
bool syncTime() {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "同步系统时间到信号机" << std::endl;
        // 这里需要调用Chainzone驱动的时间同步功能
        // 由于SignalDeviceChainzone没有直接的时间同步接口，
        // 我们需要通过驱动来实现
        std::cout << "时间同步功能需要通过驱动实现" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "同步时间失败: " << e.what() << std::endl;
        return false;
    }
}

// 监控信号机状态
bool monitorStatus(int interval) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "开始监控信号机状态，每 " << interval << " 秒查询一次" << std::endl;
        std::cout << "按Ctrl+C停止监控" << std::endl;

        while (g_running) {
            queryStatus() ;
            std::cout << "-----------------------------------" << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(interval));
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "监控信号机状态失败: " << e.what() << std::endl;
        return false;
    }
}

// 测试网络连接 - 使用正确的协议格式
bool testConnection() {
    try {
        if (!g_signalCtl) {
            std::cerr << "请先加载配置" << std::endl;
            return false;
        }

        std::cout << "测试网络连接到 " << g_signalCtl->hostIP << ":" << g_signalCtl->hostPort << std::endl;
        
        int sock = socket(AF_INET, SOCK_DGRAM, 0);
        if (sock < 0) {
            std::cerr << "创建socket失败: " << strerror(errno) << std::endl;
            return false;
        }

        struct sockaddr_in addr;
        memset(&addr, 0, sizeof(addr));
        addr.sin_family = AF_INET;
        addr.sin_port = htons(g_signalCtl->hostPort);
        if (inet_pton(AF_INET, g_signalCtl->hostIP.c_str(), &addr.sin_addr) <= 0) {
            std::cerr << "无效的IP地址: " << g_signalCtl->hostIP << std::endl;
            close(sock);
            return false;
        }

        // 设置超时
        struct timeval tv;
        tv.tv_sec = 5;  // 增加超时时间
        tv.tv_usec = 0;
        setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv));

        // 尝试多种协议格式
        std::vector<std::vector<uint8_t>> testPackets = {
            // 格式1: 完整的Chainzone协议头部
            {0x0C, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x0A, 0x05, 0x00, 0x00},
            
            // 格式2: 简化版本 - 只有命令
            {0x0A, 0x05},
            
            // 格式3: 命令 + 长度
            {0x0A, 0x05, 0x00, 0x00},
            
            // 格式4: 长度在前
            {0x00, 0x04, 0x0A, 0x05},
            
            // 格式5: 尝试little endian命令
            {0x0C, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x05, 0x0A, 0x00, 0x00}
        };

        for (size_t i = 0; i < testPackets.size(); i++) {
            const auto& packet = testPackets[i];
            
            std::cout << "\n尝试格式 " << (i+1) << " (长度: " << packet.size() << " 字节): ";
            for (size_t j = 0; j < packet.size(); j++) {
                printf("%02X ", packet[j]);
            }
            std::cout << std::endl;
            
            ssize_t sent = sendto(sock, packet.data(), packet.size(), 0, 
                                 (struct sockaddr*)&addr, sizeof(addr));
            
            if (sent < 0) {
                std::cerr << "发送失败: " << strerror(errno) << std::endl;
                continue;
            }

            std::cout << "发送成功，等待响应..." << std::endl;

            // 尝试接收响应
            char buffer[1024];
            ssize_t received = recvfrom(sock, buffer, sizeof(buffer), 0, nullptr, nullptr);
            
            if (received > 0) {
                std::cout << "✓ 收到响应！长度: " << received << " 字节" << std::endl;
                std::cout << "响应数据: ";
                for (int k = 0; k < received && k < 64; k++) {
                    printf("%02X ", (unsigned char)buffer[k]);
                    if ((k + 1) % 16 == 0) std::cout << std::endl << "           ";
                }
                std::cout << std::endl;
                
                // 尝试解析响应
                if (received >= 12) {
                    uint16_t respCmd = ((unsigned char)buffer[8] << 8) | (unsigned char)buffer[9];
                    std::cout << "响应命令: 0x" << std::hex << respCmd << std::dec << std::endl;
                    
                    if (respCmd == 0x8a05) {
                        std::cout << "✓ 收到正确的Monitor响应" << std::endl;
                        
                        // 解析Monitor数据
                        if (received > 12) {
                            const unsigned char* data = (const unsigned char*)buffer + 12;
                            int dataSize = received - 12;
                            
                            std::cout << "数据部分 (" << dataSize << " 字节): ";
                            for (int m = 0; m < dataSize && m < 32; m++) {
                                printf("%02X ", data[m]);
                            }
                            std::cout << std::endl;
                            
                            if (dataSize >= 4) {
                                int stepSeconds = data[0] | (data[1] << 8);
                                int stepCountdown = data[2] | (data[3] << 8);
                                std::cout << "步长时间: " << stepSeconds << "s" << std::endl;
                                std::cout << "倒计时: " << stepCountdown << "s" << std::endl;
                                
                                if (dataSize >= 6) {
                                    int phaseIndex = data[4] | (data[5] << 8);
                                    std::cout << "相位索引: " << phaseIndex << std::endl;
                                }
                            }
                        }
                    }
                } else if (received >= 4) {
                    // 尝试简单格式解析
                    uint16_t cmd = ((unsigned char)buffer[0] << 8) | (unsigned char)buffer[1];
                    std::cout << "简单格式命令: 0x" << std::hex << cmd << std::dec << std::endl;
                }
                
                close(sock);
                return true;
            } else {
                std::cout << "无响应 (errno: " << errno << ": " << strerror(errno) << ")" << std::endl;
            }
            
            // 短暂等待再尝试下一个格式
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        close(sock);
        
        std::cout << "\n所有格式都无响应，可能的原因:" << std::endl;
        std::cout << "1. 信号机需要特定的源地址或序列号" << std::endl;
        std::cout << "2. 需要先进行握手或认证" << std::endl;
        std::cout << "3. 协议版本不匹配" << std::endl;
        std::cout << "4. 信号机处于特殊状态" << std::endl;
        
        return false;
        
    } catch (const std::exception& e) {
        std::cerr << "测试连接失败: " << e.what() << std::endl;
        return false;
    }
}

// 添加端口扫描功能
bool scanPorts() {
    try {
        if (!g_signalCtl) {
            std::cerr << "请先加载配置" << std::endl;
            return false;
        }

        std::cout << "扫描 " << g_signalCtl->hostIP << " 上的常见端口..." << std::endl;
        
        // 常见的Chainzone端口和服务
        std::vector<std::pair<int, std::string>> ports = {
            {80, "HTTP"},
            {53, "DNS"},
            {123, "NTP"},
            {161, "SNMP"},
            {502, "Modbus"},
            {8000, "HTTP-alt"},
            {8080, "HTTP-proxy"},
            {9520, "Chainzone专用"},
            {10000, "Chainzone管理"},
            {10001, "Chainzone数据1"},
            {10002, "Chainzone数据2"}
        };
        
        for (const auto& portPair : ports) {
            int port = portPair.first;
            const std::string& service = portPair.second;
            
            int sock = socket(AF_INET, SOCK_STREAM, 0);
            if (sock < 0) {
                std::cerr << "创建socket失败: " << strerror(errno) << std::endl;
                continue;
            }
            
            struct sockaddr_in addr;
            memset(&addr, 0, sizeof(addr));
            addr.sin_family = AF_INET;
            addr.sin_port = htons(port);
            inet_pton(AF_INET, g_signalCtl->hostIP.c_str(), &addr.sin_addr);
            
            // 设置连接超时
            struct timeval tv;
            tv.tv_sec = 1;
            tv.tv_usec = 0;
            setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv));
            setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &tv, sizeof(tv));
            
            std::cout << "正在测试端口 " << port << " (" << service << ")... ";
            
            int result = connect(sock, (struct sockaddr*)&addr, sizeof(addr));
            
            if (result == 0) {
                std::cout << "开放" << std::endl;
                
                // 如果是Chainzone专用端口，尝试发送协议测试包
                if (port == 9520) {
                    std::vector<uint8_t> packet;
                    packet.push_back(0x01);  // 监控命令低字节
                    packet.push_back(0x00);  // 监控命令高字节
                    packet.push_back(0x00);  // 数据长度低字节 (0)
                    packet.push_back(0x00);  // 数据长度高字节 (0)
                    
                    ssize_t sent = send(sock, packet.data(), packet.size(), 0);
                    if (sent > 0) {
                        char buffer[1024];
                        ssize_t received = recv(sock, buffer, sizeof(buffer), 0);
                        if (received > 0) {
                            std::cout << "  ✓ 收到协议响应，长度: " << received << " 字节" << std::endl;
                            
                            // 检查响应是否符合Chainzone协议格式
                            if (received >= 4) {
                                uint16_t responseCmd = *(uint16_t*)buffer;
                                uint16_t responseDataLen = *(uint16_t*)(buffer + 2);
                                
                                std::cout << "  响应命令: 0x" << std::hex << ntohs(responseCmd) << std::dec << std::endl;
                                std::cout << "  数据长度: " << ntohs(responseDataLen) << " 字节" << std::endl;
                                
                                if (ntohs(responseCmd) == 0x0100) {  // 假设0x0100是监控响应命令
                                    std::cout << "  ✓ 收到有效的Chainzone协议响应" << std::endl;
                                } else {
                                    std::cout << "  ✗ 收到意外的响应类型，可能是错误的协议版本" << std::endl;
                                }
                            }
                        }
                    }
                }
            } else {
                std::cout << "关闭" << std::endl;
            }
            
            close(sock);
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "端口扫描失败: " << e.what() << std::endl;
        return false;
    }
}

// 添加网络抓包分析命令
bool analyzeNetwork() {
    if (!g_signalCtl) {
        std::cerr << "请先加载配置" << std::endl;
        return false;
    }
    
    std::cout << "网络分析建议:" << std::endl;
    std::cout << "1. 使用tcpdump抓包对比:" << std::endl;
    std::cout << "   sudo tcpdump -i any -X host " << g_signalCtl->hostIP << " and port " << g_signalCtl->hostPort << std::endl;
    std::cout << std::endl;
    std::cout << "2. 先运行TypeScript版本:" << std::endl;
    std::cout << "   node index.js jet-monitor --address \"" << g_signalCtl->hostIP << "\"" << std::endl;
    std::cout << std::endl;
    std::cout << "3. 然后运行C++版本的test命令，对比数据包格式" << std::endl;
    std::cout << std::endl;
    std::cout << "4. 检查防火墙设置:" << std::endl;
    std::cout << "   sudo iptables -L | grep " << g_signalCtl->hostPort << std::endl;
    std::cout << std::endl;
    
    return true;
}

// 测试UDP协议实现 - 发送DNS请求验证
bool testUdpProtocol() {
    try {
        std::cout << "测试UDP协议实现..." << std::endl;
        
        // 测试1: 发送DNS查询到*******
        std::cout << "\n1. 测试DNS查询到*******..." << std::endl;
        
        int sock = socket(AF_INET, SOCK_DGRAM, 0);
        if (sock < 0) {
            std::cerr << "创建socket失败: " << strerror(errno) << std::endl;
            return false;
        }

        struct sockaddr_in dns_addr;
        memset(&dns_addr, 0, sizeof(dns_addr));
        dns_addr.sin_family = AF_INET;
        dns_addr.sin_port = htons(53);  // DNS端口
        inet_pton(AF_INET, "*******", &dns_addr.sin_addr);

        // 设置超时
        struct timeval tv;
        tv.tv_sec = 3;
        tv.tv_usec = 0;
        setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv));

        // 构造DNS查询包 (查询google.com的A记录)
        std::vector<uint8_t> dns_query = {
            0x12, 0x34,  // Transaction ID
            0x01, 0x00,  // Flags: standard query
            0x00, 0x01,  // Questions: 1
            0x00, 0x00,  // Answer RRs: 0
            0x00, 0x00,  // Authority RRs: 0
            0x00, 0x00,  // Additional RRs: 0
            // Query: google.com
            0x06, 'g', 'o', 'o', 'g', 'l', 'e',
            0x03, 'c', 'o', 'm',
            0x00,        // End of name
            0x00, 0x01,  // Type: A
            0x00, 0x01   // Class: IN
        };

        std::cout << "发送DNS查询包..." << std::endl;
        ssize_t sent = sendto(sock, dns_query.data(), dns_query.size(), 0,
                             (struct sockaddr*)&dns_addr, sizeof(dns_addr));
        
        if (sent < 0) {
            std::cerr << "DNS查询发送失败: " << strerror(errno) << std::endl;
            close(sock);
            return false;
        }

        std::cout << "DNS查询发送成功 (" << sent << " 字节)，等待响应..." << std::endl;

        char buffer[512];
        ssize_t received = recvfrom(sock, buffer, sizeof(buffer), 0, nullptr, nullptr);
        
        if (received > 0) {
            std::cout << "✓ 收到DNS响应，长度: " << received << " 字节" << std::endl;
            std::cout << "UDP协议工作正常！" << std::endl;
        } else {
            std::cout << "✗ 未收到DNS响应 (errno: " << errno << ": " << strerror(errno) << ")" << std::endl;
            std::cout << "可能是网络问题或防火墙阻止" << std::endl;
        }
        
        close(sock);
        
        // 测试2: 测试到信号机的连接，使用不同的源端口
        if (!g_signalCtl) {
            std::cout << "\n跳过信号机测试 - 未加载配置" << std::endl;
            return received > 0;
        }
        
        std::cout << "\n2. 测试到信号机的UDP连接..." << std::endl;
        
        int sock2 = socket(AF_INET, SOCK_DGRAM, 0);
        if (sock2 < 0) {
            std::cerr << "创建socket2失败: " << strerror(errno) << std::endl;
            return received > 0;
        }
        
        // 绑定到特定端口
        struct sockaddr_in local_addr;
        memset(&local_addr, 0, sizeof(local_addr));
        local_addr.sin_family = AF_INET;
        local_addr.sin_addr.s_addr = INADDR_ANY;
        local_addr.sin_port = htons(12345);  // 使用固定源端口
        
        if (bind(sock2, (struct sockaddr*)&local_addr, sizeof(local_addr)) < 0) {
            std::cout << "绑定端口12345失败，使用随机端口: " << strerror(errno) << std::endl;
        } else {
            std::cout << "绑定到源端口12345成功" << std::endl;
        }
        
        struct sockaddr_in signal_addr;
        memset(&signal_addr, 0, sizeof(signal_addr));
        signal_addr.sin_family = AF_INET;
        signal_addr.sin_port = htons(g_signalCtl->hostPort);
        inet_pton(AF_INET, g_signalCtl->hostIP.c_str(), &signal_addr.sin_addr);
        
        setsockopt(sock2, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv));
        
        // 构造测试数据包
        std::vector<uint8_t> test_packet = {
            0x0C, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x0A, 0x05, 0x00, 0x00
        };
        
        std::cout << "发送测试包到 " << g_signalCtl->hostIP << ":" << g_signalCtl->hostPort << std::endl;
        sent = sendto(sock2, test_packet.data(), test_packet.size(), 0,
                     (struct sockaddr*)&signal_addr, sizeof(signal_addr));
        
        if (sent > 0) {
            std::cout << "发送成功 (" << sent << " 字节)，等待响应..." << std::endl;
            received = recvfrom(sock2, buffer, sizeof(buffer), 0, nullptr, nullptr);
            
            if (received > 0) {
                std::cout << "✓ 收到信号机响应！长度: " << received << " 字节" << std::endl;
                std::cout << "响应数据: ";
                for (int i = 0; i < received && i < 32; i++) {
                    printf("%02X ", (unsigned char)buffer[i]);
                }
                std::cout << std::endl;
            } else {
                std::cout << "✗ 未收到信号机响应 (errno: " << errno << ": " << strerror(errno) << ")" << std::endl;
                std::cout << "这表明UDP发送成功，但信号机未响应" << std::endl;
            }
        } else {
            std::cout << "✗ 发送到信号机失败: " << strerror(errno) << std::endl;
        }
        
        close(sock2);
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "UDP协议测试失败: " << e.what() << std::endl;
        return false;
    }
}

// 主函数
int main(int argc, char* argv[]) {
    // 设置信号处理
    std::signal(SIGINT, signalHandler);
    std::signal(SIGTERM, signalHandler);

    std::cout << "Chainzone信号机接口测试工具" << std::endl;
    std::cout << "输入 'help' 获取帮助信息" << std::endl;

    // 命令映射
    std::map<std::string, std::function<bool(const std::vector<std::string>&)>> commands;

    commands["help"] = [](const std::vector<std::string>& args) {
        printHelp();
        return true;
    };

    commands["load"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: load <config_file>" << std::endl;
            return false;
        }
        return loadConfig(args[1]);
    };

    commands["connect"] = [](const std::vector<std::string>& args) {
        return connect();
    };

    commands["disconnect"] = [](const std::vector<std::string>& args) {
        return disconnect();
    };

    commands["status"] = [](const std::vector<std::string>& args) {
        return queryStatus();
    };

    commands["phase"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: phase <number>" << std::endl;
            return false;
        }
        try {
            int phase = std::stoi(args[1]);
            return setPhase(phase);
        } catch (const std::exception& e) {
            std::cerr << "无效的相位值: " << args[1] << std::endl;
            return false;
        }
    };

    commands["reset"] = [](const std::vector<std::string>& args) {
        return resetSignal();
    };

    commands["reconnect"] = [](const std::vector<std::string>& args) {
        int seconds = 3; // 默认等待3秒
        if (args.size() >= 2) {
            try {
                seconds = std::stoi(args[1]);
            } catch (const std::exception& e) {
                std::cerr << "无效的等待时间: " << args[1] << "，使用默认值3秒" << std::endl;
            }
        }
        return reconnectSignal(seconds);
    };

    commands["mode"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: mode <0|1|2|3>" << std::endl;
            return false;
        }
        try {
            int mode = std::stoi(args[1]);
            if (mode != 0 && mode != 1 && mode != 2 && mode != 3) {
                std::cerr << "无效的模式值: " << mode << "，必须是0或1" << std::endl;
                return false;
            }
            return setControlMode(mode);
        } catch (const std::exception& e) {
            std::cerr << "无效的模式值: " << args[1] << std::endl;
            return false;
        }
    };

    commands["monitor"] = [](const std::vector<std::string>& args) {
        int interval = 5; // 默认5秒
        if (args.size() >= 2) {
            try {
                interval = std::stoi(args[1]);
                if (interval < 1) {
                    std::cerr << "间隔时间太短，设置为1秒" << std::endl;
                    interval = 1;
                }
            } catch (const std::exception& e) {
                std::cerr << "无效的间隔时间: " << args[1] << "，使用默认值5秒" << std::endl;
            }
        }
        return monitorStatus(interval);
    };

    commands["synctime"] = [](const std::vector<std::string>& args) {
        return syncTime();
    };

    commands["exit"] = [](const std::vector<std::string>& args) {
        g_running = false;
        return true;
    };

    commands["test"] = [](const std::vector<std::string>& args) {
        return testConnection();
    };

    commands["scan"] = [](const std::vector<std::string>& args) {
        return scanPorts();
    };

    commands["analyze"] = [](const std::vector<std::string>& args) {
        return analyzeNetwork();
    };

    commands["udptest"] = [](const std::vector<std::string>& args) {
        return testUdpProtocol();
    };
    
    // 添加帮助信息
    commands["help"] = [](const std::vector<std::string>& args) {
        printHelp();
        return true;
    };

    // 如果有命令行参数，尝试加载配置文件
    if (argc > 1) {
        std::cout << "尝试加载配置文件: " << argv[1] << std::endl;
        loadConfig(argv[1]);
    } else {
        // 默认加载当前目录下的配置文件
        const std::vector<std::string> defaultConfigFiles = {
            "chainzone_signal_config.json",
            "config/chainzone_signal_config.json",
            "../config/chainzone_signal_config.json"
        };

        bool configLoaded = false;
        for (const auto& configFile : defaultConfigFiles) {
            std::ifstream f(configFile);
            if (f.good()) {
                std::cout << "自动加载配置文件: " << configFile << std::endl;
                configLoaded = loadConfig(configFile);
                if (configLoaded) break;
            }
        }

        if (!configLoaded) {
            std::cout << "未找到默认配置文件，请使用 'load <config_file>' 命令加载配置文件" << std::endl;
        } else {
            // 如果成功加载配置，尝试自动连接
            std::cout << "尝试自动连接到信号机..." << std::endl;
            if (connect()) {
                std::cout << "自动连接成功，请使用 'status' 命令查询状态" << std::endl;
            } else {
                std::cout << "自动连接失败，请使用 'connect' 命令手动连接" << std::endl;
            }
        }
    }

    // 主循环
    while (g_running) {
        std::cout << "> ";
        std::string line;
        std::getline(std::cin, line);

        if (line.empty()) {
            continue;
        }

        // 解析命令
        std::istringstream iss(line);
        std::vector<std::string> args;
        std::string arg;
        while (iss >> arg) {
            args.push_back(arg);
        }

        if (args.empty()) {
            continue;
        }

        std::string cmd = args[0];
        auto it = commands.find(cmd);
        if (it != commands.end()) {
            it->second(args);
        } else {
            std::cerr << "未知命令: " << cmd << std::endl;
            std::cout << "输入 'help' 获取帮助信息" << std::endl;
        }
    }

    // 清理资源
    if (g_device) {
        disconnect();
    }

    std::cout << "程序退出" << std::endl;
    return 0;
}