//
// HiSense信号机接口测试工具
//

#include "components/signal_controllers/SignalDevices/SignalDeviceHKOPENAPI.hpp"
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <fstream>
#include <sstream>
#include <vector>
#include <map>
#include <functional>
#include <memory>
#include <csignal>
#include <atomic>

// 全局变量
std::atomic<bool> g_running(true);
std::shared_ptr<SignalDeviceHKOPENAPI> g_device;
std::shared_ptr<SignalCtl> g_signalCtl;


// 信号处理函数
void signalHandler(int signal) {
    std::cout << "Received signal " << signal << ", exiting..." << std::endl;
    g_running = false;
}

// 帮助信息
void printHelp() {
    std::cout << "\nHK信号机接口测试工具\n" << std::endl;
    std::cout << "命令列表：" << std::endl;
    std::cout << "  help                - 显示帮助信息" << std::endl;
    std::cout << "  load <config_file>  - 从文件加载配置" << std::endl;
    std::cout << "  connect             - 连接到信号机" << std::endl;
    std::cout << "  disconnect          - 断开与信号机的连接" << std::endl;
    std::cout << "  status              - 查询信号机状态" << std::endl;
    std::cout << "  phase <number>      - 设置相位" << std::endl;
    std::cout << "  reset               - 复位信号机" << std::endl;
    std::cout << "  reconnect <seconds> - 重连信号机，等待指定秒数" << std::endl;
    std::cout << "  mode <0|1>          - 设置控制模式（0=自动，1=手动）" << std::endl;
    std::cout << "  monitor <seconds>   - 监控信号机状态，每隔指定秒数查询一次" << std::endl;
    std::cout << "  exit                - 退出程序" << std::endl;
    std::cout << std::endl;
}

// 从文件加载配置
bool loadConfig(const std::string& filename) {
    try {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cerr << "无法打开配置文件: " << filename << std::endl;
            return false;
        }

        std::stringstream buffer;
        buffer << file.rdbuf();
        std::string config = buffer.str();

        g_signalCtl = std::make_shared<SignalCtl>();
        g_signalCtl->fromJson(config);

        std::cout << "配置加载成功" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "加载配置失败: " << e.what() << std::endl;
        return false;
    }
}

// 连接到信号机
bool connect() {
    try {
        if (!g_signalCtl) {
            std::cerr << "请先加载配置" << std::endl;
            return false;
        }

        g_device = std::make_shared<SignalDeviceHKOPENAPI>(*g_signalCtl);
        std::cout << "信号机连接成功" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "连接信号机失败: " << e.what() << std::endl;
        return false;
    }
}

// 断开与信号机的连接
bool disconnect() {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        g_device.reset();
        std::cout << "已断开与信号机的连接" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "断开连接失败: " << e.what() << std::endl;
        return false;
    }
}

// 查询信号机状态
bool queryStatus() {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        SignalEnvState state = g_device->getSignalState();
        state.updatePhaseInfo(*g_signalCtl);
        std::string ses_str = state.toJson().dump();
        std::cout << "信号机状态：" << std::endl;
        std::cout << "  SignalEnvState String: " << ses_str << std::endl;
        std::cout << "  当前相位: " << state.currentPhase << std::endl;
        std::cout << "  相位时间: " << state.phaseTime << " 秒" << std::endl;
        std::cout << "  控制状态: " << (state.signalCtlStatus ? "可控" : "不可控") << std::endl;
        std::cout << "  当前计划: " << state.currentPlan << std::endl;
        std::cout << "  设备状态: " << ISignalDevice::toString(g_device->getCurDeviceState()) << std::endl;

        return true;
    } catch (const std::exception& e) {
        std::cerr << "查询状态失败: " << e.what() << std::endl;
        return false;
    }
}

// 设置相位
bool setPhase(int phase) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "设置相位为 " << phase << std::endl;
        g_device->controlSignal(phase);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "设置相位失败: " << e.what() << std::endl;
        return false;
    }
}

// 复位信号机
bool resetSignal() {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "复位信号机" << std::endl;
        bool result = g_device->reset();
        std::cout << "复位结果: " << (result ? "成功" : "失败") << std::endl;
        return result;
    } catch (const std::exception& e) {
        std::cerr << "复位信号机失败: " << e.what() << std::endl;
        return false;
    }
}

// 重连信号机
bool reconnectSignal(int seconds) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "重连信号机，等待 " << seconds << " 秒" << std::endl;
        bool result = g_device->reconnect(seconds);
        std::cout << "重连结果: " << (result ? "成功" : "失败") << std::endl;
        return result;
    } catch (const std::exception& e) {
        std::cerr << "重连信号机失败: " << e.what() << std::endl;
        return false;
    }
}

// 设置控制模式
bool setControlMode(int mode) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "设置控制模式为 " << (mode == 0 ? "自动" : "手动") << std::endl;
        bool result = g_device->setControlMode(mode);
        std::cout << "设置结果: " << (result ? "成功" : "失败") << std::endl;
        return result;
    } catch (const std::exception& e) {
        std::cerr << "设置控制模式失败: " << e.what() << std::endl;
        return false;
    }
}

// 监控信号机状态
bool monitorStatus(int interval) {
    try {
        if (!g_device) {
            std::cerr << "未连接到信号机" << std::endl;
            return false;
        }

        std::cout << "开始监控信号机状态，每 " << interval << " 秒查询一次" << std::endl;
        std::cout << "按Ctrl+C停止监控" << std::endl;

        while (g_running) {
            queryStatus();
            std::cout << "-----------------------------------" << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(interval));
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "监控信号机状态失败: " << e.what() << std::endl;
        return false;
    }
}

// 主函数
int main(int argc, char* argv[]) {
    // 设置信号处理
    std::signal(SIGINT, signalHandler);
    std::signal(SIGTERM, signalHandler);

    std::cout << "HiSense信号机接口测试工具" << std::endl;
    std::cout << "输入 'help' 获取帮助信息" << std::endl;

    // 命令映射
    std::map<std::string, std::function<bool(const std::vector<std::string>&)>> commands;

    commands["help"] = [](const std::vector<std::string>& args) {
        printHelp();
        return true;
    };

    commands["load"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: load <config_file>" << std::endl;
            return false;
        }
        return loadConfig(args[1]);
    };

    commands["connect"] = [](const std::vector<std::string>& args) {
        return connect();
    };

    commands["disconnect"] = [](const std::vector<std::string>& args) {
        return disconnect();
    };

    commands["status"] = [](const std::vector<std::string>& args) {
        return queryStatus();
    };

    commands["phase"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: phase <number>" << std::endl;
            return false;
        }
        try {
            int phase = std::stoi(args[1]);
            return setPhase(phase);
        } catch (const std::exception& e) {
            std::cerr << "无效的相位值: " << args[1] << std::endl;
            return false;
        }
    };

    commands["reset"] = [](const std::vector<std::string>& args) {
        return resetSignal();
    };

    commands["reconnect"] = [](const std::vector<std::string>& args) {
        int seconds = 3; // 默认等待3秒
        if (args.size() >= 2) {
            try {
                seconds = std::stoi(args[1]);
            } catch (const std::exception& e) {
                std::cerr << "无效的等待时间: " << args[1] << "，使用默认值3秒" << std::endl;
            }
        }
        return reconnectSignal(seconds);
    };

    commands["mode"] = [](const std::vector<std::string>& args) {
        if (args.size() < 2) {
            std::cerr << "用法: mode <0|1>" << std::endl;
            return false;
        }
        try {
            int mode = std::stoi(args[1]);
            if (mode != 0 && mode != 1) {
                std::cerr << "无效的模式值: " << mode << "，必须是0或1" << std::endl;
                return false;
            }
            return setControlMode(mode);
        } catch (const std::exception& e) {
            std::cerr << "无效的模式值: " << args[1] << std::endl;
            return false;
        }
    };

    commands["monitor"] = [](const std::vector<std::string>& args) {
        int interval = 5; // 默认5秒
        if (args.size() >= 2) {
            try {
                interval = std::stoi(args[1]);
                if (interval < 1) {
                    std::cerr << "间隔时间太短，设置为1秒" << std::endl;
                    interval = 1;
                }
            } catch (const std::exception& e) {
                std::cerr << "无效的间隔时间: " << args[1] << "，使用默认值5秒" << std::endl;
            }
        }
        return monitorStatus(interval);
    };

    commands["exit"] = [](const std::vector<std::string>& args) {
        g_running = false;
        return true;
    };

    // 如果有命令行参数，尝试加载配置文件
    if (argc > 1) {
        std::cout << "尝试加载配置文件: " << argv[1] << std::endl;
        loadConfig(argv[1]);
    } else {
        // 默认加载当前目录下的配置文件
        const std::vector<std::string> defaultConfigFiles = {
            "hk_signal_config.json",
            "config/hk_signal_config.json",
            "../config/hk_signal_config.json"
        };

        bool configLoaded = false;
        for (const auto& configFile : defaultConfigFiles) {
            std::ifstream f(configFile);
            if (f.good()) {
                std::cout << "自动加载配置文件: " << configFile << std::endl;
                configLoaded = loadConfig(configFile);
                if (configLoaded) break;
            }
        }

        if (!configLoaded) {
            std::cout << "未找到默认配置文件，请使用 'load <config_file>' 命令加载配置文件" << std::endl;
        } else {
            // 如果成功加载配置，尝试自动连接
            std::cout << "尝试自动连接到信号机..." << std::endl;
            if (connect()) {
                std::cout << "自动连接成功，请使用 'status' 命令查询状态" << std::endl;
            } else {
                std::cout << "自动连接失败，请使用 'connect' 命令手动连接" << std::endl;
            }
        }
    }

    // 主循环
    while (g_running) {
        std::cout << "> ";
        std::string line;
        std::getline(std::cin, line);

        if (line.empty()) {
            continue;
        }

        // 解析命令
        std::istringstream iss(line);
        std::vector<std::string> args;
        std::string arg;
        while (iss >> arg) {
            args.push_back(arg);
        }

        if (args.empty()) {
            continue;
        }

        std::string cmd = args[0];
        auto it = commands.find(cmd);
        if (it != commands.end()) {
            it->second(args);
        } else {
            std::cerr << "未知命令: " << cmd << std::endl;
            std::cout << "输入 'help' 获取帮助信息" << std::endl;
        }
    }

    // 清理资源
    if (g_device) {
        disconnect();
    }

    std::cout << "程序退出" << std::endl;
    return 0;
}
