# toolchain_linux.cmake

# 设置交叉编译的编译器路径
set(CMAKE_C_COMPILER /usr/bin/aarch64-linux-gnu-gcc)
set(CMAKE_CXX_COMPILER /usr/bin/aarch64-linux-gnu-g++)

# 设置头文件和库文件的搜索路径
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR aarch64)

# 设置CMake的系统根目录，用于交叉编译
set(CMAKE_SYSROOT "/home/<USER>/Work/dev_sdk/Ubuntu_rootfs/rootfs_dst/")

# 设置CMake的系统头文件和库的搜索路径
set(CMAKE_FIND_ROOT_PATH ${CMAKE_SYSROOT})

# 设置CMake只在系统根目录中查找库和头文件
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)