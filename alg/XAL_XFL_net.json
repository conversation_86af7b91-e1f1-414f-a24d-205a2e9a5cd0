{"lane_to_phase": {"XAL_XFL_N_0": "NS", "XAL_XFL_N_1": "NS_NW", "XAL_XFL_S_1": "SN", "XAL_XFL_S_0": "SN_SW", "XAL_XFL_W_0": "WN"}, "neighbour_to_phase": {}, "cur_inter_id": "XAL_XFL", "changeTime": {"SN_SW_NS": 20, "WN": 17, "NS": 20, "SN_SW": 20}, "maxKeepTime": 80, "maxKeepNum": 1.5, "delayTime": 1, "advancedParam": 1.6, "debug": false, "minRunningSpeed": 10, "phases": ["SN_SW_NS", "WN"], "solution_to_phases": {"1": ["SN_SW_NS", "WN"], "2": ["SN_SW_NS", "WN"], "3": ["WN", "NS", "SN_SW"], "4": ["SN_SW_NS", "WN"], "5": ["SN_SW_NS", "WN"], "6": ["WN", "NS", "SN_SW"]}, "phase_index_diff": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0, "6": 1}, "overflow_phase_to_road": {}, "all_roadnetLightPhase": ["SN_SW_NS", "WN", "NS", "SN_SW"], "overflow_phase_start": -1, "overflow_phase_index_diff": -1, "all_avaliable_phases": ["SN_SW_NS", "WN", "SN_SW", "NS"], "is_cycle_control": false, "anomaly_detect_interval": 10, "person_min_time": 40, "start_anomaly_detect": false, "person_recongnize_plan": 0, "person_factor": 1, "cityflowTest": 0, "algo_version": "v1"}