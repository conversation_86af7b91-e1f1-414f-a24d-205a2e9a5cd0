# SmartTrafficNexus智能交通管理系统

## 概述

本项目实现了一个使用现代C++设计模式和原则的交通管理系统。该系统旨在捕获交通数据，处理数据，并相应地管理交通流和信号。

## 架构

以下是系统的高级架构图：

```
+------------------+
|   SystemManager  |
+--------+---------+
         |
         | 创建和管理
         v
+--------+---------+     +----------------+
| ComponentFactory |---->| ConfigManager  |
+--------+---------+     +----------------+
         |
         | 创建
         v
+--------+----------+    +-----------------+
|   IDataProvider   |<---| CameraRadar     |
| (Subject)         |    +-----------------+
+--------+----------+
         |
         | 通知
         v
+--------+----------+
|    IObserver      |
+--------+----------+
         ^
         |
    +----+----+----+----+
    |         |         |
+---+---+ +---+---+ +---+---+
|  ITM  | | Data  | | Flow  |
|       | |Service| |Control|
+-------+ +-------+ +-------+
    ^         ^         ^
    |         |         |
+---+---+ +---+---+ +---+---+
|Traffic| | Data  | | Signal|
|Manager| |Process| |Control|
+-------+ +-------+ +-------+

+---------------+
|    Logger     |
+---------------+
```

## 组件

1. **SystemManager**: 管理整个系统，初始化组件，并运行主循环。
2. **ComponentFactory**: 创建各种系统组件的实例。
3. **ConfigManager**: 管理系统配置。
4. **IDataProvider**: 数据提供者接口，由CameraRadar实现。
5. **IObserver**: 观察者组件接口，包括：
    - ITM（智能交通管理）
    - DataService（数据服务）
    - FlowControlModule（流量控制模块）
    - SignalMachine（信号机）
6. **Logger**: 处理系统范围的日志记录。

## 设计模式

- **观察者模式**: 用于通知各组件新数据。
- **工厂模式**: 用于创建组件实例。
- **单例模式**: 用于Logger和ConfigManager。

## 主要特性

- 实时交通数据捕获和处理
- 智能交通流量管理
- 自适应信号控制
- 集中化日志系统
- 配置管理

## 配置

系统使用配置文件（`config.ini`）进行各种设置。在运行系统之前，请确保此文件设置正确。

## 日志记录

系统使用集中化的日志记录机制。日志写入`system.log`。

## 错误处理

系统使用自定义异常处理来有效管理和报告错误。

## 未来增强

- 实现更复杂的交通预测算法
- 添加对多个数据源的支持
- 开发实时监控的用户界面