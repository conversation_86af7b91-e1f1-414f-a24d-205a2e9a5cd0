{
    "compilerOptions": {
        "tsBuildInfoFile": "./node_modules/.temp/.tsbuildinfo", /* Specify the path to .tsbuildinfo incremental compilation file. */
        "target": "esnext", /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */
        "module": "esnext", /* Specify what module code is generated. */
        "rootDir": "./src", /* Specify the root folder within your source files. */
        "moduleResolution": "node", /* Specify how TypeScript looks up a file from a given module specifier. */
        "outDir": "./dist", /* Specify an output folder for all emitted files. */
        "verbatimModuleSyntax": true, /* Do not transform or elide any imports or exports not marked as type-only, ensuring they are written in the output file's format based on the 'module' setting. */
        "esModuleInterop": true, /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */
        "forceConsistentCasingInFileNames": true, /* Ensure that casing is correct in imports. */
        "strictNullChecks": true,                         /* When type checking, take into account 'null' and 'undefined'. */
        "skipLibCheck": true /* Skip type checking all .d.ts files. */
    },
    "exclude": [
        "**/*.spec.ts",
        "**/*.spec.js",
        "**/*.test.ts",
        "**/*.test.js",
    ]
}
