# 背景介绍

青松科技的主营业务是 LED 显示屏，其信号机采用了与显示屏相似的硬件架构，所以通讯协议分成了两部分：

+ 《青松-JetFileII 通讯协议Ver2.17.20160718.pdf》

  此文档是基础说明，大部分内容是显示屏和信号机通用的通讯协议。也针对信号机的特点加入了 *智能交通控制机通讯专用协议(0x0A)* 章节。

+ 《青松-JKC交通控制数据结构Ver2.4.pdf》

  此文档是针对信号机相关数据结构的补充说明，用于解析从信号机获取到的各类数据，或构建相关数据写入到信号机中。

# 示例代码

本示例代码项目采用 `Typescript` 编写，以命令行方式运行。

## 目录结构

```
.
├── assets          // 附件目录
│   └── ...
├── cli             // 入口脚本
├── dist            // 编译输出目录
│   └── ...
├── node_modules
│   └── ...
├── runtime         // 程序运行的输出文件
│   └── ...
├── src             // 源代码
│   ├── command     // 命令列表
│   ├── index.ts    // 程序入口
│   ├── packing     // 数据包打包/解包
│   ├── protocol    // 通讯协议的实现
│   ├── serializing // 序列化/反序列化
│   ├── stream      // 字节流读写
│   ├── transport   // 网络传输
│   ├── utils       // 一些工具类
│   └── ...
└── ...
```

## 如何编译

这是一个简单的 Node.js + Typescript 工程，按 npm 工具链编译即可
```
cd <project-dir>

pnpm i # npm/yarn 也可以，看个人习惯
pnpm build
```

## 如何运行

+ 调试运行
  ```
  pnpm cli --help

  NODE_DEBUG="chainzone*" pnpm cli jet-date
  ```

+ 编译后运行
  ```
  ./cli --help

  index.js <command>

  Commands:
    index.js jet <command>              测试简单无参数（arg/data）指令
    index.js jet-schedule               读取特殊日期信息
    index.js jet-monitor                读取信号机实时状态
    index.js jet-identity               读取信号机ID信息
    index.js jet-file <path>            从信号机下载文件
    index.js jet-discover               从局域网搜索信号机
    index.js jet-dir [target]           读取信号机目录下的文集/目录列表
    index.js jet-date                   读取信号机系统时间
    index.js jet-date/send              将本计算机系统时间同步到信号机
    index.js jet-control [type] [time]  手动控制，支持跳步/跳相，黄闪/全红等操作
    index.js jet-backup                 从信号机备份所有文件
    index.js hello

  Options:
    --help     Show help                                                 [boolean]
    --version  Show version number                                       [boolean]
    --json                                                               [boolean]
  ```

# 命令示例

+ `./cli jet-discover --address=************* --json`
  ```
  {
      "url": "udp://*************:9520",
      "programVersion": "208D",
      "fpgaVersion": "FFFF",
      "ipAddress": "***********",
      "address": {
          "group": 1,
          "unit": 1
      },
      "rev": null
  }
  ```

+ `./cli jet-dir D: --depth 2`
  ```
  D:/
  ├── SPECIAL/
  │   └── SPECIAL.SPD (281)
  ├── ...
  └── PDPHASE/
      └── 08.PHE (692)
  ```

+ `./cli jet-file D:/PHASE/02.PHE 2>/dev/null | base64`
  ```
  DeKgiyBEOi9QSEFTRS8wMi5QSEUgIDUxJSAgDapVIAABAAIAUwcWIBIZFUcXAp8HAAAAAAAAAAAA
  AAAAIAAAAAAAAAAAAAMAAAA8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAA
  AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
  AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
  ...
  ```

+ `./cli jet-file D:/PHASE/02.PHE -O runtime/`
  ```
  ✔ D:/PHASE/02.PHE

  SAVED:
    .../runtime/02.PHE
  ```


+ `./cli jet-date`
  ```
  ReadDateResponse { value: 2025-07-17T08:44:08.000Z }
  ```

+ `./cli jet-date/send`
  ```
  OK
  ```
