import type { <PERSON>ary<PERSON>eader } from '../stream'
import { bcdDecode } from '../utils'
import { AbstractSerializer, type SerializingOptions } from './serializer'

export class PasswordSerializer extends AbstractSerializer<string> {
    constructor(
        readonly byteCount?: number
    ) {
        super()
    }

    protected internalDeserialize(reader: BinaryReader, to: string | null | undefined, options: SerializingOptions): string {
        const bytes = reader.readBytes(this.byteCount ?? options.byteCount)
        return bytes
            .map(x => bcdDecode(x) as any ?? String.fromCharCode(x))
            .join('')
    }
}

export const passwordSerializer = new PasswordSerializer(6)
