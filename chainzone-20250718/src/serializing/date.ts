import type { Binary<PERSON><PERSON>er, BinaryWriter } from '../stream'
import { bcdDecode, bcdEncode } from '../utils'
import { AbstractSerializer } from './serializer'

export class DateSerializer extends AbstractSerializer<Date | null> {
    protected internalSerialize(writer: BinaryWriter, value: Date | null): void {
        if (value == null) {
            writer.writeBytes(8, 0xff)
        } else {
            const [Y, ...rest] = [
                bcdEncode(value.getFullYear()),
                bcdEncode(value.getMonth() + 1),
                bcdEncode(value.getDate()),
                bcdEncode(value.getHours()),
                bcdEncode(value.getMinutes()),
                bcdEncode(value.getSeconds()),
                bcdEncode(value.getDay() + 1),
            ]

            writer.writeUInt16(Y)
            rest.forEach(x => writer.writeUInt8(x))
        }
    }

    protected internalDeserialize(reader: BinaryReader): Date | null {
        const items = [
            bcdDecode(reader.readUInt16()),
            bcdDecode(reader.readUInt8()),
            bcdDecode(reader.readUInt8()),
            bcdDecode(reader.readUInt8()),
            bcdDecode(reader.readUInt8()),
            bcdDecode(reader.readUInt8()),
            bcdDecode(reader.readUInt8()),
        ]
        if (items.some(x => x == null)) {
            return null
        }

        const [Y, m, d, H, i, s] = items as number[]
        return new Date(Y, m - 1, d, H, i, s)
    }
}

export const dateSerializer = new DateSerializer()
