import type { BinaryReader } from '../stream'
import { AbstractSerializer, type SerializingOptions } from './serializer'

export class VersionSerializer extends AbstractSerializer<string> {
    prepareOptions(options?: SerializingOptions | undefined): SerializingOptions {
        const { byteCount = 2, ...rest } = options ?? {}
        return { ...rest, byteCount }
    }

    protected internalDeserialize(reader: BinaryReader, to: string | null | undefined, options: SerializingOptions): string {
        const bytes = reader.readBytes(options.byteCount)
        return bytes.toString('hex').toUpperCase()
    }
}

export const versionSerializer = new VersionSerializer()
