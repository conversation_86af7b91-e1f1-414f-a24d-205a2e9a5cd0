import type { BinaryReader } from '../stream'
import { hexEncode } from '../utils'
import { AbstractSerializer } from './serializer'

export class MacSerializer extends AbstractSerializer<string> {
    constructor(
        public separator = ':'
    ) {
        super()
    }

    protected internalDeserialize(reader: BinaryReader): string {
        return [...Array(6)]
            .map(() => reader.readUInt8())
            .map(x => hexEncode(x))
            .join(this.separator)
    }
}

export const macSerializer = new MacSerializer()
