import type { Address } from '../packing'
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, BinaryWriter } from '../stream'
import { AbstractSerializer } from './serializer'

export class AddressSerializer extends AbstractSerializer<Address> {
    defaultValue(): Address {
        return {
            group: 0,
            unit: 0,
        }
    }

    protected internalSerialize(writer: Binary<PERSON>rite<PERSON>, value: Address): void {
        const { group, unit } = value
        writer.writeUInt8(group)
        writer.writeUInt8(unit)
    }

    protected internalDeserialize(reader: BinaryReader, to: Address | null | undefined): Address {
        const result = to ?? this.defaultValue()
        result.group = reader.readUInt8()
        result.unit = reader.readUInt8()

        return result
    }
}

export const addressSerializer = new AddressSerializer()
