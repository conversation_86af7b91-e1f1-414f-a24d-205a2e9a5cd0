import type { BinaryReader } from '../stream'
import { AbstractSerializer } from './serializer'

export class IpSerializer extends AbstractSerializer<string> {
    protected internalDeserialize(reader: BinaryReader): string {
        const val = reader.readUInt32()
        return [
            (val >> 24) & 0xff,
            (val >> 16) & 0xff,
            (val >> 8) & 0xff,
            val & 0xff,
        ].join('.')
    }
}

export const ipSerializer = new IpSerializer()
