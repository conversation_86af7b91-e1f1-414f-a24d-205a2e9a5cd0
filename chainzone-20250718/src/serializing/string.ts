import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, BinaryWriter } from '../stream'
import { AbstractSerializer, type SerializingOptions } from './serializer'

export class StringSerializer extends AbstractSerializer<string | null> {
    constructor(
        readonly byteCount?: number
    ) {
        super()
    }

    protected internalSerialize(writer: BinaryWriter, value: string | null, options: SerializingOptions): void {
        const byteCount = this.byteCount ?? options.byteCount
        if (value === null && byteCount != null) {
            writer.writeBytes(byteCount, 0xff)
        } else {
            writer.writeString(value ?? '')
        }
    }

    protected internalDeserialize(reader: BinaryReader, to: string | null | undefined, options: SerializingOptions): string | null {
        let bytes = reader.readBytes(this.byteCount ?? options.byteCount)
        if (bytes[0] === 0xff) {
            return null
        }
        const index = bytes.indexOf(0)
        if (index >= 0) {
            bytes = bytes.subarray(0, index)
        }

        return bytes.toString('utf8')
    }
}

export const stringSerializer = new StringSerializer()
