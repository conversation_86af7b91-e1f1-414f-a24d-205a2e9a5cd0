import { NotImplementedError, NotSupported } from '../error'
import type { Binary<PERSON><PERSON>er, BinaryWriter } from '../stream'
import { SerializingError } from './error'

export interface SerializingOptions {
    byteCount?: number
}

export interface Serializer<T = unknown, O extends SerializingOptions = SerializingOptions> {
    defaultValue(options?: O): T
    serialize(writer: BinaryWriter, value?: T | null, options?: O): void
    deserialize(reader: BinaryReader, to?: T | null, options?: O): T
}

export abstract class AbstractSerializer<T, O extends SerializingOptions = SerializingOptions> implements Serializer<T, O> {
    prepareOptions(options?: O) {
        return options ?? {} as O
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    defaultValue(options: O): T {
        throw new NotSupported()
    }

    protected checkByteCount(stream: BinaryReader | BinaryWriter, preferCount?: number) {
        const { position } = stream
        return () => {
            const byteCount = stream.position - position
            if (preferCount != null) {
                if (byteCount > preferCount) {
                    throw new SerializingError(`Byte count read/wrote overflow: ${byteCount}, expected: ${preferCount}`)
                }

                return preferCount - byteCount
            }

            return 0
        }
    }

    serialize(writer: BinaryWriter, value?: T | null, options?: O): void {
        options = this.prepareOptions(options)

        const val = value ?? this.defaultValue(options)

        const check = this.checkByteCount(writer, options?.byteCount)
        this.internalSerialize(writer, val, options)

        const byteCount = check()
        if (byteCount > 0) {
            writer.writeBytes(byteCount)
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected internalSerialize(writer: BinaryWriter, value: T, options: O) {
        throw new NotImplementedError()
    }

    deserialize(reader: BinaryReader, to?: T | null, options?: O): T {
        options = this.prepareOptions(options)

        const check = this.checkByteCount(reader, options.byteCount)
        const result = this.internalDeserialize(reader, to, options)

        const byteCount = check()
        if (byteCount > 0) {
            reader.skip(byteCount)
        }

        return result
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected internalDeserialize(reader: BinaryReader, to: T | undefined | null, options: O): T {
        throw new NotImplementedError()
    }
}
