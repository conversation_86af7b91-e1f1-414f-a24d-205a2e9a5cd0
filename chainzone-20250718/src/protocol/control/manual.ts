import type { BinaryWriter } from '../../stream'
import { StatusRequestClass } from '../../transport'

export enum ManualType {
    Last = 0,
    Next,
    Pause,
    AllRed,
    YellowBlink,

    Automatic = 0x08,

    Phase0 = 0x09,
    // ...
    // Phase31 = 0x28,
}

/** 手动控制当前相位 */
export class Manual extends StatusRequestClass(0x0a02) {
    timeout = 5             // 默认保持 5 分钟
    sequentialJump = true   // 默认顺序跳转
    crossNumber = 0         // 控制路口号, 始终填 0，具体含义厂家未公开，可能用于一机多岗

    constructor(
        readonly manualType: ManualType
    ) {
        super()
    }

    packArg(writer: BinaryWriter): void {
        writer.writeUInt8(this.manualType)

        let { timeout } = this
        if (timeout <= 0) {
            timeout = 0
            writer.writeUInt8(0)
        } else {
            const minutes = timeout > 0x7f
                ? 0x7f : timeout
            timeout = (1 << 7) & minutes
        }
        writer.writeUInt8(timeout)
        writer.writeUInt8(this.crossNumber)
    }
}
