import type { Address } from '../../packing'
import { addressSerializer } from '../../serializing/address'
import { ipSerializer } from '../../serializing/ip'
import { versionSerializer } from '../../serializing/version'
import type { BinaryReader } from '../../stream'
import { AbstractResponse, RequestClass } from '../../transport'

export class DiscoverResponse extends AbstractResponse {
    programVersion: string
    fpgaVersion: string
    ipAddress: string
    address: Address
    rev: null

    unpackArg(reader: BinaryReader): void {
        this.programVersion = versionSerializer.deserialize(reader)
        this.fpgaVersion = versionSerializer.deserialize(reader)
        this.ipAddress = ipSerializer.deserialize(reader)
        this.address = addressSerializer.deserialize(reader)
        this.rev = reader.skip(2)
    }
}

/** 本指令未在通信协议中公开，以后新版信号机固件中会有调整，仅供参考 */
export class Discover extends RequestClass(0x0301, DiscoverResponse) {
}
