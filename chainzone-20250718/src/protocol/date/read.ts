import { dateSerializer } from '../../serializing/date'
import type { BinaryReader } from '../../stream'
import { AbstractResponse, RequestClass } from '../../transport'

export class ReadDateResponse extends AbstractResponse {
    value: Date

    protected internalUnpack(reader: BinaryReader): void {
        this.value = dateSerializer.deserialize(reader)!
    }
}

/** 读取信号机系统时间 */
export class ReadDate extends RequestClass(0x0501, ReadDateResponse) {
}
