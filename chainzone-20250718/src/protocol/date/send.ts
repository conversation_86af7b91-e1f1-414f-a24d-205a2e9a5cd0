import { dateSerializer } from '../../serializing/date'
import type { BinaryWriter } from '../../stream'
import { StatusRequestClass } from '../../transport'

/** 修改信号机系统时间 */
export class SendDate extends StatusRequestClass(0x0502) {
    constructor(
        readonly value?: Date
    ) {
        super()
    }

    packArg(writer: BinaryWriter): void {
        const val = this.value ?? new Date()
        dateSerializer.serialize(writer, val)
    }
}
