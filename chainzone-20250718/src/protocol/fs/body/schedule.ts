import { NotImplementedError } from '../../../error'
import type { Packable } from '../../../packing'
import type { BinaryReader } from '../../../stream'
import { bcdDecode } from '../../../utils'

export class ScheduleEntry implements Packable {
    pack(): void {
        throw new NotImplementedError()
    }

    year?: number
    month?: number
    date?: number
    weekDay?: number
    rev: null
    periodFile: string

    unpack(reader: BinaryReader): this {
        this.year = bcdDecode(reader.readUInt16()) ?? undefined
        this.month = bcdDecode(reader.readUInt8()) ?? undefined
        this.date = bcdDecode(reader.readUInt8()) ?? undefined
        this.weekDay = bcdDecode(reader.readUInt8()) ?? undefined
        this.rev = reader.skip(1)
        this.periodFile = reader.readString(25)

        return this
    }
}

export const SPECIAL_SPD = 'D:/SPECIAL/SPECIAL.SPD'

/** 特殊日期数据结构 */
export class Schedule implements Packable {
    pack(): void {
        throw new NotImplementedError()
    }

    itemCount: number
    items: ScheduleEntry[]

    unpack(reader: BinaryReader): this {
        let count = reader.readUInt8()
        this.itemCount = count

        const items: ScheduleEntry[] = []
        while (count-- > 0) {
            items.push(new ScheduleEntry().unpack(reader))
        }
        this.items = items

        return this
    }
}
