import { basename, extname } from 'path'

export type NormalizePathOptions = {
    /** 默认显示时采用 Unix 风格，发送时采用 Windows 风格 */
    windowsStyle?: boolean
    /** 接收时去掉末尾的 '\0'，发送时添加 '\0' 到末尾*/
    zeroTailing?: boolean
}

export function normalizePath(path: string, sending: true): string
export function normalizePath(path: string, options?: NormalizePathOptions): string
export function normalizePath(path: string, x?: true | NormalizePathOptions) {
    const options: NormalizePathOptions = x === true
        ? { windowsStyle: true, zeroTailing: true }
        : x ?? {}

    const { windowsStyle, zeroTailing } = options

    let result = path.trim()//.toUpperCase()
    const zeroIndex = result.indexOf('\0')
    if (zeroIndex >= 0) {
        result = result.slice(0, zeroIndex)
    }

    result = result.replace(/[/\\]+/g, '/')

    if (result === '/') return ''

    result = result
        .replace(/^\/([A-Z]):?\//, '$1:/') // `/D/...` => `D:/`
        .replace(/^\/+/, '')
        .replace(/\/+$/, '')

    if (windowsStyle) {
        result = result.replace(/\//g, '\\')
    }

    return zeroTailing
        ? result + '\0' : result
}

export function getBaseName(path: string, ext: string | boolean = false) {
    // 先转换为 Unix 风格
    // 以保证不同平台下 basename 能正确处理目录分隔符
    const thePath = normalizePath(path)
    const suffix = typeof ext === 'string'
        ? ext : (ext ? undefined : extname(thePath))

    return basename(thePath, suffix)
}
