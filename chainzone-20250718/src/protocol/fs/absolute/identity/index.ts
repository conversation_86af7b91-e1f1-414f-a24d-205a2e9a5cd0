import { NotImplementedError } from '../../../../error'
import type { Packable } from '../../../../packing'
import { dateSerializer } from '../../../../serializing/date'
import { macSerializer } from '../../../../serializing/mac'
import { passwordSerializer } from '../../../../serializing/password'
import { stringSerializer } from '../../../../serializing/string'
import type { BinaryReader } from '../../../../stream'

export const ADDRESS_Identity = 0x0105fc00

export class Identity implements Packable {
    /** 以太网物理地址 */
    macAddress: string
    /** 用户密码 */
    userPassword: string
    /** 管理员密码 */
    adminPassword: string
    /** 出厂时间 */
    productionDate: Date | null
    /** 生产串号 */
    productionSn: string | null
    /** 制造商信息 */
    vender: string | null

    pack(): void {
        throw new NotImplementedError()
    }

    unpack(reader: BinaryReader): this {
        this.macAddress = macSerializer.deserialize(reader)
        this.userPassword = passwordSerializer.deserialize(reader)
        this.adminPassword = passwordSerializer.deserialize(reader)
        this.productionDate = dateSerializer.deserialize(reader)
        this.productionSn = stringSerializer.deserialize(reader, null, { byteCount: 16 })
        this.vender = stringSerializer.deserialize(reader, null, { byteCount: 256 })

        return this
    }
}
