import { jetEndian } from '../../../jet'
import type { Packable } from '../../../packing'
import type { BinaryReader, BinaryWriter } from '../../../stream'
import { AbstractResponse, RequestClass } from '../../../transport'

export class AbsoluteResponse extends AbstractResponse {
    data: Buffer

    unpackData(reader: BinaryReader): void {
        this.data = reader.readBytes()
    }

    unpack<T extends Packable>(val: T): T {
        const reader = jetEndian.read(this.data)
        return val.unpack(reader)
    }
}

/** 绝对地址信息读取 */
export class ReadAbsolute extends RequestClass(0x0101, AbsoluteResponse) {
    constructor(
        readonly address: number,
        readonly byteCount = 1024
    ) {
        super()
    }

    packArg(writer: BinaryWriter): void {
        writer.writeUInt32(this.address)
        writer.writeUInt16(this.byteCount)
    }
}
