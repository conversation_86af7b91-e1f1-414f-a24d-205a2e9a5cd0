import type { JetPacket } from '../../../packing'
import type { BinaryReader, BinaryWriter } from '../../../stream'
import { AbstractResponse, RequestClass } from '../../../transport'
import { ProtocolError } from '../../error'
import { normalizePath } from '../utils'
import { FileSystemEntry } from './dir'

const readCountKey = Symbol('readCount')
export class ReadDirResponse extends AbstractResponse {
    readonly root: FileSystemEntry

    constructor(path = '/') {
        super()
        this.root = new FileSystemEntry(path, true)
    }

    totalCount: number
    unpackArg(reader: BinaryReader): void {
        this.totalCount = reader.readUInt16()
    }

    unpackData(reader: BinaryReader): void {
        const ignoreNames = ['.', '..']
        while (!reader.eof()) {
            const item = new FileSystemEntry().unpack(reader)
            this[readCountKey] += 1
            if (!ignoreNames.includes(item.name)) {
                this.root.appendChild(item)
            }
        }
    }

    private [readCountKey] = 0
    get readCount() {
        return this[readCountKey]
    }

    isCompleted() {
        return this.readCount >= this.totalCount
    }
}

export enum DirectoryType {
    // Explicit = 0,
    ROOT = 1,
    FONT,
    TEXT,
    STRING,
    PICTURE,
    ARRAY_PICTURE,
}

/** DIR 指定目录的文件目录项 */
export class ReadDir extends RequestClass(0x070c, ReadDirResponse) {
    /** 读取的目标目录 */
    readonly target: DirectoryType | string

    constructor(
        target: DirectoryType | string
    ) {
        super()
        this.target = typeof target === 'string'
            ? normalizePath(target)
            : target
    }

    /** 分页大小 */
    readSize = 16
    /** 分页偏移 */
    readOffset = 0

    packArg(writer: BinaryWriter, packet: JetPacket): void {
        const { target } = this
        packet.flag = typeof target === 'string' ? 0 : target

        writer.writeUInt16(this.readSize)
        writer.writeUInt16(this.readOffset)
        if (typeof target === 'string') {
            writer.writeString(normalizePath(target, true))
        }
    }

    get displayTarget() {
        const { target } = this
        return typeof target === 'string'
            ? target : `[${DirectoryType[target]}]`
    }

    protected response?: ReadDirResponse
    createResponse(): ReadDirResponse {
        // 始终返回同一个响应对象来保存分页数据
        if (this.response == null) {
            this.response = new ReadDirResponse(this.displayTarget)
        }

        return this.response
    }

    /** 获取下一页的请求对象 */
    next() {
        const { response } = this
        if (response == null) {
            this.readOffset = 0
        } else if (!response.isCompleted()) {
            this.readOffset = response.readCount
        } else {
            return null
        }

        return this
    }

    /** 全部读取完成后获取读取结果 */
    end() {
        const res = this.response
        if (res?.isCompleted()) {
            return res.root
        }

        throw new ProtocolError('Not fully read: ' + this.displayTarget)
    }
}
