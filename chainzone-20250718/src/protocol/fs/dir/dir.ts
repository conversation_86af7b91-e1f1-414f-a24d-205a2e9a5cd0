import { NotSupported } from '../../../error'
import type { Packable } from '../../../packing'
import type { BinaryReader } from '../../../stream'
import { ProtocolError } from '../../error'
import { normalizePath } from '../utils'

export enum FileSystemAttr {
    Directory = 16,
    File = 32,
}

const parentKey = Symbol('parent')

// cSpell: ignore tecth clus
export class FileSystemEntry implements Packable {
    pack(): void {
        throw new NotSupported()
    }

    constructor(name?: string, isDir = false) {
        this.name = name!
        if (isDir) {
            this.attr = FileSystemAttr.Directory
        }
    }

    /** 项目名称 */
    name: string
    /** 项目类型（文件/目录） */
    attr: FileSystemAttr
    rev: null

    crt_time_tecth: number
    crt_time: number
    crt_date: number
    last_acc_time: number
    fst_clus_hi: number
    wrt_time: number
    wrt_date: number
    fst_clus_lo: number
    /** 文件大小，对于目录项，值为 0 */
    file_size: number

    unpack(reader: BinaryReader): this {
        const name = reader.readString(11)
        this.attr = reader.readUInt8()
        this.name = normalizeName(name, this.isDir())

        this.rev = reader.skip(1)

        this.crt_time_tecth = reader.readUInt8()
        this.crt_time = reader.readUInt16()
        this.crt_date = reader.readUInt16()
        this.last_acc_time = reader.readUInt16()
        this.fst_clus_hi = reader.readUInt16()
        this.wrt_time = reader.readUInt16()
        this.wrt_date = reader.readUInt16()
        this.fst_clus_lo = reader.readUInt16()
        this.file_size = reader.readUInt32()

        return this
    }

    isDir() {
        return (this.attr & FileSystemAttr.Directory)
            === FileSystemAttr.Directory
    }

    get displayName() {
        const { name } = this
        return this.isDir() ? name + '/' : name
    }

    private [parentKey]?: FileSystemEntry
    get parent() {
        return this[parentKey]
    }

    children?: FileSystemEntry[]
    appendChild(...items: FileSystemEntry[]) {
        if (!this.isDir()) {
            throw new ProtocolError('Not a directory.')
        }

        const children = this.children ?? []
        items.forEach(x => {
            x[parentKey] = this
            children.push(x)
        })

        this.children = children
        return this
    }

    getPath(): string {
        const { parent, name } = this
        return normalizePath(
            parent != null
                ? [parent.getPath(), name].join('/')
                : name
        )
    }
}

/** 修正返回文件名格式问题 */
function normalizeName(val: string, isDir: boolean) {
    // 'IOTABLE TAB' => 'IOTABLE.TAB'
    val = val
        .trimEnd()
        .replace(/(\S) +/, '$1.')

    if (!isDir && !val.slice(1).includes('.')) {
        // 'KEYTABLETAB' => 'KEYTABLE.TAB'
        val = val.replace(/([A-Z]{3})$/, '.$1')
    }

    return val
}
