import { NotImplementedError } from '../../../error'
import { jetEndian } from '../../../jet'
import type { Packable } from '../../../packing'
import { dateSerializer } from '../../../serializing/date'
import type { BinaryReader } from '../../../stream'
import { sumBytes } from '../../../utils'
import { ProtocolError } from '../../error'

export enum FileType {
    NotSet = 0,

    /** 路口模型文件 */
    Map = 1,
    /** 相位文件 */
    Phase,
    /** 时段文件 */
    Period,
    /** 特殊日期文件 */
    Schedule,
    /** 灯组对照表 */
    IOTable,
    /** 人行按键对照表 */
    PedKeyMap,
    /** 线圈检测对照表 */
    VehLoopMap,
    /** 绿冲突对照表 */
    GreenConflict,
    /** 错误记录 */
    ErrorLog,
    /** 操作记录 */
    OperationLog,
    /** 配置文件 */
    Config,
    /** 温度日志 */
    TemperatureLog,
    /** 行人相位文件 */
    PedPhase,
    /** 车流量记录 */
    VehFlow,
    /** CPU 升级文件 */
    Program,
    // ...
}

export class JetFileHead implements Packable {
    pack(): void {
        throw new NotImplementedError()
    }

    /** 文件标识符 */
    sign: number        // 0x55aa
    /** 头字节数 */
    headSize: number    // 32
    /** 版本 */
    version: number
    /** 文件类型 */
    type: FileType
    /** 和校验（不含文件头） */
    bodySum: number
    /** 文件创建时间 */
    createAt: Date
    /** 数据字节数 */
    bodySize: number
    /** 保留 */
    rev: null

    unpack(reader: BinaryReader): this {
        this.sign = reader.readUInt16()
        this.headSize = reader.readUInt16()
        this.version = reader.readUInt16()
        this.type = reader.readUInt16()
        this.bodySum = reader.readUInt16()
        this.createAt = dateSerializer.deserialize(reader)!
        this.bodySize = reader.readUInt32()
        this.rev = reader.skip(10)

        return this
    }
}

export class JetFile implements Packable {
    static fromBuffer(buffer: Buffer): JetFile
    static fromBuffer<T extends Packable>(buffer: Buffer, body: T): T
    static fromBuffer(buffer: Buffer, body?: Packable) {
        const result = new JetFile()
            .unpack(jetEndian.read(buffer))

        return body == null
            ? result
            : body.unpack(jetEndian.read(result.body))
    }

    constructor(
        readonly strict = true
    ) { }

    pack(): void {
        throw new NotImplementedError()
    }

    /** 文件头 */
    head: JetFileHead
    /** 文件体 */
    body: Buffer

    unpack(reader: BinaryReader): this {
        this.head = new JetFileHead().unpack(reader)
        this.body = reader.readBytes()

        if (this.strict) {
            if (this.body.length !== this.head.bodySize) {
                throw new ProtocolError('Body size mis-matched.')
            }

            const bodySum = sumBytes(this.body, 0xffff)
            if (bodySum !== this.head.bodySum) {
                throw new ProtocolError('Body checkSum mis-matched.')
            }
        }

        return this
    }
}
