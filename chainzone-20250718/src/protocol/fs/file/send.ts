import type { BinaryWriter } from '../../../stream'
import { StatusRequestClass, StatusResponse } from '../../../transport'
import { ProtocolError } from '../../error'
import { normalizePath } from '../utils'

export class SendFile extends StatusRequestClass(0x0208) {
    /** 目标文件路径 */
    readonly path: string
    /** 文件大小 */
    readonly fileSize: number
    /** 分包数量 */
    readonly chunkCount: number

    chunkIndex = 1

    constructor(
        path: string,
        /** 文件内容 */
        readonly buffer: Buffer,
        /** 分包大小 */
        readonly chunkSize = 1024
    ) {
        super()
        this.path = normalizePath(path, true)

        const fileSize = buffer.length
        this.fileSize = fileSize
        this.chunkCount = Math.ceil(fileSize / chunkSize)
    }

    packArg(writer: BinaryWriter): void {
        writer.writeUInt32(this.fileSize)
        writer.writeUInt16(this.chunkSize)
        writer.writeUInt16(this.chunkCount)
        writer.writeUInt16(this.chunkIndex)
        writer.writeString(this.path)
    }

    packData(writer: BinaryWriter): void {
        const { chunkIndex, chunkSize } = this
        const start = (chunkIndex - 1) * chunkSize

        const bytes = this.buffer.subarray(start, start + chunkSize)
        writer.writeBytes(bytes)
    }

    private response?: StatusResponse
    onResponse(response: StatusResponse): void {
        this.response = response
    }

    next() {
        if (this.chunkIndex >= this.chunkCount) {
            return null
        }

        this.chunkIndex = this.response != null
            ? this.chunkIndex + 1
            : 1

        return this
    }

    end() {
        if (this.chunkIndex < this.chunkCount) {
            throw new ProtocolError('Not fully sent.')
        }

        return this.response!
    }
}
