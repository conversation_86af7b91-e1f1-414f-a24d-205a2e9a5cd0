import { jetEndian } from '../../../jet'
import type { Binary<PERSON>eader, BinaryWriter } from '../../../stream'
import { AbstractResponse, RequestClass } from '../../../transport'
import { ProtocolError } from '../../error'
import { normalizePath } from '../utils'

export class ReadFileResponse extends AbstractResponse {
    readonly writer = jetEndian.write()

    smallSize: number
    chunkIndex: number
    largeSize: number

    get fileSize() {
        const { smallSize = 0, largeSize = 0 } = this
        return largeSize > 0 ? largeSize : smallSize
    }

    get readSize() {
        return this.writer.length
    }

    unpackArg(reader: BinaryReader): void {
        this.smallSize = reader.readUInt16()
        this.chunkIndex = reader.readUInt16()
        this.largeSize = reader.readUInt32()
    }

    chunkContent: Buffer
    unpackData(reader: BinaryReader): void {
        const bytes = reader.readBytes()
        this.chunkContent = bytes
        this.writer.writeBytes(bytes)
    }

    isCompleted() {
        return this.readSize >= this.fileSize
    }
}

/** 读取指定路径的文件 */
export class ReadFile extends RequestClass(0x0108, ReadFileResponse) {
    /** 文件路径 */
    readonly path: string

    /** 分包序号，从 1 起算 */
    chunkIndex = 1

    constructor(
        path: string,
        /** 分包大小 */
        readonly chunkSize = 1024
    ) {
        super()
        this.path = normalizePath(path, true)
        this.chunkSize = chunkSize
    }

    packArg(writer: BinaryWriter): void {
        const { chunkIndex: index = 0 } = this
        if (index < 1) {
            throw new ProtocolError('Invalid chunkIndex: ' + String(index))
        }

        writer.writeUInt16(this.chunkSize)
        writer.writeUInt16(this.chunkIndex)
        // 《通讯协议》中对本指令的描述不完整
        // arg 字段后面接文件路径来指定读取哪个文件
        writer.writeString(this.path)
    }

    protected response?: ReadFileResponse
    createResponse(): ReadFileResponse {
        // 始终返回同一个响应对象，以保存所有分包数据
        if (this.response == null) {
            this.response = new ReadFileResponse()
        }

        return this.response
    }

    /** 获取下一个分包请求对象 */
    next() {
        const { response } = this
        if (response == null) {
            this.chunkIndex = 1
        } else if (!response.isCompleted()) {
            this.chunkIndex += 1
        } else {
            return null
        }

        return this
    }

    /** 全部读取完成后获取文件完整内容 */
    end() {
        const res = this.response
        if (res?.isCompleted()) {
            return res.writer.toBuffer()
        }

        throw new ProtocolError('Not fully read: ' + this.path)
    }
}
