import type { BinaryReader } from '../../stream'
import { AbstractResponse, RequestClass } from '../../transport'
import { bcdDecode } from '../../utils'
import type { LightGroupStatus } from './light'

export enum SyncStatus {
    /** 已经处于绿波带同步状态 */
    Coordinated = 0,
    /** 绿波带同步中，等待同步 */
    Coordinating,
    /** 处于手动模式，无法同步 */
    ManualOperating,
    /** 系统降级，无法同步 */
    SystemDowngraded,
    /** 按钮触发，同步失效 */
    KeyTrigged,
    /** 线圈触发，同步失效 */
    LoopTrigged,
}

export class MonitorResponse extends AbstractResponse {
    /** 当前步伐时间 */
    stepSeconds: number
    /** 数据大小 */
    dataSize: number
    /** 当前步伐剩余时间 */
    stepCountdown: number
    /** 当前放行相位号 */
    phaseIndex: number
    /** 相位运行计数器 */
    phaseActiveCount: number
    /** 人行灯偏移位置 */
    pedLightOffset: number
    /** 当前步伐号 */
    stepIndex: number

    /** 同步状态 */
    syncStatus: SyncStatus
    /** 绿波带偏移 */
    syncOffset: number

    /** 当前相位创建时间，分时日月 47151912 => 12-19 15:47 */
    phaseCreateDate: number
    /** 运行相位文件校验 */
    phaseFileSum: number

    /** 单点自适应标志 */
    selfAdaption: boolean
    /** 动态相位时间累计调节长度 */
    selfAdaptionDynamicSeconds: number

    /** 保留不使用（4bytes） */
    rev: null
    /** 当前相位放行文件名 */
    phaseFile: string

    /** 实时灯态 */
    lightGroupStatus: LightGroupStatus[]

    unpackData(reader: BinaryReader): void {
        this.stepSeconds = reader.readUInt8()
        this.dataSize = reader.readUInt8()
        this.stepCountdown = reader.readUInt8()
        this.phaseIndex = reader.readUInt8()
        this.phaseActiveCount = reader.readUInt8()
        this.pedLightOffset = reader.readUInt8()
        this.stepIndex = reader.readUInt8()
        this.syncStatus = reader.readUInt8()
        this.syncOffset = reader.readUInt16()
        // 分时日月 47151912 = 12-19 15:47
        this.phaseCreateDate = bcdDecode(reader.readUInt32())!
        this.phaseFileSum = reader.readUInt16()
        this.selfAdaption = !!reader.readUInt8()
        this.selfAdaptionDynamicSeconds = reader.readUInt8()
        this.rev = reader.skip(4)
        this.phaseFile = reader.readString(25)

        const lightGroupStatus: LightGroupStatus[] = []
        while (!reader.eof()) {
            lightGroupStatus.push(reader.readUInt8())
        }

        this.lightGroupStatus = lightGroupStatus
    }
}

/** 获取信号机实时状态，见《数据结构》七 */
export class Monitor extends RequestClass(0x0a05, MonitorResponse) {

}
