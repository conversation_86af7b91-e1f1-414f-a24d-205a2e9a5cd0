export class CommonError extends Error {
    constructor(
        message: string,
        readonly code?: number | null,
        readonly cause?: unknown
    ) {
        super(message)
    }
}

export class NotSupported extends CommonError {
    constructor(
        message = 'Not supported',
        code?: number | null,
        cause?: unknown
    ) {
        super(message, code, cause)
    }
}

export class NotImplementedError extends CommonError {
    constructor(
        message = 'Not implemented',
        code?: number | null,
        cause?: unknown
    ) {
        super(message, code, cause)
    }
}
