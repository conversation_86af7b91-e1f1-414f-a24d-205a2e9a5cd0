import { jetEndian } from '../jet'
import type { Binary<PERSON><PERSON><PERSON>, BinaryWriter } from '../stream'
import type { Request, Response } from '../transport'
import type { Packet } from './types'

export type Address = {
    group: number
    unit: number
}

let serial = 1
const MAX_SERIAL = 0xFFFF
function nextSerial() {
    const result = serial++
    if (result > MAX_SERIAL) {
        return serial = 1
    }

    return result
}

/** 数据包格式封装，见《通信协议》(一.2) */
export class JetPacket implements Packet {
    /** 数据节数（仅指 Data 字段） */
    protected dataLen: number

    /** 源地址 */
    sourceAddress?: Address
    /** 目的地址 */
    destinationAddress?: Address

    /** 包序号，可用于 请求/响应 配对 */
    packetSerial: number

    /** 大类/小类命令的组合值，如 0x0501 表示时间读取 */
    protected command: number
    /** 参数长度，argLen * 4 才是真实的字节长度 */
    protected argLen: number

    /** 对于请求通常是保留字节，对于响应如果 flag 为 1，表示状态回送 */
    flag?: number

    /** arg + data 的组合 */
    protected body: Buffer

    pack(writer: BinaryWriter): void {
        writer.writeUInt16(this.dataLen)

        const writeAddress = (val?: Address) => {
            const { group = 0, unit = 0 } = val ?? {}
            writer.writeUInt8(group)
            writer.writeUInt8(unit)
        }
        writeAddress(this.sourceAddress)
        writeAddress(this.destinationAddress)

        writer.writeUInt16(this.packetSerial)

        writer.writeUInt8((this.command >> 8) & 0xff)
        writer.writeUInt8(this.command & 0xff)

        writer.writeUInt8(this.argLen)
        writer.writeUInt8(this.flag ?? 0)

        writer.writeBytes(this.body)
    }

    unpack(reader: BinaryReader): this {
        this.dataLen = reader.readUInt16()

        this.sourceAddress = {
            group: reader.readUInt8(),
            unit: reader.readUInt8(),
        }
        this.destinationAddress = {
            group: reader.readUInt8(),
            unit: reader.readUInt8(),
        }

        this.packetSerial = reader.readUInt16()

        this.command = (reader.readUInt8() << 8) | reader.readUInt8()

        this.argLen = reader.readUInt8()
        this.flag = reader.readUInt8()

        this.body = reader.readBytes()

        return this
    }

    /** 创建一个请求数据包 */
    static request(req: Request): JetPacket {
        const result = new JetPacket()
        req.prepare?.(result)

        result.packetSerial = result.packetSerial ?? nextSerial()
        result.command = req.command()

        const writer = jetEndian.write()
        result.argLen = result.packArg(writer, req)
        result.dataLen = result.packData(writer, req)
        result.body = writer.toBuffer()

        return result
    }

    /** @returns argLen */
    protected packArg(writer: BinaryWriter, request: Request): number {
        const position = writer.position

        request.packArg(writer, this)

        const byteCount = writer.position - position
        const preferByteCount = Math.ceil(byteCount / 4) * 4
        if (preferByteCount > byteCount) {
            writer.writeBytes(preferByteCount - byteCount)
        }

        return preferByteCount / 4
    }

    /** @returns dataLen */
    protected packData(writer: BinaryWriter, request: Request): number {
        const position = writer.position

        request.packData(writer, this)
        return writer.position - position
    }

    /** 将数据包解析为响应内容 */
    response<T extends Response>(res: T): T {
        res.prepare?.(this)

        const { argLen, dataLen, body } = this

        const reader = jetEndian.read(body)
        if (argLen > 0) {
            this.unpackArg(reader, res, argLen)
        }
        if (dataLen > 0) {
            this.unpackData(reader, res, dataLen)
        }

        return res
    }

    protected unpackArg(reader: BinaryReader, response: Response, argLen: number) {
        const byteCount = argLen * 4
        const argReader = reader.endian.read(
            reader.readBytes(byteCount)
        )

        response.unpackArg(argReader, this, argLen)
    }

    protected unpackData(reader: BinaryReader, response: Response, dataLen: number) {
        const dataReader = reader.endian.read(
            reader.readBytes(dataLen)
        )

        response.unpackData(dataReader, this, dataLen)
    }
}
