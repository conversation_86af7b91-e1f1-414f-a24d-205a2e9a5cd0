import { expect, test } from 'vitest'
import { ReadDate, ReadDateResponse } from '../protocol/date'
import { JetPacker } from './packer'
import { JetPacket } from './packet'

test('pack', () => {
    const req = new ReadDate()

    const packet = JetPacket.request(req)
    packet.packetSerial = 9

    const packer = new JetPacker()
    const hex = packer.pack(packet).toString('hex')
    expect(hex).toBe('55a70f00000000000000090005010000')
})

test('unpack', () => {
    const buffer = Buffer.from('55A89800000000000000090005010300252007130102230100000000', 'hex')

    const packer = new JetPacker()
    const packet = packer.unpack(new JetPacket(), buffer)

    const res = packet.response(new ReadDateResponse())
    expect(res.value.getDate()).toBe(new Date('2025-07-13 01:02:23').getDate())
})
