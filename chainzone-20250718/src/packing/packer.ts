import { jetEndian } from '../jet'
import { sumBytes } from '../utils'
import type { JetPacket } from './packet'
import type { Packer } from './types'

const REQ_CODE = 0xA755
const RES_CODE = 0xA855

const CHECKSUM_MASK = 0xFFFF

/** 数据打包/解包辅助类，负责添加/去除请求或响应标识以及校验和 */
export class JetPacker implements Packer<JetPacket> {
    constructor(
        /** 解包时是否校验包头字节和校验和 */
        readonly strict = true
    ) { }

    /** 打包准备发送 */
    pack(packet: JetPacket): Buffer {
        const writer = jetEndian.write()
        // 添加请求标识
        writer.writeUInt16(REQ_CODE)

        // 先写入0占位校验和
        const sumPosition = writer.position
        writer.writeUInt16(0)

        const packetPosition = writer.position
        packet.pack(writer)

        // 包内容序列化
        const packetBytes = writer.toBuffer().subarray(packetPosition)
        // 计算并写入正确的校验和
        const packetSum = sumBytes(packetBytes, CHECKSUM_MASK)
        writer.seek(sumPosition, 'begin')
        writer.writeUInt16(packetSum)

        return writer.toBuffer()
    }

    /** 解包收到的数据包 */
    unpack(packet: JetPacket, buffer: Buffer): JetPacket {
        const reader = jetEndian.read(buffer)

        const synCode = reader.readUInt16()
        if (this.strict && synCode !== RES_CODE) {
            // 校验响应标识
            throw new Error('Unexpected synCode: ' + String(synCode))
        }

        const checkSum = reader.readUInt16()

        if (this.strict) {
            const packetPosition = reader.position

            const packetBytes = reader.slice()
            const packetSum = sumBytes(packetBytes, CHECKSUM_MASK)
            if (checkSum !== packetSum) {
                // 检查校验和
                throw new Error('Invalid checkSum value.')
            }

            reader.seek(packetPosition, 'begin')
        }
        // 解包为数据包
        return packet.unpack(reader)
    }
}
