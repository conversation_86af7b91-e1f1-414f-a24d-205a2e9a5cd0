import type { Binary<PERSON><PERSON><PERSON>, BinaryWriter } from '../stream'

export interface Packable {
    pack(writer: BinaryWriter): void
    unpack(reader: BinaryReader): this
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface Packet extends Packable {
}

export interface Packer<T extends Packet> {
    pack(packet: T): Buffer
    unpack(packet: T, buffer: Buffer): T
}
