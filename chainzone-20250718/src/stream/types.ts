import type { Endian } from './endian'

export type SeekOrigin = 'begin' | 'position' | 'end'

export interface BinaryStream {
    readonly endian: Endian
    readonly length: number
    readonly position: number

    toBuffer(): Buffer
    /** @return position before seek */
    seek(offset: number, origin: SeekOrigin): number
}

export interface BinaryReader extends BinaryStream {
    readUInt8(): number
    readUInt16(): number
    readUInt32(): number

    readBytes(byteCount?: number): Buffer
    readString(byteCount?: number): string

    skip(byteCount: number): null
    eof(): boolean
}

export interface BinaryWriter extends BinaryStream {
    writeUInt8(val: number): this
    writeUInt16(val: number): this
    writeUInt32(val: number): this

    writeBytes(byteCount: number, value?: number): this
    writeBytes(source: Buffer, sourcePosition?: number, length?: number): this
    writeString(value: string, byteCount?: number): this
}
