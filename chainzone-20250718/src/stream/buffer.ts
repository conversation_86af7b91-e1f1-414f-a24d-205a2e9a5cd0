import { Endian } from './endian'
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, BinaryWriter, Seek<PERSON><PERSON><PERSON> } from './types'

// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging
export class BinaryBuffer {
    protected _buffer: Buffer
    protected _position: number
    protected _length: number

    constructor(
        readonly endian: Endian,
        buffer?: Buffer | null,
        readonly capacity = 1024
    ) {
        const bytes = buffer ?? Buffer.alloc(0)

        this._buffer = bytes
        this._position = 0
        this._length = bytes.length
    }

    protected forward(byteCount: number, extend = false) {
        const { _position: pos, _length: len } = this
        const newPos = pos + byteCount
        if (newPos > len) {
            this.resize(newPos, extend)
        }

        this._position = newPos
        return pos
    }

    resize(size: number, extend?: boolean): void {
        const { _length: length } = this

        const newSize = size < 0 ? length + size : size
        if (newSize < 0) {
            throw new Error('Invalid size: ' + size)
        }

        if (newSize > length) {
            if (!extend) {
                throw new Error('Size overflow: ' + newSize)
            }

            if (newSize > this._buffer.length) {
                const cap = this.capacity
                const allocSize = Math.ceil(size / cap) * cap

                const buffer = Buffer.alloc(allocSize)
                this._buffer.copy(buffer)
                this._buffer = buffer
            }
        }

        this._length = newSize
    }

    /** @return position before seek */
    seek(offset: number, origin: SeekOrigin): number {
        const len = this._length
        if (len <= 0) {
            return 0
        }

        const result = this._position

        let pos = result
        if (origin === 'begin') {
            pos = offset
        } else if (origin === 'position') {
            pos += offset
        } else if (origin === 'end') {
            pos = len - 1 + offset
        } else {
            throw new Error('Unknown origin: ' + String(origin))
        }

        while (pos < 0) {
            pos += len
        }

        this._position = pos % len
        return result
    }

    slice(byteCount?: number, position?: number): Buffer {
        if (position != null) {
            this.seek(position, 'begin')
        }

        const size = byteCount ?? (this._length - this._position)
        const start = this.forward(size)

        return this._buffer.subarray(start, start + size)
    }

    get length () {
        return this._length
    }

    get position () {
        return this._position
    }

    toBuffer() {
        return this._buffer.subarray(0, this._length)
    }

    read(): BinaryReader {
        return this
    }

    readBytes(byteCount?: number): Buffer {
        const bytes = this.slice(byteCount)
        const result = Buffer.allocUnsafe(bytes.length)
        bytes.copy(result)
        return result
    }

    readString(byteCount?: number): string {
        let bytes = this.readBytes(byteCount)
        const endPos = bytes.indexOf(0)
        if (endPos >= 0) {
            bytes = bytes.subarray(0, endPos)
        }

        return bytes.toString('utf8')
    }

    skip(byteCount: number): null {
        this.forward(byteCount)
        return null
    }

    eof() {
        return this._position >= this._length
    }

    write(): BinaryWriter {
        return this
    }

    writeBytes(byteCount: number, value?: number): this
    writeBytes(source: Buffer, sourcePosition?: number, length?: number): this
    writeBytes(x: Buffer | number, y?: number, length?: number) {
        const [source, srcStart] = typeof x === 'number'
            ? [Buffer.alloc(x, y), 0] as const
            : [x, y ?? 0] as const

        const srcEnd = length != null ? srcStart + length : source.length
        const buffer = source.subarray(srcStart, srcEnd)

        const start = this.forward(buffer.length, true)
        buffer.copy(this._buffer, start)

        return this
    }

    writeString(value: string, byteCount?: number): this {
        const bytes = Buffer.from(value, 'utf8')

        let emptyByteCount = 0
        if (byteCount != null) {
            if (bytes.length > byteCount) {
                throw new Error('String byteCount overflow: ' + bytes.length)
            } else {
                emptyByteCount = byteCount - bytes.length
            }
        }

        this.writeBytes(bytes)
        if (emptyByteCount > 0) {
            this.writeBytes(emptyByteCount)
        }

        return this
    }
}

// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging
export interface BinaryBuffer extends BinaryReader, BinaryWriter {}

const numberBytes = { UInt8: 1, UInt16: 2, UInt32: 4 } as const

Object.entries(numberBytes).forEach(([k, v]) => {
    const type = k as keyof typeof numberBytes
    const byteCount = v

    BinaryBuffer.prototype[`read${type}`] = function(this: BinaryBuffer) {
        const endian = this.endian.getEndian()
        const method = `readUInt${endian}` as const
        return this._buffer[method](this.forward(byteCount), byteCount)
    }

    BinaryBuffer.prototype[`write${type}`] = function(this: BinaryBuffer, val: number) {
        const endian = this.endian.getEndian()
        const method = `writeUInt${endian}` as const
        // 必须先扩展 buffer 才能执行写入操作
        const offset = this.forward(byteCount, true)
        this._buffer[method](val, offset, byteCount)
        return this
    }
})
