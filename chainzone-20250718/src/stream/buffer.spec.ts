import { expect, test } from 'vitest'
import { BE, Endian, LE } from './endian'

const [x, y, z] = [0x01, 0x0203, 0x04050607]
const hexLE = '01030207060504'
const hexBE = '01020304050607'

function write(endian: Endian) {
    const writer = endian.write()
    writer.writeUInt8(x)
    writer.writeUInt16(y)
    writer.writeUInt32(z)
    writer.writeBytes(writer.toBuffer())

    return writer.toBuffer().toString('hex')
}

function read(endian: Endian, hex: string) {
    const reader = endian.read(Buffer.from(hex, 'hex'))

    return [
        reader.readUInt8(),
        reader.readUInt16(),
        reader.readUInt32(),
        reader.readBytes(),
    ] as const
}

test('write-le', () => {
    const hex = write(LE)
    expect(hex).toBe(hexLE + hexLE)
})

test('read-le', () => {
    const [X, Y, Z, bytes] = read(LE, hexLE + hexLE)
    expect([X, Y, Z]).toEqual([x, y, z])
    expect(bytes.toString('hex')).toBe(hexLE)
})

test('write-be', () => {
    const hex = write(BE)
    expect(hex).toBe(hexBE + hexBE)
})

test('read-le', () => {
    const [X, Y, Z, bytes] = read(BE, hexBE + hexBE)
    expect([X, Y, Z]).toEqual([x, y, z])
    expect(bytes.toString('hex')).toBe(hexBE)
})
