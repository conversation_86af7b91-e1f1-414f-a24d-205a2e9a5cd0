import { BinaryBuffer } from './buffer'

export class Endian {
    constructor(
        readonly bigEndian: boolean
    ) {

    }

    getEndian() {
        return this.bigEndian ? 'BE' : 'LE' as const
    }

    read(buffer: Buffer) {
        return new BinaryBuffer(this, buffer)
    }

    write(buffer?: Buffer | null, capacity?: number) {
        return new BinaryBuffer(this, buffer, capacity).write()
    }
}

export const BE = new Endian(true)
export const LE = new Endian(false)
