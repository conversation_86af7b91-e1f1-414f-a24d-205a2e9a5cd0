import type { JetPacket, Packet } from '../packing'
import type { BinaryWriter } from '../stream'
import type { Class } from '../types'
import { StatusResponse, UnknownResponse, type Response } from './response'

export interface Request<T extends Response = any> {
    command(): number
    prepare?(packet: JetPacket): void
    packArg(writer: BinaryWriter, packet: JetPacket): void
    packData(writer: BinaryWriter, packet: JetPacket): void

    createResponse(packet: Packet): T
    onResponse(response: T): void
}

export abstract class AbstractRequest<T extends Response> implements Request<T> {
    abstract command(): number
    abstract createResponse(packet: Packet): T

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    prepare(packet: JetPacket): void {}

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    packArg(writer: BinaryWriter, packet: JetPacket): void {}
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    packData(writer: BinaryWriter, packet: JetPacket): void {}

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onResponse(response: T): void {}
}

export class UnknownRequest extends AbstractRequest<UnknownResponse> {
    constructor(readonly commandCode: number) {
        super()
    }

    command(): number {
        return this.commandCode
    }

    createResponse(): UnknownResponse {
        return new UnknownResponse()
    }
}

export function RequestClass<T extends Response>(command: number, responseClz: Class<T>) {
    return class extends AbstractRequest<T> {
        command(): number {
            return command
        }

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        createResponse(packet: Packet): T {
            return new responseClz()
        }
    }
}

export function StatusRequestClass(command: number) {
    return RequestClass(command, StatusResponse)
}
