import { CommonError } from '../error'
import { <PERSON><PERSON>ack<PERSON>, JetPacket, type Packer } from '../packing'
import { DirectoryType, FileSystemEntry, ReadDir, ReadFile, ReadFileResponse, SendFile } from '../protocol/fs'
import type { Awaitable } from '../types'
import { asyncTimeout, TimeoutError } from '../utils'
import { Connection, type EndPoint } from './connection'
import type { Request } from './request'
import type { Response, StatusResponse } from './response'

export interface ResponsePayload<T extends Response> {
    remote: EndPoint
    packet: JetPacket,
    response: T
}

export type RequestMultipleCallback<T extends Response>
    = (payload: ResponsePayload<T>) => Awaitable<boolean | number | void>

export interface RequestMultipleOptions<T extends Response> {
    callback: RequestMultipleCallback<T>
    signal?: AbortSignal
}

export type ReadDirCallback = (target: FileSystemEntry) => Awaitable<boolean | number | void>

export type ReadFileOptions = {
    chunkRetries?: number
    chunkCallback?: (response: ReadFileResponse) => Awaitable<boolean | number | void>
}

export class JetClient extends Connection<JetPacket> {
    defaultTimeout = 2000

    readonly packer = new JetPacker()
    getPacker(): Packer<JetPacket> {
        return this.packer
    }

    createPacket(): JetPacket {
        return new JetPacket()
    }

    async request(request: Request) {
        const packet = JetPacket.request(request)
        await this.send(packet)
    }

    async requestStatus<T extends StatusResponse>(request: Request<T>, timeout?: number) {
        const { response } = await this.requestOne(request, timeout)
        return response
    }

    async requestRaw<T extends Response>(request: Request<T>, timeout?: number) {
        const result = await this.requestOne(request, timeout)
        return result.response
    }

    async requestOne<T extends Response>(request: Request<T>, timeout?: number) {
        const requestPacket = JetPacket.request(request)
        const { packetSerial } = requestPacket

        let off: () => void
        const promise = new Promise<ResponsePayload<T>>((resolve, reject) => {
            off = this.on('received', e => {
                try {
                    const { packet, remote } = e
                    if (packet.packetSerial === packetSerial) {
                        const response = packet.response(
                            request.createResponse(packet)
                        )
                        request.onResponse(response)

                        resolve({ response, remote, packet })
                    }
                } catch (err) {
                    reject(err)
                }
            })

            this.send(requestPacket)
        })

        const timeoutMs = timeout ?? this.defaultTimeout
        const result = timeoutMs > 0
            ? asyncTimeout(() => promise, timeoutMs)
            : promise

        return result.finally(() => off())
    }

    async requestMultiple<T extends Response>(request: Request<T>, options: RequestMultipleOptions<T>, timeout?: number): Promise<void>
    async requestMultiple<T extends Response>(request: Request<T>, callback: RequestMultipleCallback<T>, timeout?: number): Promise<void>
    async requestMultiple<T extends Response>(request: Request<T>, x: RequestMultipleOptions<T> | RequestMultipleCallback<T>, timeout?: number) {
        const options: RequestMultipleOptions<T> = typeof x === 'function'
            ? { callback: x } : x

        const requestPacket = JetPacket.request(request)
        const { packetSerial } = requestPacket

        let off: () => void
        let timer: ReturnType<typeof asyncTimeout> | undefined
        const promise = new Promise<void>((resolve, reject) => {
            const { callback, signal } = options
            if (signal != null) {
                signal.onabort = () => {
                    const { reason } = signal
                    if (reason instanceof Error) {
                        reject(reason)
                    } else {
                        resolve()
                    }
                }
            }

            off = this.on('received', async (e) => {
                try {
                    const { packet, remote } = e
                    if (packet.packetSerial === packetSerial) {
                        const response = packet.response(
                            request.createResponse(packet)
                        )
                        request.onResponse(response)

                        const nextTick = await callback({ response, remote, packet })
                        if (nextTick === false) {
                            resolve()
                        } else if (typeof nextTick === 'number') {
                            if (timer == null) {
                                timer = asyncTimeout(() => promise, nextTick)
                            } else {
                                timer.reset(nextTick)
                            }
                        } else {
                            timer?.reset()
                        }
                    }
                } catch (err) {
                    reject(err)
                }
            })

            this.send(requestPacket)
        })

        const timeoutMs = timeout ?? this.defaultTimeout
        const result = timeoutMs > 0
            ? (timer = asyncTimeout(() => promise, timeoutMs))
            : promise

        return result.finally(() => {
            off()
            timer?.clear()
        })
    }

    async readDir(target: DirectoryType, beforeRead?: ReadDirCallback, timeout?: number): Promise<FileSystemEntry>
    async readDir(path: string, depth?: number | null, beforeRead?: ReadDirCallback, timeout?: number): Promise<FileSystemEntry>
    async readDir(request: ReadDir, depth?: number | null, beforeRead?: ReadDirCallback, timeout?: number): Promise<FileSystemEntry>
    async readDir(x: DirectoryType | string | ReadDir, ...args: unknown[]): Promise<FileSystemEntry> {
        const request = x instanceof ReadDir ? x : new ReadDir(x)

        let depth = typeof x === 'number' ? 0
            : (args.splice(0, 1)[0] ?? 0) as number

        const [beforeRead, timeout] = args as [
            ReadDirCallback | undefined,
            number | undefined
        ]

        let result: FileSystemEntry
        if (request.target === '') {
            result = new FileSystemEntry('', true)
            // cSpell: ignore cdef
            const items = [...'CDEF'].map(x =>
                new FileSystemEntry(`${x}:`, true)
            )

            // const config = await this.readConfigSys()
            // cSpell: ignore sdcard
            // if (config.sdcard_exist) {
            //     items.push(new FileSystemInfo('E:/', true))
            // }

            // if (config.f_drive_exist) {
            //     items.push(new FileSystemEntry('F:/', true))
            // }

            result.appendChild(...items)
        } else {
            let req = request
            while (req != null) {
                try {
                    await this.requestOne(req, timeout)
                    req = req.next()!
                } catch (err) {
                    throw new ClientError(`Failed to read directory: ${req.displayTarget} (offset ${req.readOffset})`, err)
                }
            }

            result = request.end()
        }

        if (--depth > 0) {
            for (const x of (result.children ?? [])) {
                if (!x.isDir()) continue;

                const nextTick = await beforeRead?.(x)
                if (nextTick === false) continue;

                const localTimeout = typeof nextTick === 'number'
                    ? nextTick : timeout

                const { children } = await this.readDir(x.getPath(), depth, beforeRead, localTimeout)
                if (children != null) {
                    x.appendChild(...children)
                }
            }
        }

        return result
    }


    async readFile(path: string, timeout?: number | null, options?: ReadFileOptions): Promise<Buffer>
    async readFile(request: ReadFile, timeout?: number | null, options?: ReadFileOptions): Promise<Buffer>
    async readFile(x: string | ReadFile, timeout?: number, options?: ReadFileOptions) {
        const request = x instanceof ReadFile ? x : new ReadFile(x)

        const { chunkRetries = 5, chunkCallback } = options ?? {}

        let req = request
        try {
            while (req != null) {
                let retryTimes = chunkRetries
                let chunkTimeout = timeout
                while (true) {
                    try {
                        const response = await this.requestRaw(req, chunkTimeout)

                        const nextTick = await chunkCallback?.(response)
                        if (nextTick === false) {
                            return response.writer.toBuffer()
                        } else if (typeof nextTick === 'number') {
                            chunkTimeout = nextTick
                        }

                        req = req.next()!
                        break
                    } catch (err) {
                        if (err instanceof TimeoutError) {
                            if (retryTimes-- > 0) continue;
                        }

                        throw err
                    }
                }
            }
        } catch (err) {
            throw new ClientError(`Failed to read file: ${req.path} (offset ${(req.chunkIndex - 1) * req.chunkSize})`, err)
        }

        return request.end()
    }

    async sendFile(path: string, buffer: Buffer, timeout?: number): Promise<StatusResponse>
    async sendFile(request: SendFile, timeout?: number): Promise<StatusResponse>
    async sendFile(x: SendFile | string, y?: Buffer | number, z?: number): Promise<StatusResponse> {
        const request = x instanceof SendFile
            ? x : new SendFile(x, y as Buffer)
        const timeout = typeof y === 'number' ? y : z

        let req = request
        while (req != null) {
            try {
                await this.requestOne(req, timeout)
                req = req.next()!
            } catch (err) {
                throw new ClientError(`Failed to send file: ${req.path} (offset ${req.chunkIndex})`, err)
            }
        }

        return request.end()
    }

}

export class ClientError extends CommonError {
}
