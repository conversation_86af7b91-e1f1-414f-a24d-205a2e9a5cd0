import { CommonError, NotImplementedError } from '../error'
import type { JetPacket } from '../packing'
import type { BinaryReader } from '../stream'
import { hexEncode } from '../utils'

export interface Response {
    prepare?(packet: JetPacket): void
    unpackArg(reader: BinaryReader, packet: JetPacket, argLen: number): void
    unpackData(reader: BinaryReader, packet: JetPacket, dataLen: number): void
}

export abstract class AbstractResponse implements Response {
    prepare(packet: JetPacket): void {
        if (packet.flag) {
            packet.response(new StatusResponse())
            throw new Error('Unexpected status response.')
        }
    }

    unpackArg(reader: BinaryReader, packet: JetPacket, argLen: number): void {
        this.internalUnpack(reader, packet, argLen * 4)
    }

    unpackData(reader: BinaryReader, packet: JetPacket, dataLen: number): void {
        this.internalUnpack(reader, packet, dataLen)
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected internalUnpack(reader: Binary<PERSON>eader, packet: JetPacket, byteCount: number): void {
        throw new NotImplementedError()
    }
}

export class UnknownResponse extends AbstractResponse {
    arg: Buffer | null
    data: Buffer | null

    unpackArg(reader: BinaryReader, packet: JetPacket, argLen: number): void {
        if (argLen > 0) {
            this.arg = reader.readBytes(argLen * 4)
        }
    }

    unpackData(reader: BinaryReader, packet: JetPacket, dataLen: number): void {
        if (dataLen > 0) {
            this.data = reader.readBytes(dataLen)
        }

    }
}

const statusMsg = {
    0x9000: 'OK',
    0x9001: '通信同步头错误',
    0x9002: '和校验错误',
    0x9003: '通讯地址错误',
    0x9004: '大类命令非法',
    0x9005: '小类命令非法',

    0x9006: '包长度不对',
    0x9007: '磁盘已满',
    0x9008: '文件不存在',
    0x9009: '文件已达末尾',
    0x9010: '打开文件失败',
    0x9011: '小类命令在这系统中不支持',
    0x9012: '文件写入失败（磁盘空间不足）',
    0x9013: '分包数据过大',
    0x9014: '分包顺序错误',
    0x9015: '删除失败（文件已打开）',

    0x9030: '本操作需要登陆',
    0x9031: '登录密码错误',
    0x9032: '登录用户错误',
    0x9033: '旧密码不匹配',
    0x9035: '已于他处登录',

    0x2000: '写入文件失败（管理内存申请失败）',
    0x2101: '写入文件内容过大',
    0x2102: '写入文件失败（磁盘空间不足）',
    0x2103: '写入文件失败（C:盘空间不足）',
    0x2104: '写入文件失败（D:盘空间不足）',
    0x2105: '写入文件失败（E:盘空间不足）',
    0x2106: '写入文件失败（F:盘空间不足）',

    0x5201: '时间设置不成功',

    0x6701: '没有当前播放内容',
    0x6702: '没有下一个播放内容',

    0x7201: '格式化磁盘失败',
    0x7301: '创建文件夹失败',
    0x7401: '重命名文件失败',
    0x7402: '无效的文件路径',
    0x7501: '移动文件夹失败',
    0x7601: '删除文件夹失败',
    0x7B01: '打开文件夹失败',
    0x7D01: '读取磁盘信息失败',
    0x7E01: '改文件不存在',

    0x8301: '系统处于 READY 状态，可以接收连接播放数据了',
    0x8302: '系统处于 BUSY 状态',

    0x0A01: '切换手动控制时优先级不足',
    0x0A02: '系统繁忙，请 1 秒后重试',
    0x0A03: '命令执行中，请等待',
}

export class StatusResponse implements Response {
    statusCode: number

    constructor(
        readonly autoAssert = true
    ) { }

    get statusMessage(): string {
        const { statusCode: code } = this
        return statusMsg[code] ?? '<unknown>'
    }

    prepare(packet: JetPacket): JetPacket {
        if (!packet.flag) {
            throw new Error('Not a status response.')
        }

        return packet
    }

    unpackArg(): void { }

    unpackData(reader: BinaryReader): void {
        this.statusCode = reader.readUInt16()
        if (this.autoAssert) {
            this.assert()
        }
    }

    assert(): 'OK' {
        const { statusCode, statusMessage } = this
        if (statusMessage !== 'OK') {
            throw new StatusError(`[${hexEncode(statusCode, 2, '0x')}]${statusMessage}`, statusCode)
        }

        return statusMessage
    }

    finish(): this {
        this.assert()
        return this
    }
}

export class StatusError extends CommonError {
    constructor(message: string, code = 0) {
        super(message, code)
    }
}
