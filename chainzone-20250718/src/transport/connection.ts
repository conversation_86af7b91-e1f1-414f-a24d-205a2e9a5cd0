import { createSocket, Socket, type RemoteInfo } from 'dgram'
import type { AddressInfo } from 'net'
import { debug } from 'util'
import { Emitter } from '../emitter'
import { type Packer, type Packet } from '../packing'

const d = debug('chainzone:conn')

export type EndPoint = {
    address: string
    port: number
}

export type TransportPayload<T> = {
    remote: EndPoint
    buffer: Buffer
    packet: T
}

export type ConnectionEvents<T> = {
    open: {
        socket: Socket
        address: EndPoint
        reason?: unknown
    }
    close: {
        address?: EndPoint
        reason?: unknown
    }

    error: {
        error: unknown
    }

    sent: TransportPayload<T>
    received: TransportPayload<T>
}


export abstract class Connection<T extends Packet> extends Emitter<ConnectionEvents<T>> {
    constructor(
        private defaultRemote?: EndPoint,
        readonly localPort?: number
    ) {
        super()
    }

    abstract getPacker(): Packer<T>
    abstract createPacket(): T

    getDefaultRemote() {
        const result = this.defaultRemote
        if (result == null) {
            throw new Error('Default remote is not set.')
        }

        return result
    }

    private _socket?: Socket
    get socket() {
        return this._socket
    }

    get address() {
        return this._socket?.address()
    }

    async open(reason?: unknown) {
        if (this.address != null) {
            return
        }

        const socket = createSocket('udp4')

        const onError = this.onError.bind(this)
        const onMessage = this.onMessage.bind(this)

        socket.on('error', onError)
        socket.on('message', onMessage)
        socket.once('close', () => {
            socket.off('error', onError)
            socket.off('message', onMessage)
        })

        const { localPort } = this
        await new Promise<void>((resolve, reject) => {
            socket.once('error', (err) => {
                this.close(err)
                reject(err)
            })
            socket.bind(localPort, undefined, resolve)
        })

        this._socket = socket
        this.onOpen(socket, socket.address(), reason)
    }

    protected onOpen(socket: Socket, address: AddressInfo, reason?: unknown) {
        this.emit('open', { socket, address, reason })
        d('[open] (%s:%d)', address.address, address.port)
    }

    async send(packet: T, remote?: EndPoint) {
        remote = remote ?? this.defaultRemote
        if (remote == null) {
            throw new Error('Remote target is not defined.')
        }

        await this.open('send package')

        const buffer = this.getPacker().pack(packet)

        const { port, address } = remote

        await new Promise<void>((resolve, reject) => {
            this.socket!.send(buffer, port, address, (err) => {
                if (err != null) {
                    reject(err)
                } else {
                    resolve()
                }
            })
        })

        this.onSent(buffer, packet, remote)
    }

    protected onSent(buffer: Buffer, packet: T, remote: EndPoint): void {
        this.emit('sent', { buffer, remote, packet })

        if (d.enabled) {
            d('[sent] (%s:%d): %s', remote.address, remote.port, buffer.toString('hex'))
        }
    }

    protected onMessage(buffer: Buffer, rinfo: RemoteInfo) {
        if (d.enabled) {
            const { address, port } = rinfo
            d('[recv] (%s:%d): %s', address, port, buffer.toString('hex'))
        }

        const packet = this.unpack(buffer)
        this.emit('received', { buffer, remote: rinfo, packet })
    }

    protected unpack(buffer: Buffer) {
        const packet = this.createPacket()
        return this.getPacker().unpack(packet, buffer)
    }

    async close(reason?: unknown) {
        const { address } = this

        await new Promise<void>((resolve) => {
            const { socket } = this
            if (socket != null) {
                socket.close(resolve)
            } else {
                resolve()
            }
        })

        this.onClosed(address, reason)
    }

    protected onError(error: unknown) {
        this.emit('error', { error })
        // cSpell: ignore eror
        d('[eror] %s', error)
    }

    protected onClosed(address?: AddressInfo, reason?: unknown): void {
        this.emit('close', { address, reason })

        if (d.enabled && address != null) {
            d('[clos] (%s:%d)', address.address, address.port)
        }
    }
}
