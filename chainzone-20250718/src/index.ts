import 'dotenv'
import yargs from 'yargs'
import { hideBin } from 'yargs/helpers'
import { setupCommands } from './command'

async function main() {
    const app = yargs(hideBin(process.argv))
        .option('json', { type: 'boolean' })
        // .showHelpOnFail(false)
        .demandCommand()

    await setupCommands(app)

    const { body, json } = await app.parseAsync()
    if (body !== undefined) {
        // 打印命令返回内容
        console.log(json
            ? JSON.stringify(body, null, 4)
            : body
        )
    }
}

/** 等待异步操作完成，避免进程提前退出 */
function wait() {
    let timer: ReturnType<typeof setTimeout> | undefined
    function setupTimer() {
        clearTimeout(timer)
        timer = setTimeout(() => {
            setupTimer()
        }, 1000)
    }

    setupTimer()
    return () => clearTimeout(timer)
}

main().finally(wait())
