import { format } from 'util'
import { Monitor } from '../protocol/monitor'
import { asyncDelay } from '../utils'
import { defineJetCommand } from './jet.command'

export default defineJetCommand({
    describe: '读取信号机实时状态',
    builder: (app) => {
        return app.option('monitor', {
            alias: ['M'],
            type: 'boolean'
        })
    },
    handler: async (client, args) => {
        const req = new Monitor()
        const requestOne = async () => {
            const result = await client.requestRaw(req)
            return result
        }

        if (!args.monitor) {
            return requestOne()
        }

        const abort = new AbortController()
        process.on('SIGINT', () => {
            console.log('\n')
            abort.abort('<user abort>')
        })
        console.log('\nPress `Ctrl+C` to abort...\n')

        while (!abort.signal.aborted) {
            const res = await requestOne()
            const content = format('\r> %s [%d:%d] %d/%d    ',
                res.phaseFile,
                res.phaseIndex, res.stepIndex,
                res.stepSeconds, res.stepCountdown
            )
            process.stdout.write(content)
            await asyncDelay(1000, abort)
        }
    }
})
