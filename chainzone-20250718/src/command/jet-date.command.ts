import { ReadDate, SendDate } from '../protocol/date'
import { defineJetCommand } from './jet.command'

export default defineJetCommand({
    describe: '读取信号机系统时间',
    handler: async (client) => {
        const res = await client.requestRaw(new ReadDate())
        return res.value
    },
    subCommands: {
        send: {
            describe: '将本计算机系统时间同步到信号机',
            handler: async (client) => {
                const res = await client.requestRaw(new SendDate())
                return res.statusMessage
            }
        }
    }
})
