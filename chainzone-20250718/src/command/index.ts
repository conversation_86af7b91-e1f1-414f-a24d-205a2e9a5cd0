import { glob } from 'glob'
import { basename, extname, join } from 'path'
import type { Argv } from 'yargs'
import { ConsoleError } from './error'
import type { CommandDefinition, CommandModule, CommandOptions, GlobalOptions } from './types'

export * from './types'

type CommandEntry = {
    file: string
    definition: CommandDefinition
}

/** 搜素和加载 *.command.ts/js 文件中定义的命令 */
async function resolveCommands() {
    const pattern = join(__dirname, '*.command.[jt]s')
        .replace(/\\+/g, '/')

    const files = await glob(pattern)

    const result: CommandEntry[] = []
    for (const x of files) {
        const src = './' + basename(x)
        const { default: definition } = await import(src) ?? {}
        if (definition != null) {
            result.push({
                file: x,
                definition
            })
        }
    }

    return result
}

/** 将命令定义转换为 yargs 要求的格式 */
function normalizeCommand(entry: CommandEntry, parent?: CommandModule): CommandModule {
    const { file, definition } = entry
    const options: CommandOptions = typeof definition === 'function'
        ? { handler: definition } : definition

    const { name, positional, builder, handler, subCommands, ...rest } = options
    let commandName = name ?? extractCommandName(file)
    if (parent != null) {
        commandName = parent.name + (commandName === ''
            ? ''
            : '/' + commandName
        )
    }

    const command = [commandName, positional]
        .filter(Boolean)
        .join(' ')

    const result: CommandModule = {
        ...rest,
        name: commandName,
        command,
        builder: async (app) => {
            let localApp: any = app
            if (typeof parent?.builder === 'function') {
                // 子命令继承父命令的选项
                localApp = await parent.builder(localApp)
            }
            if (typeof builder === 'function') {
                localApp = await builder(localApp ?? app)
            }

            return localApp ?? app
        },
    }

    if (handler != null) {
        result.handler = async (args) => {
            try {
                const body = await handler(args)
                if (body !== undefined) {
                    // 如果命令函数有返回值，存放在 args 中，供后续打印出来
                    args['body'] = body
                }
            } catch (err) {
                if (err instanceof ConsoleError) {
                    throw err
                }

                throw new ConsoleError(
                    err.message ?? String(err),
                    err.errorCode ?? err.errCode ?? err.code,
                    err
                )
            }
        }
    }

    if (subCommands != null) {
        result.subCommands = Object.entries(subCommands)
            .map(([k, v]) => {
                const item: CommandDefinition<any> = typeof v === 'function'
                    ? { handler: v } : v
                item.name = item.name ?? k
                return normalizeCommand(
                    { file, definition: item },
                    result
                )
            })
    }

    return result
}

/** 从文件名推导命令名，如 jet-date.command.ts => jet-date */
function extractCommandName(fileName: string) {
    // basename without extname like .js|.ts
    const name = basename(fileName, extname(fileName))
    // name without .command
    return basename(name, '.command')
}

/** 搜素，加载并注册命令 */
export async function setupCommands(app: Argv<GlobalOptions>) {
    let result: Argv<any> = app
    const items = await resolveCommands()
    const registerCommand = (x: CommandModule) => {
        const { describe, handler, subCommands, ...rest } = x
        if (handler != null) {
            result = result.command({ ...rest, handler, describe: describe ?? '' })
        }
        if (subCommands != null) {
            subCommands.forEach(y => registerCommand(y))
        }
    }
    items.forEach(x => {
        const command = normalizeCommand(x)
        registerCommand(command)
    })

    return result
}
