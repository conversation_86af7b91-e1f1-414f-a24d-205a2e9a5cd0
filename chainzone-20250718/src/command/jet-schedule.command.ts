import { JetFile } from '../protocol/fs'
import { Schedule, SPECIAL_SPD } from '../protocol/fs/body/schedule'
import { defineJetCommand } from './jet.command'

export default defineJetCommand({
    describe: '读取特殊日期信息',
    handler: async (client) => {
        const buffer = await client.readFile(SPECIAL_SPD)
        const result = JetFile.fromBuffer(buffer, new Schedule())
        return result
    }
})
