import archiver from 'archiver'
import dayjs from 'dayjs'
import ora from 'ora'
import type { FileSystemEntry } from '../protocol/fs'
import type { JetClient } from '../transport/client'
import { sha1sum, treePrinter } from '../utils'
import { prepareOutputStream } from './jet-file.command'
import { defineJetCommand } from './jet.command'

export default defineJetCommand({
    describe: '从信号机备份所有文件',
    builder: (app) => app
        .option('output', {
            alias: ['O'],
            type: 'string',
            default: 'runtime/',
        }),
    handler: async (client, args) => {
        const spinner = ora()
        // const spinner = ora('Listing files').start()
        const root = await client.readDir('', Number.MAX_SAFE_INTEGER)
        // spinner.succeed().text = ''

        const output = prepareOutputStream(args.output,
            () => resolveFileName(client)
        )

        const archive = archiver('zip')
        archive.pipe(output, { end: true })

        const { stderr } = process
        const sumList: string[] = []
        const print = treePrinter(async (x: string, y: FileSystemEntry) => {
            const path = y.getPath()
            const name = path.replace(/[:]/g, '')

            stderr.write(x)
            if (path) {
                if (y.isDir()) {
                    archive.append('', { name: name + '/' })
                } else {
                    const buffer = await client.readFile(path, null, {
                        chunkCallback: (res) => {

                            const percent = Math.floor(res.readSize / res.fileSize * 100)
                            const suffix = percent >= 100
                                ? '  ✔             '
                                : `  ${spinner.frame()} ${percent}%  `
                            stderr.write('\r' + x + suffix)
                        }
                    })
                    archive.append(buffer, { name })
                    sumList.push(`${sha1sum(buffer)} *${name}`)
                }
            }
            stderr.write('\n')
        })

        await print(root,
            x => x.displayName + (x.isDir() ? '' : ` (${x.file_size})`)
        )

        archive.append(sumList.join('\n'), { name: 'sha1sum.txt' })

        await archive.finalize()
    }
})

function resolveFileName(client: JetClient) {
    const { address } = client.getDefaultRemote()
    // cSpell: ignore YYYYMMDDHHmm
    const date = dayjs().format('YYYYMMDD_HHmm')
    return `chainzone-${address}-bak${date}.zip`
}
