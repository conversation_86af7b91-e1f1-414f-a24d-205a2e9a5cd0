import { type ArgumentsCamelCase, type Argv } from 'yargs'
import { UnknownRequest } from '../transport'
import { JetClient } from '../transport/client'
import type { CommandBuilder, CommandDefinition, CommandOptions, GlobalOptions } from './types'

type JetOptions = GlobalOptions & {
    port: number
    address: string
    timeout: number
}

export type <PERSON><PERSON>om<PERSON>Handler<T extends JetOptions> = (client: JetClient, args: ArgumentsCamelCase<T>) => any
export type JetCommandOptions<T extends JetOptions> = Omit<CommandOptions<T>, 'handler' | 'builder' | 'subCommands' > & {
    builder?: CommandBuilder<JetOptions, T>
    handler: JetCommandHandler<T>
    subCommands?: Record<string, JetCommandDefinition<T>>
}

export type JetCommandDefinition<T extends JetOptions> = JetCommandOptions<T> | JetCommandHandler<T>

/** 定义信号机操作命令 */
export function defineJetCommand<T extends JetOptions = JetOptions>(options: JetCommandOptions<T> | JetCommandHandler<T>) {
    const { builder, handler, subCommands, ...rest }: JetCommandOptions<T> =
        typeof options === 'function'
            ? { handler: options as any }
            : options

    const result: CommandOptions<T> = {
        ...rest,
        builder: (app) => {
            const result = app
                .option('port', {
                    type: 'number',
                    default: Number(process.env.DEFAULT_PORT ?? 9520)
                })
                .option('address', {
                    default: process.env.DEFAULT_ADDRESS ?? '***********'
                })
                .option('timeout', {
                    type: 'number',
                    default: Number(process.env.DEFAULT_TIMEOUT ?? 2000)
                })

            return (builder?.(result) ?? result) as Argv<T>
        },
        handler: async (args) => {
            const { port, address, timeout } = args
            const client = new JetClient({ address, port })
            if (timeout != null) {
                client.defaultTimeout = timeout
            }
            try {
                return await handler(client, args)
            } finally {
                await client.close()
                // console.log((process as any)._getActiveHandles())
            }
        }
    }

    if (subCommands != null) {
        result.subCommands = Object.entries(subCommands)
            .reduce(
                (res, [k, v]) => {
                    const item: JetCommandOptions<T> = typeof v === 'function'
                        ? { handler: v } : v
                    res[k] = defineJetCommand(item)
                    return res
                },
                {} as Record<string, CommandDefinition<T>>
            )
    }

    return result
}

/**
 * e.g.
 * ```
 * ./cli jet 0x0501 --json
 *
 * {
 *     "arg": "252007171006510500000000"
 * }
 * ```
 */
const jetCommand = defineJetCommand({
    describe: '测试简单无参数（arg/data）指令',
    positional: '<command>',
    builder: (app) => {
        return app.positional('command', {
            type: 'number',
            demandOption: true,
        })
    },
    handler: async (client, args) => {
        const req = new UnknownRequest(args.command)
        const { arg, data } = await client.requestRaw(req)
        return {
            arg: arg?.toString('hex'),
            data: data?.toString('hex'),
        }
    }
})

export default jetCommand
