import { ReadAbsolute } from '../protocol/fs/absolute'
import { ADDRESS_Identity, Identity } from '../protocol/fs/absolute/identity'
import { defineJetCommand } from './jet.command'

export default defineJetCommand({
    describe: '读取信号机ID信息',
    handler: async (client) => {
        const req = new ReadAbsolute(ADDRESS_Identity)
        const res = await client.requestRaw(req)
        return res.unpack(new Identity())
    }
})
