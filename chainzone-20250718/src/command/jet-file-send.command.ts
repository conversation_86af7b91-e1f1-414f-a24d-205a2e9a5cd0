import { createReadStream } from 'fs'
import ora from 'ora'
import { basename, extname } from 'path'
import { SendFile } from '../protocol/fs'
import { ConsoleError } from './error'
import { defineJetCommand } from './jet.command'

export default defineJetCommand({
    name: 'jet-file/send',
    positional: '<files..>',
    describe: '上传文件到信号机',
    builder: (app) => app
        .options('output', {
            alias: ['O'],
            type: 'string'
        })
        .positional('files', {
            type: 'string',
            array: true,
            demandOption: true,
        }),
        // .array('files'),
    handler: async (client, args) => {
        const { files, output } = args
        const items = files.map(x => ({
            source: x,
            target: adjustPath(x, output),
        }))

        const spinner = ora()
        for (const x of items) {
            const { source, target } = x
            spinner.start('Loading: ' + source)
            const buffer = await readFile(source)
            spinner.text = 'Sending: ' + source

            const req = new SendFile(target, buffer)
            await client.sendFile(req)

            spinner.succeed(source + ' => ' + req.path)
        }
        spinner.stop()
    }
})

const dirMap: Record<string, string> = {
    '.SPD': 'D:/SPECIAL',
    '.TAB': 'D:/IOTABLE',
    '.PER': 'D:/PERIOD',
    '.PHE': 'D:/PHASE',
    '.INI': 'D:/CONFIG',
    '.MAP': 'D:/MAP',
    '.CFT': 'D:/CONFLICT',
}

function adjustPath(localFile: string, output?: string) {
    localFile = localFile.replace(/\\+/g, '/')
    output = (output ?? '').replace(/\\+/g, '/')
    if (output.endsWith('/')) {
        return output + basename(localFile)
    } else if (output.includes('/')) {
        return output
    }

    const baseName = basename(output || localFile)
    const extName = extname(baseName)
    const dirName = dirMap[extName]
    if (dirName == null) {
        throw new ConsoleError('Failed to adjust target directory name.')
    }

    return `${dirName}/${baseName}`
}

async function readFile(localFile: string) {
    const stream = createReadStream(localFile)
    return new Promise<Buffer>((resolve, reject) => {
        const chunkList: Buffer[] = []
        stream.on('error', reject)
        stream.on('data', (chunk: Buffer) => {
            chunkList.push(chunk)
        })
        stream.on('end', () => {
            resolve(Buffer.concat(chunkList))
        })
    }).finally(() => stream.destroy())
}
