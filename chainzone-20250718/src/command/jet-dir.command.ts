import { DirectoryType } from '../protocol/fs'
import { treePrinter } from '../utils'
import { defineJetCommand } from './jet.command'

export default defineJetCommand({
    describe: '读取信号机目录下的文集/目录列表',
    positional: '[target]',
    builder: (app) => app
        .positional('target', {
            type: 'string',
            default: '',
        })
        .option('depth', {
            alias: ['D', 'L'],
            type: 'number',
            default: 1,
        }),
    handler: async (client, args) => {
        const { target, depth } = args
        const dir = (DirectoryType[target] as DirectoryType | undefined)
            ?? target

        const res = typeof dir === 'number'
            ? await client.readDir(dir)
            : await client.readDir(dir, depth)

        const print = treePrinter()
        await print(
            res,
            x => x.displayName + (x.isDir() ? '' : ` (${x.file_size})`)
        )
    }
})
