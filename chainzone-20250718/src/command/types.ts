import type { ArgumentsCamelCase, Argv } from 'yargs'
import type { Awaitable } from '../types.js'

export type GlobalOptions = {
    json?: boolean
}

export interface CommandOptions<T = GlobalOptions> {
    /** 命令名称 */
    name?: string
    /** 命令参数定义 */
    positional?: string
    /** 命令别名 */
    aliases?: readonly string[] | string

    /** 命令描述，在 --help 的情况下显示 */
    describe?: string

    /** 命令参数/选项定义 */
    builder?: CommandBuilder<GlobalOptions, T>
    /** 命令执行函数 */
    handler?: CommandHandler<T>

    /** 子命令列表 */
    subCommands?: Record<string, CommandDefinition<T>>
}

export interface CommandModule {
    name: string
    command: string
    describe?: string
    aliases?: readonly string[] | string
    builder: (app: Argv<any>) => Promise<Argv<any>>
    handler?: (args: ArgumentsCamelCase) => any
    subCommands?: CommandModule[]
}

export type CommandBuilder<T = GlobalOptions, U = T> = (app: Argv<T>) => Awaitable<Argv<U> | void>
export type CommandHandler<T = GlobalOptions> = (args: ArgumentsCamelCase<T>) => any

export type CommandDefinition<T = GlobalOptions> = CommandOptions<T> | CommandHandler<T>
