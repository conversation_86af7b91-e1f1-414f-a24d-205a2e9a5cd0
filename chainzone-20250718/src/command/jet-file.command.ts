import { createWriteStream, mkdirSync, statSync } from 'fs'
import ora from 'ora'
import { dirname, join, resolve } from 'path'
import { getBaseName } from '../protocol/fs/utils'
import { ConsoleError } from './error'
import { defineJetCommand } from './jet.command'

export default defineJetCommand({
    describe: '从信号机下载文件',
    positional: '<path>',
    builder: (app) => app
        .positional('path', {
            type: 'string',
            demandOption: true
        })
        .option('output', {
            alias: ['O'],
            type: 'string',
        }),
    handler: async (client, args) => {
        const { path, output } = args
        const spinner = ora(path)
        const buffer = await client.readFile(path, null, {
            chunkCallback: (x) => {
                const percent = Math.floor(x.readSize / x.fileSize * 100)
                process.stdout.write('\r')
                if (percent >= 100) {
                    spinner.succeed(path + '        ')
                } else {
                    process.stdout.write(spinner.frame() + `  ${percent}%  `)
                }
            }
        })

        const stream = output != null
            ? prepareOutputStream(output, () => getBaseName(path, true))
            : process.stdout

        stream.write(buffer, (err) => {
            stream.end()
            if (err != null) {
                throw err
            }
        })
    }
})

export function prepareOutputStream(output?: string, createFileName?: () => string) {
    output = (output ?? process.cwd())
        .replace(/\/+/, '/')

    let stat = statSync(output, { throwIfNoEntry: false })
    if (stat == null) {
        const dir = output.endsWith('/') ? output : dirname(output)
        mkdirSync(dir, { recursive: true })
        stat = statSync(output, { throwIfNoEntry: false })
    }

    if (stat?.isDirectory()) {
        const fileName = createFileName?.()
        if (!fileName) {
            throw new ConsoleError('Failed to automatic resolve the output file name.')
        }

        output = join(output, fileName)
    }

    const result = createWriteStream(output, 'binary')
    result.on('close', () => {
        process.stderr.write('\nSAVED: \n  ' + resolve(output) + '\n\n')
    })

    return result
}
