import { CutOff, Extend, Manual, ManualType } from '../protocol/control'
import type { Request, StatusResponse } from '../transport'
import { ConsoleError } from './error'
import { defineJetCommand } from './jet.command'

function isNumeric(val: string) {
    return /^[\d.]+$/.test(val)
}

function snakeCase(val: string) {
    return val.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()
}

const manualTypes = Object.entries(ManualType)
    .filter(([k]) => !isNumeric(k))
    .reduce(
        (res, [k, v]) => (res[snakeCase(k)] = v as any, res),
        {} as Record<string, ManualType>
    )

const controlChoices = ['extend', 'cut-off', ...Object.keys(manualTypes)]

export default defineJetCommand({
    describe: '手动控制，支持跳步/跳相，黄闪/全红等操作',
    positional: '[type] [time]',
    builder: (app) => {
        return app
            .positional('type', {
                type: 'string',
                // choices: controlChoices,
                default: 'automatic',
            })
            .positional('time', {
                type: 'number'
            })
    },
    handler: async (client, args) => {
        const type = args.type.toLowerCase()
        const time = Number(args.time || undefined)

        let request: Request<StatusResponse> | undefined
        const manualType = manualTypes[type]
        if (typeof manualType === 'number') {
            const req = new Manual(manualType)
            if (manualType !== ManualType.Automatic && time > 0) {
                req.timeout = time
            }
            request = req
        } else if (type === 'extend') {
            if (time > 0) {
                request = new Extend(time)
            } else {
                throw new ConsoleError('Invalid extend value: ' + String(args.time))
            }

        } else if (type === 'cut-off') {
            request = new CutOff()
        }

        if (request == null) {
            throw new ConsoleError(`Invalid type value: ${String(args.type)}, supported: \n\n${controlChoices.join('/')}\n`)
        }

        const response = await client.requestRaw(request)
        return response.statusMessage
    }
})
