import { format } from 'util'
import { Discover } from '../protocol/discover'
import { TimeoutError } from '../utils'
import { defineJetCommand } from './jet.command'

/** Windows 操作系统下工作可能不正常，需要明确指定 --address=************* */
const jetDiscoverCommand = defineJetCommand({
    describe: '从局域网搜索信号机',
    builder: (app) => {
        app.option('address', {
            default: '***************'
        })
    },
    handler: async (client, args) => {
        client.on('open', e => {
            e.socket.setBroadcast(true)
        })

        const req = new Discover()
        try {
            await client.requestMultiple(req, (e) => {
                const { address, port } = e.remote
                const content = {
                    url: format('udp://%s:%d', address, port),
                    ...e.response,
                }
                console.log(args.json
                    ? JSON.stringify(content, null, 4)
                    : content
                )
                return 100
            })
        } catch (err) {
            if (!(err instanceof TimeoutError)) {
                throw err
            }
        }
    }
})



export default jetDiscoverCommand
