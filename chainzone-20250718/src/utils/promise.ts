export type Resolver<T, A extends any[] = []> = (...args: A) => T | Promise<T>

export type TimeoutPromise<T = any> = Promise<T> & {
    clear(): void
    reset(ms?: number): void
}

export async function asyncDelay(timeout: number, abort?: AbortController) {
    return new Promise<void>((resolve, reject) => {
        const timer = setTimeout(() => resolve(), timeout)
        if (abort != null) {
            abort.signal.onabort = function() {
                clearTimeout(timer)
                const { reason } = this
                if (reason instanceof Error) {
                    reject(reason)
                } else {
                    resolve()
                }
            }
        }
    })
}

export function asyncTimeout<T>(resolver: Resolver<T>, ms: number): TimeoutPromise<T> {
    let clear: TimeoutPromise['clear']
    let reset: TimeoutPromise['reset']

    const result = new Promise<T>((resolve, reject) => {
        let timer: NodeJS.Timeout
        clear = () => clearTimeout(timer)

        function setup(timeoutMs: number) {
            clear()
            timer = setTimeout(
                () => reject(new TimeoutError(timeoutMs)),
                timeoutMs
            )
        }

        reset = x => x == null
            ? timer.refresh() : setup(x)

        setup(ms)
        Promise.resolve(resolver())
            .then(resolve)
            .catch(reject)
            .finally(clear)
    }) as TimeoutPromise<T>

    result.clear = () => clear()
    result.reset = x => reset(x)

    return result
}

export class TimeoutError extends Error {
    constructor(message: string | number) {
        if (typeof message === 'number') {
            message = `Timeout period elapsed: ${message}(ms)`
        }

        super(message)
    }
}
