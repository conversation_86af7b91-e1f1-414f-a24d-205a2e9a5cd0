/**
 * 59 => 0x59
 * 2024 => 0x2024
 */
export function bcdEncode(val: number) {
    let result = 0
    let offset = 0
    while (val > 0) {
        const item = val % 100
        val = Math.floor(val / 100)

        const ones = item % 10
        const tens = Math.floor(item / 10)

        result |= ((tens << 4) | ones) << (offset++ * 8)
    }

    return result
}

/**
 * 0x59 => 59
 * 0x2024 => 2024
 */
export function bcdDecode(val: number) {
    let result = 0
    let offset = 0
    while (val > 0) {
        const item = val & 0xff

        const ones = item & 0xf
        const tens = (item >> 4) & 0xf
        if (ones > 0x9 || tens > 0x9) {
            return null
        }

        result += (tens * 10 + ones) * Math.pow(100, offset++)

        val = val >> 8
    }

    return result
}
