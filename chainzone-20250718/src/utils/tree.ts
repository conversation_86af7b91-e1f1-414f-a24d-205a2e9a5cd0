import type { Awaitable } from '../types'

export interface TreeNode {
    readonly children?: this[] | null
}

type Printer<T> = (text: string, node: T) => Awaitable<boolean | void>
export function treePrinter<T extends TreeNode = TreeNode>(printer?: Printer<T>) {
    printer = printer ?? (v => console.log(v))

    type Formatter<T> = (v: T) => unknown
    return async function print<U extends T>(node: U, format: Formatter<U>, indent = '') {
        const result = await printer(indent + String(format(node)), node)
        if (result === false) return;

        const items = node.children ?? []
        for (let i = 0; i < items.length; i++) {
            const x = items[i];

            const isLast = i >= items.length - 1
            const prefix = isLast ? '└' : '├'
            const indentLocal = indent
                .replace(/├/g, '│')
                .replace(/[└─]/g, ' ')
                + prefix
                + '── '

            await print(x, format, indentLocal)
        }
    }
}
