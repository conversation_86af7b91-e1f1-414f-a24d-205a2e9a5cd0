export function hexEncode(value: number, byteCount = 1, prefix = '') {
    const hex = value.toString(16)

    let bc = Math.ceil(hex.length / 2)
    if (byteCount > bc) {
        bc = byteCount
    }

    return `${prefix}${hex.padStart(bc * 2, '0')}`
}

export function hexDecode(value: string) {
    let val = String(value).toLocaleLowerCase()
    if (!val.startsWith('0x')) {
        val = '0x' + val
    }

    const result = Number.parseInt(val)
    return Number.isNaN(result) ? null : result
}
