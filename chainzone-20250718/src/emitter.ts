import mitt, { type EventType, type Handler, type <PERSON>cardHandler } from 'mitt'
import type { Fn } from './types'

export type Off = () => void
export type Events = Record<EventType, unknown>

/** 对 mitt 的封装，让普通对象支持事件通知 */
export abstract class Emitter<T extends Events = Events> {
    private readonly emitter = mitt<T>()

    on(event: '*', handler: WildcardHandler<T>): Off
    on<E extends keyof T>(event: E, handler: Handler<T[E]>): Off
    on(event: any, handler: Fn) {
        const { emitter } = this

        const theHandler = handler.bind(this)
        emitter.on(event, theHandler)

        return () => {
            emitter.off(event, theHandler)
        }
    }

    once(event: '*', handler: WildcardHandler<T>): Off
    once<E extends keyof T>(event: E, handler: Handler<T[E]>): Off
    once(event: any, handler: Fn) {
        const off = this.on(event, (...args) => {
            off()
            handler.call(this, ...args)
        })

        return off
    }

    protected emit<E extends keyof T>(event: T[E] extends undefined ? E : never): void
    protected emit<E extends keyof T>(event: E, payload: T[E]): void
    protected emit(event: any, payload?: any) {
        this.emitter.emit(event, payload)
    }
}
