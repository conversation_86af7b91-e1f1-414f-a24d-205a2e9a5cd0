{"name": "chainzone", "version": "1.0.0", "description": "", "private": true, "main": "dist/index.js", "scripts": {"cli": "tsx src/index.ts", "dev": "tsx watch --include ./package.json --include \"./src/**/*.ts\" src/index.ts", "build": "rimraf ./dist && tsc -b --force tsconfig.build.json", "lint": "eslint src/", "test": "vitest --testTimeout=0"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.8.1", "devDependencies": {"@eslint/js": "^9.31.0", "@types/archiver": "^6.0.3", "@types/node": "^20.19.7", "@types/yargs": "^17.0.33", "eslint": "^9.31.0", "globals": "^16.3.0", "rimraf": "^6.0.1", "rollup-plugin-typescript2": "^0.36.0", "tslib": "^2.8.1", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vitest": "^3.2.4"}, "dependencies": {"archiver": "^7.0.1", "dayjs": "^1.11.13", "dotenv": "^17.2.0", "glob": "^11.0.3", "mitt": "^3.0.1", "ora": "^5.4.1", "yargs": "^17.7.2"}}