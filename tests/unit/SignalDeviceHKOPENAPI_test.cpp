//
// Created for testing SignalDeviceHKOPENAPI
//

#include "components/signal_controllers/SignalDevices/SignalDeviceHKOPENAPI.hpp"
#include <iostream>
#include <thread>
#include <chrono>
#include <memory>
#include <string>
#include <vector>

// 测试配置
const std::string TEST_CONFIG_JSON = R"({
    "signalCtl": {
        "platformUrl": "127.0.0.1:8080",
        "appkey": "test_appkey",
        "secret": "test_secret",
        "crossCode": "test_crossCode",
        "crossName": "test_crossName",
        "userID": "test_userID",
        "schemes": [
            [0, 1, 2, 3, 4]
        ],
        "defaultPlan": 1,
        "lockPhaseChannelMap": {
            "1": [1, 2, 3],
            "2": [4, 5, 6],
            "3": [7, 8, 9]
        }
    }
})";

// 测试结果状态
enum TestResult {
    PASS,
    FAIL,
    SKIP
};

// 打印测试结果
void printTestResult(const std::string& testName, TestResult result) {
    std::cout << "Test: " << testName << " - ";
    switch (result) {
        case PASS:
            std::cout << "\033[32mPASS\033[0m" << std::endl;
            break;
        case FAIL:
            std::cout << "\033[31mFAIL\033[0m" << std::endl;
            break;
        case SKIP:
            std::cout << "\033[33mSKIP\033[0m" << std::endl;
            break;
    }
}

// 测试初始化连接
TestResult testInitialization() {
    try {
        std::cout << "Testing initialization..." << std::endl;
        
        // 创建SignalCtl对象
        SignalCtl signalCtl;
        signalCtl.fromJson(TEST_CONFIG_JSON);
        
        // 创建SignalDeviceHKOPENAPI对象
        SignalDeviceHKOPENAPI device(signalCtl);
        
        // 检查设备状态
        auto state = device.getCurDeviceState();
        std::cout << "  Device state after initialization: " << ISignalDevice::toString(state) << std::endl;
        
        // 由于我们使用的是模拟环境，可能无法真正连接，所以这里主要测试是否能创建对象而不抛出异常
        return PASS;
    } catch (const std::exception& e) {
        std::cerr << "  Exception during initialization: " << e.what() << std::endl;
        return FAIL;
    }
}

// 测试相位控制
TestResult testPhaseControl() {
    try {
        std::cout << "Testing phase control..." << std::endl;
        
        // 创建SignalCtl对象
        SignalCtl signalCtl;
        signalCtl.fromJson(TEST_CONFIG_JSON);
        
        // 创建SignalDeviceHKOPENAPI对象
        SignalDeviceHKOPENAPI device(signalCtl);
        
        // 测试设置不同相位
        std::cout << "  Setting phase to 1..." << std::endl;
        device.controlSignal(1);
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        std::cout << "  Setting phase to 2..." << std::endl;
        device.controlSignal(2);
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        std::cout << "  Setting phase to 3..." << std::endl;
        device.controlSignal(3);
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        return PASS;
    } catch (const std::exception& e) {
        std::cerr << "  Exception during phase control: " << e.what() << std::endl;
        return FAIL;
    }
}

// 测试状态查询
TestResult testStateQuery() {
    try {
        std::cout << "Testing state query..." << std::endl;
        
        // 创建SignalCtl对象
        SignalCtl signalCtl;
        signalCtl.fromJson(TEST_CONFIG_JSON);
        
        // 创建SignalDeviceHKOPENAPI对象
        SignalDeviceHKOPENAPI device(signalCtl);
        
        // 查询状态
        std::cout << "  Querying signal state..." << std::endl;
        SignalEnvState state = device.getSignalState();
        
        // 打印状态信息
        std::cout << "  Current phase: " << state.currentPhase << std::endl;
        std::cout << "  Phase time: " << state.phaseTime << std::endl;
        std::cout << "  Signal control status: " << (state.signalCtlStatus ? "true" : "false") << std::endl;
        std::cout << "  Current plan: " << state.currentPlan << std::endl;
        
        return PASS;
    } catch (const std::exception& e) {
        std::cerr << "  Exception during state query: " << e.what() << std::endl;
        return FAIL;
    }
}

// 测试重连功能
TestResult testReconnection() {
    try {
        std::cout << "Testing reconnection..." << std::endl;
        
        // 创建SignalCtl对象
        SignalCtl signalCtl;
        signalCtl.fromJson(TEST_CONFIG_JSON);
        
        // 创建SignalDeviceHKOPENAPI对象
        SignalDeviceHKOPENAPI device(signalCtl);
        
        // 测试重连
        std::cout << "  Attempting reconnection with 3 second wait..." << std::endl;
        bool reconnectResult = device.reconnect(3);
        
        std::cout << "  Reconnection result: " << (reconnectResult ? "Success" : "Failure") << std::endl;
        
        // 设置重连阈值
        std::cout << "  Setting reconnect threshold to 30 seconds..." << std::endl;
        device.setReconnectThreshold(30);
        
        return PASS;
    } catch (const std::exception& e) {
        std::cerr << "  Exception during reconnection: " << e.what() << std::endl;
        return FAIL;
    }
}

// 测试错误处理
TestResult testErrorHandling() {
    try {
        std::cout << "Testing error handling..." << std::endl;
        
        // 创建带有无效配置的SignalCtl对象
        SignalCtl signalCtl;
        signalCtl.fromJson(R"({
            "signalCtl": {
                "platformUrl": "invalid_url",
                "appkey": "",
                "secret": "",
                "crossCode": "",
                "crossName": "",
                "userID": "",
                "schemes": [],
                "defaultPlan": 0
            }
        })");
        
        // 创建SignalDeviceHKOPENAPI对象
        SignalDeviceHKOPENAPI device(signalCtl);
        
        // 尝试控制信号
        std::cout << "  Attempting to control signal with invalid configuration..." << std::endl;
        device.controlSignal(1);
        
        // 尝试查询状态
        std::cout << "  Attempting to query state with invalid configuration..." << std::endl;
        SignalEnvState state = device.getSignalState();
        
        return PASS;
    } catch (const std::exception& e) {
        std::cerr << "  Exception during error handling test: " << e.what() << std::endl;
        // 在这个测试中，异常可能是预期的行为
        return PASS;
    }
}

// 测试复位功能
TestResult testReset() {
    try {
        std::cout << "Testing reset functionality..." << std::endl;
        
        // 创建SignalCtl对象
        SignalCtl signalCtl;
        signalCtl.fromJson(TEST_CONFIG_JSON);
        
        // 创建SignalDeviceHKOPENAPI对象
        SignalDeviceHKOPENAPI device(signalCtl);
        
        // 先设置一个相位
        std::cout << "  Setting phase to 2..." << std::endl;
        device.controlSignal(2);
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 然后复位
        std::cout << "  Resetting signal device..." << std::endl;
        bool resetResult = device.reset();
        
        std::cout << "  Reset result: " << (resetResult ? "Success" : "Failure") << std::endl;
        
        // 检查设备状态
        auto state = device.getCurDeviceState();
        std::cout << "  Device state after reset: " << ISignalDevice::toString(state) << std::endl;
        
        return PASS;
    } catch (const std::exception& e) {
        std::cerr << "  Exception during reset test: " << e.what() << std::endl;
        return FAIL;
    }
}

// 测试控制模式设置
TestResult testControlMode() {
    try {
        std::cout << "Testing control mode setting..." << std::endl;
        
        // 创建SignalCtl对象
        SignalCtl signalCtl;
        signalCtl.fromJson(TEST_CONFIG_JSON);
        
        // 创建SignalDeviceHKOPENAPI对象
        SignalDeviceHKOPENAPI device(signalCtl);
        
        // 设置控制模式为手动控制
        std::cout << "  Setting control mode to manual (1)..." << std::endl;
        bool manualResult = device.setControlMode(1);
        std::cout << "  Manual control mode result: " << (manualResult ? "Success" : "Failure") << std::endl;
        
        // 设置控制模式为自动控制
        std::cout << "  Setting control mode to automatic (0)..." << std::endl;
        bool autoResult = device.setControlMode(0);
        std::cout << "  Automatic control mode result: " << (autoResult ? "Success" : "Failure") << std::endl;
        
        return PASS;
    } catch (const std::exception& e) {
        std::cerr << "  Exception during control mode test: " << e.what() << std::endl;
        return FAIL;
    }
}

// 主测试函数
int SignalDeviceHKOPENAPI_Test() {
    std::cout << "\n===== SignalDeviceHKOPENAPI Test Suite =====\n" << std::endl;
    
    // 运行所有测试
    std::vector<std::pair<std::string, TestResult>> results;
    
    results.push_back({"Initialization", testInitialization()});
    results.push_back({"Phase Control", testPhaseControl()});
    results.push_back({"State Query", testStateQuery()});
    results.push_back({"Reconnection", testReconnection()});
    results.push_back({"Error Handling", testErrorHandling()});
    results.push_back({"Reset Functionality", testReset()});
    results.push_back({"Control Mode", testControlMode()});
    
    // 打印测试结果摘要
    std::cout << "\n===== Test Results Summary =====\n" << std::endl;
    int passCount = 0;
    int failCount = 0;
    int skipCount = 0;
    
    for (const auto& result : results) {
        printTestResult(result.first, result.second);
        
        switch (result.second) {
            case PASS: passCount++; break;
            case FAIL: failCount++; break;
            case SKIP: skipCount++; break;
        }
    }
    
    std::cout << "\nTotal: " << results.size() << ", Pass: " << passCount 
              << ", Fail: " << failCount << ", Skip: " << skipCount << std::endl;
    
    return (failCount == 0) ? 0 : 1;
}
