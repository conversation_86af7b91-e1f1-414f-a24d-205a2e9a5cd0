//
// Created for testing SignalDeviceHKOPENAPI with mock server
//

#include "components/signal_controllers/SignalDevices/SignalDeviceHKOPENAPI.hpp"
#include "MockHttpServer.hpp"
#include <iostream>
#include <thread>
#include <chrono>
#include <memory>
#include <string>
#include <vector>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

// 测试结果状态
enum TestResult {
    PASS,
    FAIL,
    SKIP
};

// 打印测试结果
void printMockTestResult(const std::string& testName, TestResult result) {
    std::cout << "Mock Test: " << testName << " - ";
    switch (result) {
        case PASS:
            std::cout << "\033[32mPASS\033[0m" << std::endl;
            break;
        case FAIL:
            std::cout << "\033[31mFAIL\033[0m" << std::endl;
            break;
        case SKIP:
            std::cout << "\033[33mSKIP\033[0m" << std::endl;
            break;
    }
}

// 使用模拟服务器测试信号机接口
int SignalDeviceHKOPENAPI_MockTest() {
    std::cout << "\n===== SignalDeviceHKOPENAPI Mock Test Suite =====\n" << std::endl;
    
    // 创建模拟HTTP服务器
    MockHttpServer server(8080);
    
    // 设置默认响应
    server.setDefaultResponse(R"({
        "code": "0",
        "msg": "Success",
        "data": {}
    })");
    
    // 添加路由处理函数
    
    // 1. 查询状态接口
    server.addRoute("/artemis/api/itscms-scms/v1/crossstate/getRealtimeSchemeStateInfo", [](const std::string& body) {
        return R"({
            "code": "0",
            "msg": "Success",
            "data": {
                "list": [
                    {
                        "controlType": 10,
                        "lockPhases": [2],
                        "rings": [
                            {
                                "phaseList": [
                                    {
                                        "phaseNo": 1,
                                        "phaseLength": 30,
                                        "runTime": 0
                                    },
                                    {
                                        "phaseNo": 2,
                                        "phaseLength": 30,
                                        "runTime": 15
                                    }
                                ]
                            }
                        ],
                        "channelState": [
                            {
                                "channelNo": 1,
                                "state": 1
                            },
                            {
                                "channelNo": 2,
                                "state": 1
                            },
                            {
                                "channelNo": 3,
                                "state": 1
                            }
                        ]
                    }
                ]
            }
        })";
    });
    
    // 2. 设置信号控制接口
    server.addRoute("/artemis/api/itscms-scms/v1/signalcontrol/SetSignalControl", [](const std::string& body) {
        json requestBody = json::parse(body);
        int controlType = requestBody["controlType"].get<int>();
        int controlNo = requestBody["controlNo"].get<int>();
        
        return R"({
            "code": "0",
            "msg": "Success",
            "data": {
                "controlType": )" + std::to_string(controlType) + R"(,
                "controlNo": )" + std::to_string(controlNo) + R"(
            }
        })";
    });
    
    // 3. 设置通道锁定接口
    server.addRoute("/artemis/api/itscms-scms/v1/signalcontrol/setLockFlow", [](const std::string& body) {
        json requestBody = json::parse(body);
        int operaType = requestBody["operaType"].get<int>();
        
        return R"({
            "code": "0",
            "msg": "Success",
            "data": {
                "operaType": )" + std::to_string(operaType) + R"(
            }
        })";
    });
    
    // 启动服务器
    if (!server.start()) {
        std::cerr << "Failed to start mock server" << std::endl;
        return 1;
    }
    
    // 等待服务器启动
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    // 测试结果集合
    std::vector<std::pair<std::string, TestResult>> results;
    
    try {
        // 创建测试配置
        std::string testConfig = R"({
            "signalCtl": {
                "platformUrl": "127.0.0.1:8080",
                "appkey": "test_appkey",
                "secret": "test_secret",
                "crossCode": "test_crossCode",
                "crossName": "test_crossName",
                "userID": "test_userID",
                "schemes": [
                    [0, 1, 2, 3, 4]
                ],
                "defaultPlan": 1,
                "lockPhaseChannelMap": {
                    "1": [1, 2, 3],
                    "2": [4, 5, 6],
                    "3": [7, 8, 9]
                }
            }
        })";
        
        // 创建SignalCtl对象
        SignalCtl signalCtl;
        signalCtl.fromJson(testConfig);
        
        // 创建SignalDeviceHKOPENAPI对象
        SignalDeviceHKOPENAPI device(signalCtl);
        
        // 测试1: 查询状态
        std::cout << "Testing state query with mock server..." << std::endl;
        SignalEnvState state = device.getSignalState();
        std::cout << "  Current phase: " << state.currentPhase << std::endl;
        std::cout << "  Phase time: " << state.phaseTime << std::endl;
        std::cout << "  Signal control status: " << (state.signalCtlStatus ? "true" : "false") << std::endl;
        
        // 检查状态是否符合预期
        bool stateQueryPassed = (state.currentPhase == 2); // 模拟服务器返回的相位是2
        results.push_back({"State Query", stateQueryPassed ? PASS : FAIL});
        
        // 测试2: 设置相位
        std::cout << "Testing phase control with mock server..." << std::endl;
        server.clearRequestHistory();
        device.controlSignal(1);
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 检查是否发送了正确的请求
        auto history = server.getRequestHistory();
        bool phaseControlPassed = false;
        for (const auto& request : history) {
            if (request.find("SetSignalControl") != std::string::npos && 
                request.find("\"controlNo\":1") != std::string::npos) {
                phaseControlPassed = true;
                break;
            }
        }
        results.push_back({"Phase Control", phaseControlPassed ? PASS : FAIL});
        
        // 测试3: 重置控制模式
        std::cout << "Testing reset with mock server..." << std::endl;
        server.clearRequestHistory();
        device.reset();
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 检查是否发送了正确的请求
        history = server.getRequestHistory();
        bool resetPassed = false;
        for (const auto& request : history) {
            if (request.find("SetSignalControl") != std::string::npos && 
                request.find("\"controlType\":11") != std::string::npos) {
                resetPassed = true;
                break;
            }
        }
        results.push_back({"Reset", resetPassed ? PASS : FAIL});
        
        // 测试4: 重连功能
        std::cout << "Testing reconnection with mock server..." << std::endl;
        bool reconnectResult = device.reconnect(1);
        results.push_back({"Reconnection", reconnectResult ? PASS : FAIL});
        
    } catch (const std::exception& e) {
        std::cerr << "Exception during mock tests: " << e.what() << std::endl;
        server.stop();
        return 1;
    }
    
    // 停止服务器
    server.stop();
    
    // 打印测试结果摘要
    std::cout << "\n===== Mock Test Results Summary =====\n" << std::endl;
    int passCount = 0;
    int failCount = 0;
    int skipCount = 0;
    
    for (const auto& result : results) {
        printMockTestResult(result.first, result.second);
        
        switch (result.second) {
            case PASS: passCount++; break;
            case FAIL: failCount++; break;
            case SKIP: skipCount++; break;
        }
    }
    
    std::cout << "\nTotal: " << results.size() << ", Pass: " << passCount 
              << ", Fail: " << failCount << ", Skip: " << skipCount << std::endl;
    
    return (failCount == 0) ? 0 : 1;
}
