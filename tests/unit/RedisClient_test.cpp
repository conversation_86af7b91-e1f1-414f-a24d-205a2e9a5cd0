//
// Created by x<PERSON><PERSON><PERSON> on 7/5/24.
//
#include "utils/RedisClient.hpp"
#include <thread>
#include <iostream>


// 声明一个全局变量来跟踪消息是否被接收
std::atomic<bool> g_messageReceived(false);

// 单独声明订阅回调函数
void subscriptionCallback(const std::string& channel, const std::string& message) {
    std::cout << "Received message from " << channel << ": " << message << std::endl;
    g_messageReceived = true;
}


int RedisClient_Test() {
    try {
        RedisClient redis("*************", 6379, "jyg2021");

        // 测试基本操作
        redis.set("testkey", "Hello, Redis!");
        auto value = redis.get("testkey");
        if (value) {
            std::cout << "Get value: " << *value << std::endl;
        } else {
            std::cout << "Failed to get value" << std::endl;
        }

        // 测试删除操作
        if (redis.del("testkey")) {
            std::cout << "Key deleted successfully" << std::endl;
        } else {
            std::cout << "Failed to delete key" << std::endl;
        }

        // 测试exists操作
        if (redis.exists("testkey")) {
            std::cout << "Key still exists (unexpected)" << std::endl;
        } else {
            std::cout << "Key does not exist (as expected)" << std::endl;
        }

        // 测试事务操作
        redis.multi();
        redis.set("key1", "value1");
        redis.set("key2", "value2");
        redis.exec();

        // 验证事务结果
        auto value1 = redis.get("key1");
        auto value2 = redis.get("key2");
        if (value1 && value2) {
            std::cout << "Transaction successful. key1: " << *value1 << ", key2: " << *value2 << std::endl;
        } else {
            std::cout << "Transaction failed" << std::endl;
        }

        // 测试发布订阅
        redis.subscribe("testchannel", subscriptionCallback);

        // 等待一段时间以确保订阅已经建立
        std::this_thread::sleep_for(std::chrono::seconds(1));

        // 发布消息
        redis.publish("testchannel", "Hello, subscribers!");

        // 等待一段时间以接收消息
        std::this_thread::sleep_for(std::chrono::seconds(2));

        // 验证是否收到消息
        if (g_messageReceived) {
            std::cout << "Publish/Subscribe test passed" << std::endl;
        } else {
            std::cout << "Publish/Subscribe test failed" << std::endl;
        }

        // 取消所有订阅
        redis.unsubscribeAll();

        std::cout << "All tests completed." << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
    }
    return 0;
}
