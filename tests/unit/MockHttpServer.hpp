//
// Created for testing SignalDeviceHKOPENAPI
//

#ifndef MOCK_HTTP_SERVER_HPP
#define MOCK_HTTP_SERVER_HPP

#include <iostream>
#include <string>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <map>
#include <vector>
#include <cstring>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    typedef SOCKET socket_t;
    #define CLOSE_SOCKET closesocket
#else
    #include <unistd.h>
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    typedef int socket_t;
    #define CLOSE_SOCKET close
#endif

class MockHttpServer {
public:
    // 构造函数，指定端口
    MockHttpServer(int port = 8080) : m_port(port), m_running(false), m_socket(-1) {
#ifdef _WIN32
        WSADATA wsaData;
        WSAStartup(MAKEWORD(2, 2), &wsaData);
#endif
    }

    // 析构函数
    ~MockHttpServer() {
        stop();
#ifdef _WIN32
        WSACleanup();
#endif
    }

    // 启动服务器
    bool start() {
        if (m_running) {
            return true;
        }

        // 创建套接字
        m_socket = socket(AF_INET, SOCK_STREAM, 0);
        if (m_socket < 0) {
            std::cerr << "Failed to create socket" << std::endl;
            return false;
        }

        // 设置地址重用
        int opt = 1;
        if (setsockopt(m_socket, SOL_SOCKET, SO_REUSEADDR, (const char*)&opt, sizeof(opt)) < 0) {
            std::cerr << "Failed to set socket options" << std::endl;
            CLOSE_SOCKET(m_socket);
            return false;
        }

        // 绑定地址
        struct sockaddr_in address;
        address.sin_family = AF_INET;
        address.sin_addr.s_addr = INADDR_ANY;
        address.sin_port = htons(m_port);

        if (bind(m_socket, (struct sockaddr*)&address, sizeof(address)) < 0) {
            std::cerr << "Failed to bind socket" << std::endl;
            CLOSE_SOCKET(m_socket);
            return false;
        }

        // 监听
        if (listen(m_socket, 5) < 0) {
            std::cerr << "Failed to listen on socket" << std::endl;
            CLOSE_SOCKET(m_socket);
            return false;
        }

        // 启动服务器线程
        m_running = true;
        m_serverThread = std::thread(&MockHttpServer::serverLoop, this);

        std::cout << "Mock HTTP server started on port " << m_port << std::endl;
        return true;
    }

    // 停止服务器
    void stop() {
        if (!m_running) {
            return;
        }

        m_running = false;
        
        // 关闭套接字
        if (m_socket >= 0) {
            CLOSE_SOCKET(m_socket);
            m_socket = -1;
        }

        // 等待服务器线程结束
        if (m_serverThread.joinable()) {
            m_serverThread.join();
        }

        std::cout << "Mock HTTP server stopped" << std::endl;
    }

    // 添加路由处理函数
    void addRoute(const std::string& path, std::function<std::string(const std::string&)> handler) {
        std::lock_guard<std::mutex> lock(m_routesMutex);
        m_routes[path] = handler;
    }

    // 设置默认响应
    void setDefaultResponse(const std::string& response) {
        std::lock_guard<std::mutex> lock(m_routesMutex);
        m_defaultResponse = response;
    }

    // 获取请求历史
    std::vector<std::string> getRequestHistory() {
        std::lock_guard<std::mutex> lock(m_historyMutex);
        return m_requestHistory;
    }

    // 清除请求历史
    void clearRequestHistory() {
        std::lock_guard<std::mutex> lock(m_historyMutex);
        m_requestHistory.clear();
    }

private:
    // 服务器主循环
    void serverLoop() {
        while (m_running) {
            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(m_socket, &readSet);

            struct timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;

            int activity = select(m_socket + 1, &readSet, NULL, NULL, &timeout);

            if (activity < 0 && errno != EINTR) {
                std::cerr << "Select error" << std::endl;
                break;
            }

            if (activity == 0) {
                // 超时，继续循环
                continue;
            }

            if (FD_ISSET(m_socket, &readSet)) {
                struct sockaddr_in clientAddr;
                socklen_t clientAddrLen = sizeof(clientAddr);
                socket_t clientSocket = accept(m_socket, (struct sockaddr*)&clientAddr, &clientAddrLen);

                if (clientSocket < 0) {
                    std::cerr << "Accept failed" << std::endl;
                    continue;
                }

                // 处理客户端请求
                handleClient(clientSocket);
            }
        }
    }

    // 处理客户端请求
    void handleClient(socket_t clientSocket) {
        char buffer[4096] = {0};
        int bytesRead = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);

        if (bytesRead <= 0) {
            CLOSE_SOCKET(clientSocket);
            return;
        }

        // 解析HTTP请求
        std::string request(buffer);
        
        // 记录请求历史
        {
            std::lock_guard<std::mutex> lock(m_historyMutex);
            m_requestHistory.push_back(request);
        }

        // 解析请求路径
        std::string path;
        std::string method;
        std::string body;

        // 提取HTTP方法
        size_t methodEnd = request.find(' ');
        if (methodEnd != std::string::npos) {
            method = request.substr(0, methodEnd);
            
            // 提取路径
            size_t pathStart = methodEnd + 1;
            size_t pathEnd = request.find(' ', pathStart);
            if (pathEnd != std::string::npos) {
                path = request.substr(pathStart, pathEnd - pathStart);
            }
        }

        // 提取请求体
        size_t bodyStart = request.find("\r\n\r\n");
        if (bodyStart != std::string::npos) {
            body = request.substr(bodyStart + 4);
        }

        // 查找路由处理函数
        std::string response;
        {
            std::lock_guard<std::mutex> lock(m_routesMutex);
            auto it = m_routes.find(path);
            if (it != m_routes.end()) {
                response = it->second(body);
            } else {
                response = m_defaultResponse;
            }
        }

        // 构造HTTP响应
        std::string httpResponse = "HTTP/1.1 200 OK\r\n";
        httpResponse += "Content-Type: application/json\r\n";
        httpResponse += "Content-Length: " + std::to_string(response.length()) + "\r\n";
        httpResponse += "Connection: close\r\n";
        httpResponse += "\r\n";
        httpResponse += response;

        // 发送响应
        send(clientSocket, httpResponse.c_str(), httpResponse.length(), 0);
        
        // 关闭客户端套接字
        CLOSE_SOCKET(clientSocket);
    }

    int m_port;
    bool m_running;
    socket_t m_socket;
    std::thread m_serverThread;
    std::mutex m_routesMutex;
    std::mutex m_historyMutex;
    std::map<std::string, std::function<std::string(const std::string&)>> m_routes;
    std::string m_defaultResponse;
    std::vector<std::string> m_requestHistory;
};

#endif // MOCK_HTTP_SERVER_HPP
