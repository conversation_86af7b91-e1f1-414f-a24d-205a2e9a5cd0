cmake_minimum_required(VERSION 3.10)

add_executable(unit_tests
	unit/main.cpp
	unit/RedisClient_test.cpp
	unit/VisionProcess_test.cpp
	unit/SignalDeviceHKOPENAPI_test.cpp
	unit/SignalDeviceHKOPENAPI_mock_test.cpp
	unit/MockHttpServer.hpp
    # 添加其他测试文件
)


include_directories(
		${PROJECT_SOURCE_DIR}/src
)


find_package(Threads REQUIRED)
target_link_libraries(unit_tests STNCore STNUtils hiredis event STNVisionProcess Threads::Threads)

#add_test(NAME UnitTests COMMAND unit_tests)