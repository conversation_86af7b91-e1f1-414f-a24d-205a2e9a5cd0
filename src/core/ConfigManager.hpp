#ifndef STN_CONFIGMANAGER_HPP
#define STN_CONFIGMANAGER_HPP
#include <iostream>
#include <functional>
#include <unordered_map>
#include "tinyxml2.h"

struct ConfigData {
    std::string logLevel;
    std::string msgCenterUrl;
    std::string redisServiceIp;
    std::string redisServicePort;
    std::string redisServicePwd;
    std::string redisServiceGatherDb;
    std::string redisServiceStnDb;
    std::string redisServiceResultDb;

    std::string localRedisIp;
    std::string localRedisPort;
    std::string localRedisPwd;
    std::string localRedisGatherDb;
    std::string localRedisStnDb;
    std::string localRedisResultDb;

    std::string localHostIp;
    std::string intersectionDevicesKey;
    std::string originStateKey;
    std::string algConfigKey;
    std::string sensorConfigKey;
    std::string signalConfigKey;

    std::string algConfigFileDir;
    std::string algPhaseErrorThreshold;
};

inline ConfigData configData;

enum class ConfigItem {
    LogLevel,
    MsgCenterUrl,
    RedisServiceIp,
    RedisServicePort,
    RedisServicePwd,
    RedisServiceGatherDb,
    RedisServiceStnDb,
    RedisServiceResultDb,
    LocalRedisIp,
    LocalRedisPort,
    LocalRedisPwd,
    LocalRedisGatherDb,
    LocalRedisStnDb,
    LocalRedisResultDb,
    LocalHostIp,
    IntersectionDevicesKey,
    OriginStateKey,
    AlgConfigKey,
    SensorConfigKey,
    SignalConfigKey,
    AlgConfigFileDir,
    AlgPhaseErrorThreshold
};

const std::unordered_map<ConfigItem, const char*> configItemToString = {
    { ConfigItem::LogLevel, "logLevel" },
    { ConfigItem::MsgCenterUrl, "msgCenterUrl" },
    { ConfigItem::RedisServiceIp, "redisServiceIp" },
    { ConfigItem::RedisServicePort, "redisServicePort" },
    { ConfigItem::RedisServicePwd, "redisServicePwd" },
    { ConfigItem::RedisServiceGatherDb, "redisServiceGatherDb" },
    { ConfigItem::RedisServiceStnDb, "redisServiceStnDb" },
    { ConfigItem::RedisServiceResultDb, "redisServiceResultDb" },
    { ConfigItem::LocalRedisIp, "localRedisIp" },
    { ConfigItem::LocalRedisPort, "localRedisPort" },
    { ConfigItem::LocalRedisPwd, "localRedisPwd" },
    { ConfigItem::LocalRedisGatherDb, "localRedisGatherDb" },
    { ConfigItem::LocalRedisStnDb, "localRedisStnDb" },
    { ConfigItem::LocalRedisResultDb, "localRedisResultDb" },
    { ConfigItem::LocalHostIp, "localHostIp" },
    { ConfigItem::IntersectionDevicesKey, "intersectionDevicesKey"},
    { ConfigItem::OriginStateKey, "originStateKey"},
    { ConfigItem::AlgConfigKey, "algConfigKey"},
    { ConfigItem::SensorConfigKey, "sensorConfigKey"},
    { ConfigItem::SignalConfigKey, "signalConfigKey"},
    { ConfigItem::AlgConfigFileDir, "algConfigFileDir"},
    { ConfigItem::AlgPhaseErrorThreshold, "algPhaseErrorThreshold"}

};


// 配置管理
class ConfigManager {
public:
    static ConfigManager& getInstance() {
        static ConfigManager instance;
        return instance;
    }

    std::string getConfig(const std::string& key) {
        return config[key];
    }

    bool loadConfig(const std::string &filename);

     // Mapping ConfigItem to corresponding ConfigData fields via lambdas
    std::unordered_map<ConfigItem, std::function<void()> > configSetters = {
        {
            ConfigItem::LogLevel,
            [&]() { configData.logLevel = getConfig(configItemToString.at(ConfigItem::LogLevel)); }
        },
        {
            ConfigItem::MsgCenterUrl,
            [&]() { configData.msgCenterUrl = getConfig(configItemToString.at(ConfigItem::MsgCenterUrl)); }
        },
        {
            ConfigItem::RedisServiceIp,
            [&]() { configData.redisServiceIp = getConfig(configItemToString.at(ConfigItem::RedisServiceIp)); }
        },
        {
            ConfigItem::RedisServicePort,
            [&]() { configData.redisServicePort = getConfig(configItemToString.at(ConfigItem::RedisServicePort)); }
        },
        {
            ConfigItem::RedisServicePwd,
            [&]() { configData.redisServicePwd = getConfig(configItemToString.at(ConfigItem::RedisServicePwd)); }
        },
        {
            ConfigItem::RedisServiceGatherDb,
            [&]() {
                configData.redisServiceGatherDb = getConfig(configItemToString.at(ConfigItem::RedisServiceGatherDb));
            }
        },
        {
            ConfigItem::RedisServiceStnDb,
            [&]() { configData.redisServiceStnDb = getConfig(configItemToString.at(ConfigItem::RedisServiceStnDb)); }
        },
        {
            ConfigItem::RedisServiceResultDb,
            [&]() {
                configData.redisServiceResultDb = getConfig(configItemToString.at(ConfigItem::RedisServiceResultDb));
            }
        },
        {
            ConfigItem::LocalRedisIp,
            [&]() { configData.localRedisIp = getConfig(configItemToString.at(ConfigItem::LocalRedisIp)); }
        },
        {
            ConfigItem::LocalRedisPort,
            [&]() { configData.localRedisPort = getConfig(configItemToString.at(ConfigItem::LocalRedisPort)); }
        },
        {
            ConfigItem::LocalRedisPwd,
            [&]() { configData.localRedisPwd = getConfig(configItemToString.at(ConfigItem::LocalRedisPwd)); }
        },
        {
            ConfigItem::LocalRedisGatherDb,
            [&]() { configData.localRedisGatherDb = getConfig(configItemToString.at(ConfigItem::LocalRedisGatherDb)); }
        },
        {
            ConfigItem::LocalRedisStnDb,
            [&]() { configData.localRedisStnDb = getConfig(configItemToString.at(ConfigItem::LocalRedisStnDb)); }
        },
        {
            ConfigItem::LocalRedisResultDb,
            [&]() { configData.localRedisResultDb = getConfig(configItemToString.at(ConfigItem::LocalRedisResultDb)); }
        },
        {
            ConfigItem::LocalHostIp,
            [&]() { configData.localHostIp = getConfig(configItemToString.at(ConfigItem::LocalHostIp)); }
        },
        {
            ConfigItem::IntersectionDevicesKey,
            [&]() {
                configData.intersectionDevicesKey =
                        getConfig(configItemToString.at(ConfigItem::IntersectionDevicesKey));
            }
        },
        {
            ConfigItem::OriginStateKey,
            [&]() { configData.originStateKey = getConfig(configItemToString.at(ConfigItem::OriginStateKey)); }
        },
        {
            ConfigItem::AlgConfigKey,
            [&]() { configData.algConfigKey = getConfig(configItemToString.at(ConfigItem::AlgConfigKey)); }
        },
        {
            ConfigItem::SensorConfigKey,
            [&]() { configData.sensorConfigKey = getConfig(configItemToString.at(ConfigItem::SensorConfigKey)); }
        },
        {
            ConfigItem::SignalConfigKey,
            [&]() { configData.signalConfigKey = getConfig(configItemToString.at(ConfigItem::SignalConfigKey)); }
        },
        {
            ConfigItem::AlgConfigFileDir,
            [&]() { configData.algConfigFileDir = getConfig(configItemToString.at(ConfigItem::AlgConfigFileDir)); }
        },
        {
            ConfigItem::AlgPhaseErrorThreshold,
            [&]() { configData.algPhaseErrorThreshold = getConfig(configItemToString.at(ConfigItem::AlgPhaseErrorThreshold)); }
        },
    };

private:
    ConfigManager() = default;
    std::unordered_map<std::string, std::string> config;
    bool readConfigElement(const tinyxml2::XMLElement* element, const std::string &key);

};
#endif