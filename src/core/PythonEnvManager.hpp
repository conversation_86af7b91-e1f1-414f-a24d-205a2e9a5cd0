//
// Created by lth on 2024/10/15.
//

#ifndef PYTHONENVMANAGER_HPP
#define PYTHONENVMANAGER_HPP

#include <Python.h>
#include <iostream>


class PythonEnvManager {
  public:
    static PythonEnvManager& getInstance() {
        static PythonEnvManager instance;
        return instance;
    }
//private:
    PythonEnvManager() {
        Py_Initialize();
        PyEval_InitThreads();
        Py_DECREF(PyImport_ImportModule("threading"));
        mainThreadState = PyThreadState_Get();
        PyEval_ReleaseThread(mainThreadState);
    }
    ~PythonEnvManager() {
        PyEval_RestoreThread(mainThreadState);
        Py_Finalize();
    }

    PythonEnvManager(const PythonEnvManager&) = delete;
    PythonEnvManager& operator=(const PythonEnvManager&) = delete;

    PyThreadState* mainThreadState;
};



#endif //PYTHONENVMANAGER_HPP
