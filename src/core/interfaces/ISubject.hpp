#ifndef STN_ISUBJECT_HPP
#define STN_ISUBJECT_HPP
#include "IObserver.hpp"
class IObserver;
class ISubject {
public:
    [[nodiscard]] virtual int getType() const = 0;  // 主题提供类型信息
    virtual void addObserver(IObserver* observer) = 0;
    virtual void removeObserver(IObserver* observer) = 0;
    virtual void notifyAllObservers(const std::string& message) = 0;
    virtual void notifyObservers(const std::string& message, int component) = 0;
    virtual ~ISubject() = default;
};

#endif