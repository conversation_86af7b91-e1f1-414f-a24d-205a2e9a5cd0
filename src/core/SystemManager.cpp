#include "SystemManager.hpp"
#include <csignal>
//#include <Python.h>
#include "utils/NatsClient.hpp"


extern volatile sig_atomic_t gStopFlag;
extern NatsClient& gNatsClient;

SystemManager::SystemManager(){
    isRunning = false;
    LOG(INFO, "System initialized successfully");
}

SystemManager::~SystemManager() {
    try {
        // 在析构函数中不要再次清理资源，因为它们应该已经在 cleanup() 中被清理
        // 只记录日志
        LOG(INFO, "SystemManager destroyed.");
    } catch (const std::exception& e) {
        // 捕获并记录异常，但不再抛出
        std::cerr << "Exception in SystemManager destructor: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown exception in SystemManager destructor" << std::endl;
    }
}

void SystemManager::initialize() {
    try {
        if (!ConfigManager::getInstance().loadConfig("config/stn_config.xml")) {
            LOG(ERROR, "STN Config File load failed.");
            throw std::runtime_error("STN Config File load failed.");
        }
        Logger::getInstance().init();
        Logger::getInstance().setLogLevel(
            static_cast<Logger::Level>(std::stoi(configData.logLevel)));
        //初始化线程池
        size_t hardware_concurrency = std::thread::hardware_concurrency();
        if (hardware_concurrency == 0 ) {
            hardware_concurrency = 4;
        }
        size_t threadNum = std::min(static_cast<size_t>(2), hardware_concurrency);
        threadPool = std::make_unique<ThreadPool>(threadNum);

        // 配置并初始化NATS客户端
        NatsClient::Config natsConfig;
        natsConfig.url = configData.msgCenterUrl;
        if (!gNatsClient.init(natsConfig)) {
            LOG(ERROR, "NatsClient initialization failed." + gNatsClient.getLastError());
        }

        auto redisProvider = ComponentFactory::createDataProvider();
        auto signalCtrl = ComponentFactory::createSignalController();
        //signalCtrl->addObserver(flowCtrl.get());
        signalController = std::move(signalCtrl);
        redisProvider->addObserver(signalController.get());
        //start signalControl loop
        threadPool->enqueue(
            [this]() {
                signalController->querySignalState();
            }
        );
        //auto flowCtrl = ComponentFactory::createFlowController(signalController.get());
        auto flowCtrl = ComponentFactory::createFlowController(signalController.get(), configData.localRedisIp,
                                                                std::stoi(configData.localRedisPort), configData.localRedisPwd);
        flowController = std::move(flowCtrl);
        signalController->addObserver(flowController.get());
        redisProvider->addObserver(flowController.get());
        dataProviders.push_back(std::move(redisProvider));
    } catch (const std::exception& e) {
        LOG(ERROR, "Initialization error: " + std::string(e.what()));
        throw SystemException("Failed to initialize system");
    }
}

void SystemManager::run() {
    try {
        for (const auto& provider : dataProviders) {
            threadPool->enqueue([&provider]() { provider->captureData(); });
        }
        isRunning = true;
        while (isRunning) {
            sleep(1);
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Runtime error: " + std::string(e.what()));
        throw SystemException("System runtime error");
    }
}

void SystemManager::shutdown(int signum) {
    isRunning = false;
    LOG(WARNING, "System shutdown received.");
}

void SystemManager::cleanup() {
    LOG(INFO, "SystemManager cleanup called.");

    try {
        // 停止所有数据提供者的数据捕获
        for (auto& provider : dataProviders) {
            if (provider) {
                try {
                    provider->stopCapture();
                } catch (const std::exception& e) {
                    LOG(ERROR, "Exception stopping data provider: {}", e.what());
                } catch (...) {
                    LOG(ERROR, "Unknown exception stopping data provider");
                }
            }
        }

        // 等待一小段时间，确保停止命令已经生效
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // 按照与初始化相反的顺序清理组件
        LOG(INFO, "Cleaning up signal controller...");
        signalController.reset();

        LOG(INFO, "Cleaning up flow controller...");
        flowController.reset();

        LOG(INFO, "Cleaning up data providers...");
        dataProviders.clear();

        LOG(INFO, "Cleaning up thread pool...");
        threadPool.reset();

        LOG(INFO, "SystemManager cleanup completed.");
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception during SystemManager cleanup: {}", e.what());
    } catch (...) {
        LOG(ERROR, "Unknown exception during SystemManager cleanup");
    }
}


