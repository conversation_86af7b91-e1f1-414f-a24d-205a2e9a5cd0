#include "ConfigManager.hpp"
#include "utils/Logger.hpp"
using namespace tinyxml2;

bool ConfigManager::loadConfig([[maybe_unused]] const std::string &filename) {
    // 简化的配置加载
    /*
    config["data_capture_interval"] = "5000";
    config["max_traffic_load"] = "1000";
    config["local_redis_ip"] = "firefly.local";
    config["local_redis_port"] = "6379";
    config["local_redis_pwd"] = "jyg2021";
    */

    // 创建XML文档对象
    XMLDocument doc;

    // 从文件加载XML文档
    XMLError eResult = doc.LoadFile(filename.c_str());
    if (eResult != XML_SUCCESS) {
        std::cerr << "Error loading XML file: " << eResult << std::endl;
        LOG(ERROR, "Error loading XML config File: {}, ret {}", filename.c_str(), static_cast<int>(eResult));
        return false;
    }

    // 获取根元素
    XMLElement* pSysConfig = doc.FirstChildElement("SysConfig");
    if (!pSysConfig) {
        LOG(ERROR, "SysConfig element not found.");
        return false;
    }


    for (const auto&[fst, snd] : configItemToString) {
        XMLElement* pConfig = pSysConfig->FirstChildElement(snd);
        if (false == readConfigElement(pConfig, snd)) {
            return false;
        }
    }

    // 输出配置
    for (const auto& [key, value] : config) {
        LOG(INFO, "{}: {}", key.c_str(), value.c_str());
    }

    for (const auto& item : configSetters) {
        item.second();
    }
    return true;
}

bool ConfigManager::readConfigElement(const XMLElement *element, const std::string &key) {
    if (element && element->GetText()) {
        config[key] = element->GetText();
        return true;
    }
    LOG(WARNING, "{} not found or empty.", key.c_str());
    return false;
}
