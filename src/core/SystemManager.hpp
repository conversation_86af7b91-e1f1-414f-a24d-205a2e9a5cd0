#ifndef STN_SYSTEMMANAGER_HPP
#define STN_SYSTEMMANAGER_HPP

#include <signal.h>

#include "components/ComponentFactory.hpp"
#include "utils/Logger.hpp"
#include "utils/Exceptions.hpp"
#include "ConfigManager.hpp"
#include "utils/ThreadPool.h"
//#include "PythonEnvManager.hpp"

// 系统管理类
class SystemManager {
public:
    static SystemManager& getInstance() {
        static SystemManager instance;
        return instance;
    }
    void initialize();
    void run();
    void shutdown(int signum);
    void cleanup(); // 添加清理方法
private:
    SystemManager();
    ~SystemManager();
    std::vector<std::unique_ptr<IDataProvider>> dataProviders;
    std::unique_ptr<ITrafficManager> trafficManager;
    std::unique_ptr<IDataProcessor> dataProcessor;
    std::unique_ptr<IFlowController> flowController;
    std::unique_ptr<ISignalController> signalController;
    const std::string FlowControlConfigFile = "/home/<USER>/stn/alg/XAL_XFL_net.json";
    const std::string SensorConfigFile = "/home/<USER>/stn/alg/sensor_conf.json";
    std::unique_ptr<ThreadPool> threadPool;
    bool isRunning;
    //std::unique_ptr<PythonEnvManager> pyEnvManager;
};
#endif