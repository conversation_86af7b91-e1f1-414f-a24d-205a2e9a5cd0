# 检测结果回调示例

本目录包含了几个示例，展示如何将 `detections2json` 函数的返回结果（JSON字符串）传递给上层应用。

## 异步回调机制

我们实现了一个异步回调机制，使用线程池来执行回调函数。这样可以避免回调函数阻塞视频处理线程，即使回调函数执行耗时操作（如网络传输或复杂处理）也不会影响视频处理的性能。

## 示例说明

### 1. 基本回调示例 (detection_callback_example.cpp)

这个示例展示了如何设置和使用检测结果回调函数。它将检测结果打印到控制台并保存到文件中。

编译和运行：

```bash
g++ -o detection_callback_example detection_callback_example.cpp ../src/*.cpp -I../src -lopencv_core -lopencv_imgproc -lopencv_highgui -lpthread
./detection_callback_example rtsp://your-camera-url
```

### 2. 网络发送示例 (network_detection_callback.cpp)

这个示例展示了如何将检测结果通过网络发送给上层应用。它使用了一个简单的消息队列和发送线程。

编译和运行：

```bash
g++ -o network_detection_callback network_detection_callback.cpp ../src/*.cpp -I../src -lopencv_core -lopencv_imgproc -lopencv_highgui -lpthread
./network_detection_callback rtsp://your-camera-url
```

### 3. WebSocket服务器示例 (websocket_detection_server.cpp)

这个示例展示了如何创建一个WebSocket服务器，将检测结果实时发送给前端应用。

注意：这个示例需要一个WebSocket库，如libwebsockets或websocketpp。示例代码中使用了一个模拟的WebSocket接口，实际使用时需要替换为真实的WebSocket库。

编译和运行：

```bash
# 假设使用libwebsockets库
g++ -o websocket_detection_server websocket_detection_server.cpp ../src/*.cpp -I../src -lopencv_core -lopencv_imgproc -lopencv_highgui -lpthread -lwebsockets
./websocket_detection_server rtsp://your-camera-url 8080
```

### 4. WebSocket客户端 (websocket_client.html)

这是一个HTML页面，用于接收和显示WebSocket服务器发送的检测结果。

使用方法：
1. 启动WebSocket服务器（运行websocket_detection_server）
2. 在浏览器中打开websocket_client.html
3. 点击"连接"按钮（确保WebSocket URL正确）
4. 实时检测结果将显示在页面上

## 集成到现有项目

要将这些示例集成到现有项目中，您需要：

1. 确保在 `ffmpeg_decode2_async.h` 和 `ffmpeg_decode2_async.cpp` 中添加了回调机制
2. 在您的应用程序中实现回调函数
3. 使用 `setDetectionCallback` 方法设置回调函数
4. 根据您的需求，选择合适的方式将检测结果传递给上层应用

### 使用方法

1. 在上层应用中实现回调函数：
```cpp
void on_detection_result(const std::string& json_result, const std::string& camera_id) {
    // 注意：这个函数将在单独的线程中异步执行
    // 可以安全地执行耗时操作，而不会阻塞视频处理线程

    // 处理检测结果
    std::cout << "收到来自相机 " << camera_id << " 的检测结果：" << json_result << std::endl;

    // 如果需要执行耗时操作，可以直接在这里执行
    // 例如：网络传输、数据库操作、复杂计算等

    // 将结果发送给其他系统或保存到数据库等
}
```

2. 设置回调函数：
```cpp
decoder->setDetectionCallback(on_detection_result);
```

3. 检测结果将自动通过回调函数传递给上层应用。

### 异步回调的注意事项

由于回调函数是在单独的线程中执行的，需要注意以下几点：

1. **线程安全**：如果回调函数访问共享资源，需要使用适当的同步机制（如互斥锁）。

2. **资源管理**：回调函数中使用的所有数据都是复制的，原始数据可能在回调函数执行时已经被释放。

3. **异常处理**：在回调函数中抛出的异常不会影响主线程，需要在回调函数内部正确处理异常。

## 其他可能的集成方式

除了示例中展示的方法外，您还可以考虑以下方式将检测结果传递给上层应用：

1. **Redis发布/订阅**：使用Redis的发布/订阅功能，将检测结果发布到特定的频道
2. **NATS消息系统**：使用NATS消息系统发送检测结果
3. **HTTP API**：创建一个HTTP服务器，提供API接口供上层应用获取检测结果
4. **共享内存**：使用共享内存在进程间传递检测结果
5. **消息队列**：使用如RabbitMQ、ZeroMQ等消息队列系统

选择哪种方式取决于您的具体需求，如实时性要求、系统架构、上层应用的类型等。
