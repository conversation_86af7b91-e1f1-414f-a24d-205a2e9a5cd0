#include <iostream>
#include <string>
#include <memory>
#include <thread>
#include <chrono>
#include <signal.h>
#include <mutex>
#include <queue>
#include <condition_variable>
#include <atomic>
#include "../src/ffmpeg_decode2_async.h"
#include "../src/stream_manager.h"

// 如果项目中有网络通信库，可以包含相应的头文件
// 例如：
// #include "../../utils/NatsClient.hpp"
// #include "../../utils/RedisClient.hpp"
// #include "../../TrafficControlServer/TrafficControlServer.hpp"

// 全局停止标志
volatile sig_atomic_t stop_flag = 0;

// 信号处理函数
void signal_handler(int signal) {
    std::cout << "接收到信号 " << signal << "，准备退出..." << std::endl;
    stop_flag = 1;
}

// 线程安全的消息队列
class MessageQueue {
public:
    void push(const std::string& message) {
        std::lock_guard<std::mutex> lock(mutex_);
        queue_.push(message);
        cv_.notify_one();
    }
    
    bool pop(std::string& message, int timeout_ms = 1000) {
        std::unique_lock<std::mutex> lock(mutex_);
        if (queue_.empty()) {
            // 等待新消息或超时
            auto result = cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                [this] { return !queue_.empty() || stop_; });
            if (!result) return false;  // 超时
            if (stop_) return false;    // 停止标志
        }
        
        if (queue_.empty()) return false;
        
        message = queue_.front();
        queue_.pop();
        return true;
    }
    
    void setStop(bool stop) {
        std::lock_guard<std::mutex> lock(mutex_);
        stop_ = stop;
        cv_.notify_all();
    }
    
private:
    std::queue<std::string> queue_;
    std::mutex mutex_;
    std::condition_variable cv_;
    bool stop_ = false;
};

// 网络发送线程类
class NetworkSender {
public:
    NetworkSender() : running_(false) {}
    
    ~NetworkSender() {
        stop();
    }
    
    void start() {
        if (running_.load()) return;
        
        running_.store(true);
        messageQueue_.setStop(false);
        
        // 启动发送线程
        senderThread_ = std::thread(&NetworkSender::senderThreadFunc, this);
    }
    
    void stop() {
        if (!running_.load()) return;
        
        running_.store(false);
        messageQueue_.setStop(true);
        
        if (senderThread_.joinable()) {
            senderThread_.join();
        }
    }
    
    void sendMessage(const std::string& message) {
        if (!running_.load()) return;
        messageQueue_.push(message);
    }
    
private:
    void senderThreadFunc() {
        std::string message;
        
        while (running_.load()) {
            if (messageQueue_.pop(message)) {
                // 在这里实现将消息发送到上层应用的逻辑
                // 可以使用项目中已有的通信机制，如：
                
                // 1. 使用Redis发布/订阅
                // if (redisClient) {
                //     redisClient->publish("detection_results", message);
                // }
                
                // 2. 使用NATS消息系统
                // if (natsClient) {
                //     natsClient->publish("detection_results", message);
                // }
                
                // 3. 使用TCP服务器
                // if (tcpServer) {
                //     tcpServer->sendToClient(message);
                // }
                
                // 4. 使用HTTP POST请求
                // httpClient.post("http://your-server/api/detections", message);
                
                // 示例：简单地打印消息
                std::cout << "准备发送消息: " << message << std::endl;
                
                // 模拟网络延迟
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
    
    std::atomic<bool> running_;
    std::thread senderThread_;
    MessageQueue messageQueue_;
    
    // 可以在这里添加通信客户端的实例
    // std::shared_ptr<RedisClient> redisClient;
    // std::shared_ptr<NatsClient> natsClient;
    // std::shared_ptr<TrafficControlServer> tcpServer;
};

// 全局网络发送器实例
NetworkSender g_networkSender;

// 检测结果回调函数
void on_detection_result(const std::string& json_result, const std::string& camera_id) {
    // 可以在这里对JSON结果进行处理，例如添加额外信息
    // 为简单起见，我们直接发送原始JSON
    g_networkSender.sendMessage(json_result);
}

int main(int argc, char** argv) {
    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 检查命令行参数
    if (argc < 2) {
        std::cerr << "用法: " << argv[0] << " <rtsp_url>" << std::endl;
        return -1;
    }
    
    const char* rtsp_url = argv[1];
    
    try {
        // 启动网络发送器
        g_networkSender.start();
        
        // 创建流管理器
        StreamManager stream_manager;
        
        // 添加流
        int camera_id = 0;  // 相机ID
        StreamInfo* stream_info = stream_manager.addStream(rtsp_url, camera_id);
        if (!stream_info) {
            std::cerr << "添加流失败" << std::endl;
            return -1;
        }
        
        // 获取解码器
        AsyncFFmpegDecoder* decoder = stream_info->getDecoder();
        if (!decoder) {
            std::cerr << "获取解码器失败" << std::endl;
            return -1;
        }
        
        // 设置检测结果回调函数
        decoder->setDetectionCallback(on_detection_result);
        
        // 启动流处理
        if (!stream_manager.startAll()) {
            std::cerr << "启动流处理失败" << std::endl;
            return -1;
        }
        
        std::cout << "开始处理视频流，按Ctrl+C退出..." << std::endl;
        
        // 主循环
        while (!stop_flag) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        
        // 停止流处理
        stream_manager.stopAll();
        
        // 停止网络发送器
        g_networkSender.stop();
        
        std::cout << "已停止所有处理" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
