#include "rknn_vision_lib.h"
#include <iostream>
#include <csignal>
#include <thread>
#include <chrono>
#include <vector>
#include <cstring>  // 添加这个头文件，用于 strstr 函数

// Global flag to detect SIGINT signal
volatile sig_atomic_t g_stop_flag = 0;

// Signal handler
void signal_handler(int signal) {
        std::cout << "Application signal handler: Initiating application shutdown..." << std::endl;
        g_stop_flag = 1;
}

int main(int argc, char** argv) {
    // We'll register our signal handler after library initialization

    // Check command line arguments
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " [show_gui] <rtsp_url1> [roi_x1,roi_y1,roi_x2,roi_y2] [rtsp_url2] [roi_x1,roi_y1,roi_x2,roi_y2] ..." << std::endl;
        std::cerr << "show_gui: If first parameter is 1, GUI display will be enabled for all streams" << std::endl;
        std::cerr << "ROI coordinates are optional. If not provided, the entire frame will be used." << std::endl;
        std::cerr << "Example: " << argv[0] << " 1 rtsp://example.com/stream1 100,200,500,600 rtsp://example.com/stream2" << std::endl;
        return 1;
    }

    // 判断第一个参数是否为GUI显示控制参数
    bool showGui = false;
    int startArgIndex = 1;

    // 如果第一个参数是1，则设置所有流的showGui为true
    if (argc > 1 && (strcmp(argv[1], "1") == 0)) {
        showGui = true;
        startArgIndex = 2; // 从第二个参数开始解析URL
        std::cout << "GUI display enabled for all streams" << std::endl;
    }

    // Parse command line arguments and create stream info
    std::vector<RknnStreamInfo> streams;

    for (int i = startArgIndex; i < argc; i++) {
        // Check if argument is a URL
        if (strstr(argv[i], "rtsp://") == argv[i] ||
            strstr(argv[i], "http://") == argv[i] ||
            strstr(argv[i], "file:") == argv[i]) {

            RknnStreamInfo stream;
            stream.url = argv[i];
            stream.id = "Stream_" + std::to_string(streams.size() + 1);
            stream.roi = {0, 0, 0, 0}; // Default ROI (entire frame)
            stream.camId = std::to_string(streams.size() + 1); // Set camera ID to stream index + 1 as string
            stream.showGui = showGui; // 设置是否显示GUI
            //stream.targetClassNames = {"person", "car"}; // Set target class names to "person"
            stream.targetClassNames = {"person"}; // Set target class names to "person"

            // Check if next argument is ROI coordinates
            if (i + 1 < argc && strstr(argv[i + 1], "rtsp://") != argv[i + 1] &&
                strstr(argv[i + 1], "http://") != argv[i + 1] &&
                strstr(argv[i + 1], "file:") != argv[i + 1]) {

                // Parse ROI coordinates
                int x1, y1, x2, y2;
                if (sscanf(argv[i + 1], "%d,%d,%d,%d", &x1, &y1, &x2, &y2) == 4) {
                    stream.roi = {x1, y1, x2, y2};
                    std::cout << "ROI for " << stream.url << ": (" << x1 << "," << y1 << ") - (" << x2 << "," << y2 << ")" << std::endl;
                    i++; // Skip ROI argument
                } else {
                    std::cerr << "Invalid ROI format: " << argv[i + 1] << ". Expected format: x1,y1,x2,y2" << std::endl;
                }
            }

            streams.push_back(stream);
        }
    }

    if (streams.empty()) {
        std::cerr << "No valid streams specified" << std::endl;
        std::cerr << "Usage: " << argv[0] << " [show_gui] <rtsp_url1> [roi_x1,roi_y1,roi_x2,roi_y2] [rtsp_url2] [roi_x1,roi_y1,roi_x2,roi_y2] ..." << std::endl;
        std::cerr << "show_gui: If first parameter is 1, GUI display will be enabled for all streams" << std::endl;
        return 1;
    }

    // Configure Redis
    RknnRedisConfig redis_config;
    redis_config.host = "localhost";
    redis_config.port = 6379;
    redis_config.password = "jyg2021";

    // Initialize library
    std::cout << "Initializing RKNN Vision library..." << std::endl;
    if (!rknn_vision::init(streams, redis_config, false)) {
        std::cerr << "Failed to initialize RKNN Vision library" << std::endl;
        return 1;
    }

    // Start processing
    std::cout << "Starting RKNN Vision processing..." << std::endl;
    if (!rknn_vision::start()) {
        std::cerr << "Failed to start RKNN Vision processing" << std::endl;
        return 1;
    }

    // Register our signal handler AFTER library initialization
    // This ensures our handler gets called when the library's handler is done
    struct sigaction sa{};
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;
    sigaction(SIGINT, &sa, nullptr);
    sigaction(SIGTERM, &sa, nullptr);
    sigaction(SIGHUP, &sa, nullptr);


    std::cout << "RKNN Vision processing started. Press Ctrl+C to exit." << std::endl;

    // Main loop
    while (!g_stop_flag) {
        // Display stream information
        std::cout << "\n----- Stream Status -----" << std::endl;
        for (const auto& stream : streams) {
            float fps = rknn_vision::getStreamFps(stream.id);
            bool active = rknn_vision::isStreamActive(stream.id);

            std::cout << "Stream [" << stream.id << "] - "
                      << (active ? "Active" : "Inactive")
                      << ", FPS: " << fps
                      << ", GUI: " << (stream.showGui ? "Enabled" : "Disabled");

            // Display ROI information if set
            if (stream.roi.x2 > stream.roi.x1 && stream.roi.y2 > stream.roi.y1) {
                std::cout << ", ROI: (" << stream.roi.x1 << "," << stream.roi.y1
                          << "," << stream.roi.x2 << "," << stream.roi.y2 << ")";
            }

            std::cout << std::endl;
        }
        std::cout << "------------------------" << std::endl;

        // Sleep for 3 seconds, checking for stop flag every second
        for (int i = 0; i < 3; i++) {
            if (g_stop_flag) {
                std::cout << "Stop flag detected, breaking main loop..." << std::endl;
                break;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    std::cout << "Main loop exited. Proceeding to cleanup..." << std::endl;

    // Stop processing
    std::cout << "\nStopping RKNN Vision processing..." << std::endl;
    if (!rknn_vision::stop()) {
        std::cerr << "Failed to stop RKNN Vision processing" << std::endl;
        return 1;
    }

    std::cout << "RKNN Vision processing stopped." << std::endl;

    return 0;
}
