#include <iostream>
#include <string>
#include <memory>
#include <thread>
#include <chrono>
#include <signal.h>
#include <mutex>
#include <queue>
#include <condition_variable>
#include <atomic>
#include <set>
#include "../src/ffmpeg_decode2_async.h"
#include "../src/stream_manager.h"

// 注意：这个示例需要一个WebSocket库
// 这里使用的是一个假设的WebSocket库接口
// 实际使用时，您需要替换为实际的WebSocket库，如libwebsockets、websocketpp等

// WebSocket服务器接口（示例）
class WebSocketServer {
public:
    WebSocketServer(int port = 8080) : port_(port), running_(false) {}
    
    ~WebSocketServer() {
        stop();
    }
    
    bool start() {
        if (running_.load()) return true;
        
        std::cout << "启动WebSocket服务器，端口: " << port_ << std::endl;
        running_.store(true);
        
        // 启动服务器线程
        serverThread_ = std::thread(&WebSocketServer::serverThreadFunc, this);
        return true;
    }
    
    void stop() {
        if (!running_.load()) return;
        
        std::cout << "停止WebSocket服务器" << std::endl;
        running_.store(false);
        
        if (serverThread_.joinable()) {
            serverThread_.join();
        }
    }
    
    // 向所有连接的客户端广播消息
    void broadcast(const std::string& message) {
        std::lock_guard<std::mutex> lock(clientsMutex_);
        for (auto& client : clients_) {
            // 在实际的WebSocket库中，这里会调用发送消息的API
            std::cout << "向客户端 " << client << " 发送消息: " << message << std::endl;
        }
    }
    
private:
    void serverThreadFunc() {
        std::cout << "WebSocket服务器线程启动" << std::endl;
        
        // 在实际的WebSocket库中，这里会初始化服务器并处理连接
        // 这里只是一个模拟
        while (running_.load()) {
            // 模拟接受新连接
            static int clientId = 1000;
            if (rand() % 100 == 0) {  // 随机模拟新连接
                std::lock_guard<std::mutex> lock(clientsMutex_);
                clients_.insert(clientId++);
                std::cout << "新客户端连接，当前客户端数: " << clients_.size() << std::endl;
            }
            
            // 模拟客户端断开连接
            if (!clients_.empty() && rand() % 200 == 0) {  // 随机模拟断开连接
                std::lock_guard<std::mutex> lock(clientsMutex_);
                auto it = clients_.begin();
                std::advance(it, rand() % clients_.size());
                std::cout << "客户端 " << *it << " 断开连接" << std::endl;
                clients_.erase(it);
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        std::cout << "WebSocket服务器线程退出" << std::endl;
    }
    
    int port_;
    std::atomic<bool> running_;
    std::thread serverThread_;
    std::set<int> clients_;  // 连接的客户端集合（在实际应用中，这里会是WebSocket连接对象）
    std::mutex clientsMutex_;
};

// 全局停止标志
volatile sig_atomic_t stop_flag = 0;

// 信号处理函数
void signal_handler(int signal) {
    std::cout << "接收到信号 " << signal << "，准备退出..." << std::endl;
    stop_flag = 1;
}

// 全局WebSocket服务器实例
std::unique_ptr<WebSocketServer> g_webSocketServer;

// 检测结果回调函数
void on_detection_result(const std::string& json_result, const std::string& camera_id) {
    if (g_webSocketServer) {
        // 可以在这里对JSON结果进行处理，例如添加相机ID等信息
        // 为简单起见，我们直接广播原始JSON
        g_webSocketServer->broadcast(json_result);
    }
}

int main(int argc, char** argv) {
    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 检查命令行参数
    if (argc < 2) {
        std::cerr << "用法: " << argv[0] << " <rtsp_url> [websocket_port]" << std::endl;
        return -1;
    }
    
    const char* rtsp_url = argv[1];
    int websocket_port = (argc > 2) ? std::atoi(argv[2]) : 8080;
    
    try {
        // 创建并启动WebSocket服务器
        g_webSocketServer = std::make_unique<WebSocketServer>(websocket_port);
        if (!g_webSocketServer->start()) {
            std::cerr << "启动WebSocket服务器失败" << std::endl;
            return -1;
        }
        
        // 创建流管理器
        StreamManager stream_manager;
        
        // 添加流
        int camera_id = 0;  // 相机ID
        StreamInfo* stream_info = stream_manager.addStream(rtsp_url, camera_id);
        if (!stream_info) {
            std::cerr << "添加流失败" << std::endl;
            return -1;
        }
        
        // 获取解码器
        AsyncFFmpegDecoder* decoder = stream_info->getDecoder();
        if (!decoder) {
            std::cerr << "获取解码器失败" << std::endl;
            return -1;
        }
        
        // 设置检测结果回调函数
        decoder->setDetectionCallback(on_detection_result);
        
        // 启动流处理
        if (!stream_manager.startAll()) {
            std::cerr << "启动流处理失败" << std::endl;
            return -1;
        }
        
        std::cout << "开始处理视频流，按Ctrl+C退出..." << std::endl;
        std::cout << "WebSocket服务器运行在端口 " << websocket_port << std::endl;
        
        // 主循环
        while (!stop_flag) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        
        // 停止流处理
        stream_manager.stopAll();
        
        // 停止WebSocket服务器
        g_webSocketServer->stop();
        g_webSocketServer.reset();
        
        std::cout << "已停止所有处理" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
