#include <iostream>
#include <string>
#include <memory>
#include <thread>
#include <chrono>
#include <signal.h>
#include "../src/ffmpeg_decode2_async.h"
#include "../src/stream_manager.h"

// 全局停止标志
volatile sig_atomic_t stop_flag = 0;

// 信号处理函数
void signal_handler(int signal) {
    std::cout << "接收到信号 " << signal << "，准备退出..." << std::endl;
    stop_flag = 1;
}

// 检测结果回调函数
void on_detection_result(const std::string& json_result, const std::string& camera_id) {
    // 注意：这个函数现在是在单独的线程中异步执行的
    // 可以安全地执行耗时操作，而不会阻塞视频处理线程

    std::cout << "收到来自相机 " << camera_id << " 的检测结果：" << std::endl;
    std::cout << json_result << std::endl;

    // 模拟耗时操作（例如网络传输或复杂处理）
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 在这里，您可以将JSON结果发送到上层应用
    // 例如：通过WebSocket、HTTP、TCP或其他通信机制

    // 示例：将结果保存到文件
    static std::mutex file_mutex; // 添加互斥锁保护文件操作
    static int count = 0;

    std::string filename;
    {
        std::lock_guard<std::mutex> lock(file_mutex);
        filename = "detection_" + camera_id + "_" + std::to_string(count++) + ".json";
    }

    FILE* fp = fopen(filename.c_str(), "w");
    if (fp) {
        fwrite(json_result.c_str(), 1, json_result.length(), fp);
        fclose(fp);
        std::cout << "结果已保存到文件：" << filename << std::endl;
    }
}

int main(int argc, char** argv) {
    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 检查命令行参数
    if (argc < 2) {
        std::cerr << "用法: " << argv[0] << " <rtsp_url>" << std::endl;
        return -1;
    }

    const char* rtsp_url = argv[1];

    try {
        // 创建流管理器
        StreamManager stream_manager;

        // 添加流
        int camera_id = 0;  // 相机ID
        StreamInfo* stream_info = stream_manager.addStream(rtsp_url, camera_id);
        if (!stream_info) {
            std::cerr << "添加流失败" << std::endl;
            return -1;
        }

        // 获取解码器
        AsyncFFmpegDecoder* decoder = stream_info->getDecoder();
        if (!decoder) {
            std::cerr << "获取解码器失败" << std::endl;
            return -1;
        }

        // 设置检测结果回调函数
        decoder->setDetectionCallback(on_detection_result);

        // 启动流处理
        if (!stream_manager.startAll()) {
            std::cerr << "启动流处理失败" << std::endl;
            return -1;
        }

        std::cout << "开始处理视频流，按Ctrl+C退出..." << std::endl;

        // 主循环
        while (!stop_flag) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }

        // 停止流处理
        stream_manager.stopAll();
        std::cout << "已停止所有流处理" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}
