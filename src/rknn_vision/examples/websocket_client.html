<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时检测结果查看器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
        }
        .results-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .result-card {
            flex: 1;
            min-width: 300px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        .result-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .camera-id {
            font-weight: bold;
            color: #007bff;
        }
        .timestamp {
            color: #6c757d;
        }
        .detection-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .detection-item {
            padding: 8px;
            margin-bottom: 8px;
            background-color: white;
            border-radius: 3px;
            border-left: 3px solid #007bff;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin-top: 20px;
        }
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        button {
            padding: 8px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0069d9;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            flex-grow: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>实时检测结果查看器</h1>
        
        <div class="controls">
            <input type="text" id="wsUrl" value="ws://localhost:8080" placeholder="WebSocket URL">
            <button id="connectBtn">连接</button>
            <button id="disconnectBtn">断开</button>
            <button id="clearBtn">清除结果</button>
        </div>
        
        <div id="status" class="status disconnected">
            未连接
        </div>
        
        <div class="results-container" id="resultsContainer">
            <!-- 检测结果卡片将在这里动态添加 -->
        </div>
        
        <h3>最新JSON数据：</h3>
        <pre id="rawJson">无数据</pre>
    </div>

    <script>
        // DOM元素
        const wsUrlInput = document.getElementById('wsUrl');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const clearBtn = document.getElementById('clearBtn');
        const statusDiv = document.getElementById('status');
        const resultsContainer = document.getElementById('resultsContainer');
        const rawJsonPre = document.getElementById('rawJson');
        
        // WebSocket连接
        let ws = null;
        
        // 相机结果缓存
        const cameraResults = new Map();
        
        // 连接WebSocket
        function connectWebSocket() {
            if (ws) {
                ws.close();
            }
            
            const url = wsUrlInput.value;
            updateStatus('connecting', '正在连接...');
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function() {
                    updateStatus('connected', '已连接');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        processDetectionResult(data);
                        rawJsonPre.textContent = JSON.stringify(data, null, 2);
                    } catch (e) {
                        console.error('解析JSON失败:', e);
                        rawJsonPre.textContent = '解析失败: ' + event.data;
                    }
                };
                
                ws.onclose = function() {
                    updateStatus('disconnected', '连接已关闭');
                    ws = null;
                };
                
                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    updateStatus('disconnected', '连接错误');
                };
            } catch (e) {
                console.error('创建WebSocket失败:', e);
                updateStatus('disconnected', '创建连接失败');
            }
        }
        
        // 断开WebSocket连接
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                updateStatus('disconnected', '已断开连接');
            }
        }
        
        // 更新连接状态
        function updateStatus(className, message) {
            statusDiv.className = 'status ' + className;
            statusDiv.textContent = message;
        }
        
        // 处理检测结果
        function processDetectionResult(data) {
            const timestamp = data.timestamp;
            const persons = data.persons || [];
            
            // 获取相机ID（假设每个person对象都有相同的camIdx）
            const cameraId = persons.length > 0 ? persons[0].camIdx : 'unknown';
            
            // 更新或创建相机结果卡片
            let resultCard = document.getElementById('camera-' + cameraId);
            
            if (!resultCard) {
                resultCard = document.createElement('div');
                resultCard.id = 'camera-' + cameraId;
                resultCard.className = 'result-card';
                resultCard.innerHTML = `
                    <div class="result-header">
                        <span class="camera-id">相机 ${cameraId}</span>
                        <span class="timestamp" id="timestamp-${cameraId}"></span>
                    </div>
                    <div class="detection-list" id="detections-${cameraId}"></div>
                `;
                resultsContainer.appendChild(resultCard);
            }
            
            // 更新时间戳
            const timestampElem = document.getElementById('timestamp-' + cameraId);
            const date = new Date(timestamp);
            timestampElem.textContent = date.toLocaleTimeString();
            
            // 更新检测列表
            const detectionsList = document.getElementById('detections-' + cameraId);
            detectionsList.innerHTML = '';
            
            persons.forEach(person => {
                const detectionItem = document.createElement('div');
                detectionItem.className = 'detection-item';
                detectionItem.innerHTML = `
                    <div>ID: ${person.id}</div>
                    <div>位置: [${person.pos.join(', ')}]</div>
                `;
                detectionsList.appendChild(detectionItem);
            });
            
            if (persons.length === 0) {
                detectionsList.innerHTML = '<div>无检测结果</div>';
            }
        }
        
        // 清除所有结果
        function clearResults() {
            resultsContainer.innerHTML = '';
            rawJsonPre.textContent = '无数据';
        }
        
        // 事件监听器
        connectBtn.addEventListener('click', connectWebSocket);
        disconnectBtn.addEventListener('click', disconnectWebSocket);
        clearBtn.addEventListener('click', clearResults);
        
        // 页面加载时自动连接（可选）
        // window.addEventListener('load', connectWebSocket);
    </script>
</body>
</html>
