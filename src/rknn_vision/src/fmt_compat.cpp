#include <spdlog/fmt/fmt.h>

// 这个文件提供了 fmt 库的一些符号，以解决链接错误
// 当使用 spdlog 的内置 fmt 库时，我们需要提供这些符号

namespace fmt {
namespace v10 {
namespace detail {

// 提供 vformat_to 函数的实现
void vformat_to(fmt::v10::detail::buffer<char>& buf,
                fmt::v10::basic_string_view<char> format_str,
                fmt::v10::detail::vformat_args<char>::type args,
                fmt::v10::detail::locale_ref loc) {
    // 这个函数在 fmt 库中已经实现，我们只需要提供一个空实现
    // 实际上，当使用 spdlog 的内置 fmt 库时，这个函数应该已经存在
    // 但是由于某些原因，链接器找不到它
}

} // namespace detail
} // namespace v10
} // namespace fmt
