#include "stream_manager.h"

// StreamInfo::updateFrameCount 方法实现
void StreamInfo::updateFrameCount(bool hasFrame) {
    // 增加帧计数
    if (hasFrame) {
        frameCount++;
    }

    // 计算FPS - 每秒更新一次
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastFpsUpdateTime).count();

    // 每1000毫秒（1秒）更新一次FPS
    if (elapsed >= 1000) {
        std::lock_guard<std::mutex> lock(*fpsMutex);

        // 重新检查时间差，因为可能在获取锁的过程中已经被其他线程更新
        auto now2 = std::chrono::steady_clock::now();
        auto elapsed2 = std::chrono::duration_cast<std::chrono::milliseconds>(now2 - lastFpsUpdateTime).count();

        if (elapsed2 >= 1000) { // 确保至少过了1秒
            // 计算FPS: 帧数 / 经过的秒数
            float seconds = elapsed2 / 1000.0f;
            float fps = static_cast<float>(frameCount.load()) / seconds;

            // 更新FPS和时间戳
            currentFps.store(fps);
            lastFpsUpdateTime = now2;
            // 重置帧计数
            frameCount.store(0);
            // 输出当前FPS
            //std::cout << "Stream [" << name << "] FPS: " << fps << std::endl;
        }
    }
}

StreamManager::StreamManager() = default;

StreamManager::~StreamManager() {
    // First stop all active streams
    stopAllStreams();

    // Then clear all stream resources
    std::lock_guard<std::mutex> lock(streamsMutex);
    streams.clear();

    std::cout << "StreamManager destroyed, all resources released" << std::endl;
}

bool StreamManager::addStream(const std::string& url, const std::string& name,
                            int roi_x1, int roi_y1, int roi_x2, int roi_y2,
                            const std::string& camId,
                            const std::vector<std::string>& targetClassNames,
                            bool showGui) {
    std::lock_guard<std::mutex> lock(streamsMutex);

    // 检查URL是否已存在
    if (streams.find(url) != streams.end()) {
        std::cout << "Stream with URL " << url << " already exists" << std::endl;
        return false;
    }

    // 直接在map中构造StreamInfo对象
    auto result = streams.emplace(std::piecewise_construct,
                                std::forward_as_tuple(url),
                                std::forward_as_tuple());

    // 获取对象引用
    StreamInfo& streamInfo = result.first->second;

    // 设置属性
    streamInfo.url = url;
    streamInfo.name = name.empty() ? "Stream_" + std::to_string(streams.size()) : name;
    streamInfo.camId = camId;
    streamInfo.targetClassNames = targetClassNames; // 设置目标类别名称

    // 设置ROI区域
    streamInfo.roi_x1 = roi_x1;
    streamInfo.roi_y1 = roi_y1;
    streamInfo.roi_x2 = roi_x2;
    streamInfo.roi_y2 = roi_y2;

    // 创建FFmpegStreamChannel
    // 使用streamInfo.name作为channelId
    streamInfo.channel = std::make_shared<FFmpegStreamChannel>(streamInfo.camId, streamInfo.name);
    if (!streamInfo.channel) {
        std::cout << "Failed to create FFmpegStreamChannel for " << url << std::endl;
        streams.erase(url);
        return false;
    }

    // 设置窗口名称
    streamInfo.channel->window_name = streamInfo.name;

    // 创建异步解码器，传入目标类别名称和showGui参数
    streamInfo.decoder = std::make_shared<AsyncFFmpegDecoder>(
        streamInfo.channel.get(), 2000, 1, streamInfo.targetClassNames, showGui);
    if (!streamInfo.decoder) {
        std::cout << "Failed to create AsyncFFmpegDecoder for " << url << std::endl;
        streams.erase(url);
        return false;
    }

    // 设置StreamInfo指针到解码器，用于FPS更新
    streamInfo.decoder->setStreamInfo(&streamInfo);

    std::cout << "Added stream: " << url << " with name: " << streamInfo.name
              << ", target classes: ";
    for (const auto& className : streamInfo.targetClassNames) {
        std::cout << className << " ";
    }
    std::cout << ", showGui: " << (showGui ? "true" : "false") << std::endl;

    return true;
}

bool StreamManager::removeStream(const std::string& url) {
    // 先停止流，不持有锁
    {
        std::lock_guard<std::mutex> lock(streamsMutex);
        auto it = streams.find(url);
        if (it == streams.end()) {
            return false;
        }

        if (it->second.active) {
            // 在锁内获取解码器引用，但在锁外调用stop
            auto decoder = it->second.decoder;
            bool& active = it->second.active;

            // 释放锁后停止流
            {
                // 释放锁
                lock.~lock_guard();

                try {
                    // 设置超时机制来停止解码器
                    std::atomic<bool> stopComplete(false);
                    std::thread stopThread([&]() {
                        try {
                            decoder->stop();
                            stopComplete.store(true);
                        } catch (const std::exception& e) {
                            std::cerr << "Exception in decoder stop: " << e.what() << std::endl;
                        } catch (...) {
                            std::cerr << "Unknown exception in decoder stop" << std::endl;
                        }
                    });

                    // 等待停止完成或超时
                    const int stopTimeoutSec = 3; // 3秒超时
                    for (int i = 0; i < stopTimeoutSec && !stopComplete.load(); i++) {
                        std::this_thread::sleep_for(std::chrono::seconds(1));
                        std::cout << "Waiting for stream " << url << " to stop... " << (i+1) << "/" << stopTimeoutSec << std::endl;
                    }

                    // 如果超时，不再等待
                    if (!stopComplete.load()) {
                        std::cerr << "Stream " << url << " shutdown timed out. Proceeding anyway." << std::endl;
                        stopThread.detach();
                    } else {
                        stopThread.join();
                    }
                } catch (const std::exception& e) {
                    std::cerr << "Exception while stopping stream " << url << ": " << e.what() << std::endl;
                }

                // 重新获取锁
                new (&lock) std::lock_guard<std::mutex>(streamsMutex);
            }

            // 标记为非活动
            active = false;
        }

        // 移除流
        streams.erase(it);
    }

    return true;
}

bool StreamManager::startStream(const std::string& url) {
    // 获取流信息和解码器引用
    std::shared_ptr<AsyncFFmpegDecoder> decoder;
    std::string streamUrl;
    bool alreadyActive = false;

    {
        std::lock_guard<std::mutex> lock(streamsMutex);

        auto it = streams.find(url);
        if (it == streams.end()) {
            return false;
        }

        if (it->second.active) {
            alreadyActive = true;
        } else if (it->second.decoder) {
            decoder = it->second.decoder;
            streamUrl = it->second.url;
        } else {
            return false;
        }
    }

    // 如果流已经活动，直接返回
    if (alreadyActive) {
        return true;
    }

    // 在锁外启动流
    bool success = false;
    try {
        success = decoder->start(streamUrl.c_str());

        if (success) {
            std::cout << "Started stream: " << url << std::endl;
        } else {
            std::cout << "Failed to start stream: " << url << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "Exception while starting stream " << url << ": " << e.what() << std::endl;
        success = false;
    } catch (...) {
        std::cerr << "Unknown exception while starting stream " << url << std::endl;
        success = false;
    }

    // 更新流状态
    {
        std::lock_guard<std::mutex> lock(streamsMutex);
        auto it = streams.find(url);
        if (it != streams.end()) {
            it->second.active = success;
        }
    }

    return success;
}

bool StreamManager::stopStream(const std::string& url) {
    // 获取流信息和解码器引用
    std::shared_ptr<AsyncFFmpegDecoder> decoder;
    bool isActive = false;

    {
        std::lock_guard<std::mutex> lock(streamsMutex);

        auto it = streams.find(url);
        if (it == streams.end()) {
            return false;
        }

        if (it->second.active && it->second.decoder) {
            decoder = it->second.decoder;
            isActive = true;
        } else {
            return false;
        }
    }

    // 如果流不活动，直接返回
    if (!isActive) {
        return false;
    }

    // 在锁外停止流
    bool success = false;
    try {
        // 设置超时机制来停止解码器
        std::atomic<bool> stopComplete(false);
        std::thread stopThread([&]() {
            try {
                decoder->stop();
                stopComplete.store(true);
            } catch (const std::exception& e) {
                std::cerr << "Exception in decoder stop: " << e.what() << std::endl;
            } catch (...) {
                std::cerr << "Unknown exception in decoder stop" << std::endl;
            }
        });

        // 等待停止完成或超时
        const int stopTimeoutSec = 3; // 3秒超时
        for (int i = 0; i < stopTimeoutSec && !stopComplete.load(); i++) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            std::cout << "Waiting for stream " << url << " to stop... " << (i+1) << "/" << stopTimeoutSec << std::endl;
        }

        // 如果超时，不再等待
        if (!stopComplete.load()) {
            std::cerr << "Stream " << url << " shutdown timed out. Proceeding anyway." << std::endl;
            stopThread.detach();
        } else {
            stopThread.join();
            success = true;
        }
    } catch (const std::exception& e) {
        std::cerr << "Exception while stopping stream " << url << ": " << e.what() << std::endl;
        success = false;
    } catch (...) {
        std::cerr << "Unknown exception while stopping stream " << url << std::endl;
        success = false;
    }

    // 更新流状态
    {
        std::lock_guard<std::mutex> lock(streamsMutex);
        auto it = streams.find(url);
        if (it != streams.end()) {
            it->second.active = false; // 无论成功与否都标记为非活动
        }
    }

    if (success) {
        std::cout << "Stopped stream: " << url << std::endl;
    }

    return success;
}

bool StreamManager::startAllStreams() {
    // 首先获取所有需要启动的流的信息
    std::vector<std::pair<std::string, StreamInfo*>> streamsToStart;

    {
        std::lock_guard<std::mutex> lock(streamsMutex);
        for (auto& pair : streams) {
            if (!pair.second.active && pair.second.decoder) {
                streamsToStart.push_back(std::make_pair(pair.first, &pair.second));
            }
        }
    }

    // 在锁外启动所有流
    bool allStarted = true;
    std::vector<std::string> failedStreams;

    for (auto& streamPair : streamsToStart) {
        const std::string& url = streamPair.first;
        StreamInfo* streamInfo = streamPair.second;

        try {
            bool started = false;

            // 尝试启动流
            if (streamInfo->decoder->start(streamInfo->url.c_str())) {
                started = true;
                std::cout << "Started stream: " << url << std::endl;
            } else {
                std::cout << "Failed to start stream: " << url << std::endl;
            }

            // 更新流状态
            {
                std::lock_guard<std::mutex> lock(streamsMutex);
                auto it = streams.find(url);
                if (it != streams.end()) {
                    it->second.active = started;
                }
            }

            if (!started) {
                allStarted = false;
                failedStreams.push_back(url);
                std::cerr << "Failed to start stream: " << url << std::endl;
            } else {
                std::cout << "Successfully started stream: " << url << std::endl;
            }
        } catch (const std::exception& e) {
            allStarted = false;
            failedStreams.push_back(url);
            std::cerr << "Exception while starting stream " << url << ": " << e.what() << std::endl;
        } catch (...) {
            allStarted = false;
            failedStreams.push_back(url);
            std::cerr << "Unknown exception while starting stream " << url << std::endl;
        }
    }

    if (!failedStreams.empty()) {
        std::cerr << "Failed to start " << failedStreams.size() << " stream(s)" << std::endl;
    }

    return allStarted;
}

void StreamManager::stopAllStreams() {
    // 首先获取所有流的URL和解码器引用
    std::vector<std::pair<std::string, std::shared_ptr<AsyncFFmpegDecoder>>> streamsToStop;

    {
        std::lock_guard<std::mutex> lock(streamsMutex);
        for (auto& pair : streams) {
            if (pair.second.active && pair.second.decoder) {
                streamsToStop.push_back(std::make_pair(pair.first, pair.second.decoder));
            }
        }
    }

    // 在锁外停止所有流
    std::vector<std::string> failedStreams;
    for (auto& streamPair : streamsToStop) {
        const std::string& url = streamPair.first;
        auto decoder = streamPair.second;

        try {
            std::cout << "Stopping stream: " << url << std::endl;

            // 设置超时机制来停止解码器
            std::atomic<bool> stopComplete(false);
            std::thread stopThread([&]() {
                try {
                    decoder->stop();
                    stopComplete.store(true);
                } catch (const std::exception& e) {
                    std::cerr << "Exception in decoder stop: " << e.what() << std::endl;
                } catch (...) {
                    std::cerr << "Unknown exception in decoder stop" << std::endl;
                }
            });

            // 等待停止完成或超时
            const int stopTimeoutSec = 3; // 3秒超时
            for (int i = 0; i < stopTimeoutSec && !stopComplete.load(); i++) {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                std::cout << "Waiting for stream " << url << " to stop... " << (i+1) << "/" << stopTimeoutSec << std::endl;
            }

            // 如果超时，不再等待
            if (!stopComplete.load()) {
                std::cerr << "Stream " << url << " shutdown timed out. Proceeding anyway." << std::endl;
                stopThread.detach();
                failedStreams.push_back(url);
            } else {
                stopThread.join();
                std::cout << "Successfully stopped stream: " << url << std::endl;
            }
        } catch (const std::exception& e) {
            failedStreams.push_back(url);
            std::cerr << "Exception while stopping stream " << url << ": " << e.what() << std::endl;
        } catch (...) {
            failedStreams.push_back(url);
            std::cerr << "Unknown exception while stopping stream " << url << std::endl;
        }
    }

    // 最后标记所有流为非活动
    {
        std::lock_guard<std::mutex> lock(streamsMutex);
        for (auto& pair : streams) {
            pair.second.active = false;
        }
    }

    if (!failedStreams.empty()) {
        std::cerr << "Failed to stop " << failedStreams.size() << " stream(s)" << std::endl;
    }
}

std::vector<StreamInfoView> StreamManager::getStreamInfoList() {
    std::lock_guard<std::mutex> lock(streamsMutex);
    std::vector<StreamInfoView> streamList;

    for (const auto& pair : streams) {
        // 使用createView方法创建可复制的视图
        streamList.push_back(pair.second.createView());
    }

    return streamList;
}

size_t StreamManager::getStreamCount() {
    std::lock_guard<std::mutex> lock(streamsMutex);
    return streams.size();
}

bool StreamManager::isStreamActive(const std::string& url) {
    std::lock_guard<std::mutex> lock(streamsMutex);
    auto it = streams.find(url);
    return (it != streams.end()) && it->second.active;
}

float StreamManager::getStreamFps(const std::string& url) {
    std::lock_guard<std::mutex> lock(streamsMutex);
    auto it = streams.find(url);
    if (it != streams.end()) {
        return it->second.getFps();
    }
    return 0.0f; // 如果流不存在，返回0
}
