// Copyright (c) 2021 by Rockchip Electronics Co., Ltd. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "rga_func.h"
#include "rga_utils.h"

#include <algorithm>

int rknn_rga_init(rga_context *rga_ctx)
{
    rga_ctx->rga_handle = dlopen("/usr/lib/aarch64-linux-gnu/librga.so.2", RTLD_LAZY);
    if (!rga_ctx->rga_handle)
    {
        printf("dlopen /usr/lib/aarch64-linux-gnu/librga.so failed\n");
        return -1;
    }
    rga_ctx->init_func = (FUNC_RGA_INIT)dlsym(rga_ctx->rga_handle, "c_RkRgaInit");
    rga_ctx->deinit_func = (FUNC_RGA_DEINIT)dlsym(rga_ctx->rga_handle, "c_RkRgaDeInit");
    rga_ctx->blit_func = (FUNC_RGA_BLIT)dlsym(rga_ctx->rga_handle, "c_RkRgaBlit");
    rga_ctx->init_func();
    return 0;
}

int rknn_img_resize_phy_to_phy(rga_context *rga_ctx, int src_fd, int src_w, int src_h, int src_fmt, uint64_t dst_fd, int dst_w, int dst_h, int dst_fmt)
{
    // Lock the global RGA mutex
    std::lock_guard<std::mutex> lock(g_rga_mutex);

    int ret = 0;

    if (rga_ctx->rga_handle)
    {
        rga_info_t src, dst;

        memset(&src, 0, sizeof(rga_info_t));
        src.fd = src_fd;
        src.mmuFlag = 1;
        // src.rotation = rotation;

        memset(&dst, 0, sizeof(rga_info_t));
        dst.fd = dst_fd;
        dst.mmuFlag = 0;
        dst.nn.nn_flag = 0;

        rga_set_rect(&src.rect, 0, 0, src_w, src_h, src_w, src_h, src_fmt);
        rga_set_rect(&dst.rect, 0, 0, dst_w, dst_h, dst_w, dst_h, dst_fmt);

        ret = rga_ctx->blit_func(&src, &dst, NULL);

        // Add error logging
        if (ret < 0) {
            printf("RGA_BLIT failed: %d, src_fd=%d, dst_fd=%d\n", ret, src_fd, (int)dst_fd);
        }

        return ret;
    }
    return ret;
}

int rknn_img_resize_phy_to_virt(rga_context *rga_ctx, int src_fd, int src_w, int src_h, int src_fmt, void *dst_virt, int dst_w, int dst_h, int dst_fmt)
{
    // Lock the global RGA mutex
    std::lock_guard<std::mutex> lock(g_rga_mutex);

    int ret = 0;

    if (rga_ctx->rga_handle)
    {
        rga_info_t src, dst;

        memset(&src, 0, sizeof(rga_info_t));
        src.fd = src_fd;
        src.mmuFlag = 1;
        // src.rotation = rotation;

        memset(&dst, 0, sizeof(rga_info_t));
        dst.fd = -1;
        dst.mmuFlag = 1;
        dst.virAddr = dst_virt;
        dst.nn.nn_flag = 0;

        rga_set_rect(&src.rect, 0, 0, src_w, src_h, src_w, src_h, src_fmt);
        rga_set_rect(&dst.rect, 0, 0, dst_w, dst_h, dst_w, dst_h, dst_fmt);

        ret = rga_ctx->blit_func(&src, &dst, NULL);

        // Add error logging
        if (ret < 0) {
            printf("RGA_BLIT failed: %d, src_fd=%d, dst_virt=%p\n", ret, src_fd, dst_virt);
        }

        return ret;
    }
    return ret;
}

int rknn_img_resize_virt_to_phy(rga_context *rga_ctx, void *src_virt, int src_w, int src_h, int src_fmt, uint64_t dst_fd, int dst_w, int dst_h, int dst_fmt)
{
    // Lock the global RGA mutex
    std::lock_guard<std::mutex> lock(g_rga_mutex);

    int ret = 0;

    if (rga_ctx->rga_handle)
    {
        rga_info_t src, dst;

        memset(&src, 0, sizeof(rga_info_t));
        src.fd = -1;
        src.mmuFlag = 1;
        src.virAddr = (void *)src_virt;
        // src.rotation = rotation;

        memset(&dst, 0, sizeof(rga_info_t));
        dst.fd = dst_fd;
        dst.mmuFlag = 0;
        dst.nn.nn_flag = 0;

        rga_set_rect(&src.rect, 0, 0, src_w, src_h, src_w, src_h, src_fmt);
        rga_set_rect(&dst.rect, 0, 0, dst_w, dst_h, dst_w, dst_h, dst_fmt);

        ret = rga_ctx->blit_func(&src, &dst, NULL);

        // Add error logging
        if (ret < 0) {
            printf("RGA_BLIT failed: %d, src_virt=%p, dst_fd=%d\n", ret, src_virt, (int)dst_fd);
        }

        return ret;
    }
    return ret;
}

int rknn_img_resize_virt_to_virt(rga_context *rga_ctx, void *src_virt, int src_w, int src_h, int src_fmt, void *dst_virt, int dst_w, int dst_h, int dst_fmt)
{
    // Lock the global RGA mutex
    std::lock_guard<std::mutex> lock(g_rga_mutex);

    int ret = 0;

    if (rga_ctx->rga_handle)
    {
        rga_info_t src, dst;

        memset(&src, 0, sizeof(rga_info_t));
        src.fd = -1;
        src.mmuFlag = 1;
        src.virAddr = (void *)src_virt;
        // src.rotation = rotation;

        memset(&dst, 0, sizeof(rga_info_t));
        dst.fd = -1;
        dst.mmuFlag = 1;
        dst.virAddr = dst_virt;
        dst.nn.nn_flag = 0;

        rga_set_rect(&src.rect, 0, 0, src_w, src_h, src_w, src_h, src_fmt);
        rga_set_rect(&dst.rect, 0, 0, dst_w, dst_h, dst_w, dst_h, dst_fmt);

        ret = rga_ctx->blit_func(&src, &dst, NULL);

        // Add error logging
        if (ret < 0) {
            printf("RGA_BLIT failed: %d, src_virt=%p, dst_virt=%p\n", ret, src_virt, dst_virt);
        }

        return ret;
    }
    return ret;
}

/**
 * 使用 RGA 裁剪图像并输出到物理内存
 *
 * @param rga_ctx RGA 上下文
 * @param src_fd 源图像文件描述符
 * @param src_w 源图像宽度
 * @param src_h 源图像高度
 * @param src_fmt 源图像格式
 * @param dst_fd 目标缓冲区文件描述符
 * @param dst_w 目标缓冲区宽度
 * @param dst_h 目标缓冲区高度
 * @param dst_fmt 目标图像格式
 * @param crop_x 裁剪区域左上角 x 坐标
 * @param crop_y 裁剪区域左上角 y 坐标
 * @param crop_w 裁剪区域宽度
 * @param crop_h 裁剪区域高度
 * @return 成功返回 0，失败返回错误码
 */
int rknn_img_crop_phy_to_phy(rga_context *rga_ctx,
                             int src_fd, int src_w, int src_h, int src_fmt,
                             uint64_t dst_fd, int dst_w, int dst_h, int dst_fmt,
                             int crop_x, int crop_y, int crop_w, int crop_h)
{

    // 检查源格式是否为 YUV
    bool is_yuv = (src_fmt == RK_FORMAT_YCbCr_420_SP ||
                   src_fmt == RK_FORMAT_YCrCb_420_SP ||
                   src_fmt == RK_FORMAT_YCbCr_422_SP ||
                   src_fmt == RK_FORMAT_YCrCb_422_SP);

    // 如果是 YUV 格式，应用对齐规则
    if (is_yuv) {
        // 对齐到 2 的倍数
        int aligned_crop_x = crop_x & ~1;  // 向下取整到 2 的倍数
        int aligned_crop_y = crop_y & ~1;  // 向下取整到 2 的倍数
        int aligned_crop_w = (crop_w + 1) & ~1;  // 向上取整到 2 的倍数
        int aligned_crop_h = (crop_h + 1) & ~1;  // 向上取整到 2 的倍数

        // 确保不超出源图像边界
        aligned_crop_w = std::min(aligned_crop_w, src_w - aligned_crop_x);
        aligned_crop_h = std::min(aligned_crop_h, src_h - aligned_crop_y);

        printf("YUV alignment: original crop [%d, %d, %d, %d] -> aligned [%d, %d, %d, %d]\n",
               crop_x, crop_y, crop_w, crop_h,
               aligned_crop_x, aligned_crop_y, aligned_crop_w, aligned_crop_h);

        // 使用对齐后的参数
        crop_x = aligned_crop_x;
        crop_y = aligned_crop_y;
        crop_w = aligned_crop_w;
        crop_h = aligned_crop_h;
    }

    int ret = 0;

    // 参数检查
    if (crop_x < 0 || crop_y < 0 ||
        crop_w <= 0 || crop_h <= 0 ||
        crop_x + crop_w > src_w ||
        crop_y + crop_h > src_h) {
        printf("Invalid crop parameters: x=%d, y=%d, w=%d, h=%d (source: w=%d, h=%d)\n",
               crop_x, crop_y, crop_w, crop_h, src_w, src_h);
        return -1;
        }

    // Lock the global RGA mutex
    std::lock_guard<std::mutex> lock(g_rga_mutex);

    if (rga_ctx->rga_handle)
    {
        rga_info_t src, dst;

        memset(&src, 0, sizeof(rga_info_t));
        src.fd = src_fd;
        src.mmuFlag = 1;
        // src.rotation = rotation;  // 如果需要旋转，可以在这里设置

        memset(&dst, 0, sizeof(rga_info_t));
        dst.fd = dst_fd;
        dst.mmuFlag = 0;
        dst.nn.nn_flag = 0;

        // 设置源矩形，指定裁剪区域
        // 参数: rect, x偏移, y偏移, 宽度, 高度, 内存步长宽, 内存步长高, 格式
        rga_set_rect(&src.rect, crop_x, crop_y, crop_w, crop_h, src_w, src_h, src_fmt);

        // 设置目标矩形
        // 如果需要同时缩放，可以设置不同的宽高
        rga_set_rect(&dst.rect, 0, 0, dst_w, dst_h, dst_w, dst_h, dst_fmt);

        // 执行 blit 操作
        ret = rga_ctx->blit_func(&src, &dst, NULL);

        // Add error logging
        if (ret < 0) {
            printf("RGA_BLIT failed: %d, src_fd=%d, dst_fd=%d, crop=[%d,%d,%d,%d]\n",
                   ret, src_fd, (int)dst_fd, crop_x, crop_y, crop_w, crop_h);
        }

        return ret;
    }
    return ret;
}



/**
 * 使用 RGA 裁剪图像并保持原始尺寸输出到物理内存
 */
int rknn_img_crop_no_scale_phy_to_phy(rga_context *rga_ctx,
                                      int src_fd, int src_w, int src_h, int src_fmt,
                                      uint64_t dst_fd, int dst_w, int dst_h, int dst_fmt,
                                      int crop_x, int crop_y, int crop_w, int crop_h)
{
    // 参数检查
    if (crop_x < 0 || crop_y < 0 ||
        crop_w <= 0 || crop_h <= 0 ||
        crop_x + crop_w > src_w ||
        crop_y + crop_h > src_h ||
        crop_w > dst_w || crop_h > dst_h) {
        printf("Invalid crop parameters\n");
        return -1;
        }

    // Lock the global RGA mutex
    std::lock_guard<std::mutex> lock(g_rga_mutex);

    int ret = 0;
    if (rga_ctx->rga_handle)
    {
        rga_info_t src, dst;

        memset(&src, 0, sizeof(rga_info_t));
        src.fd = src_fd;
        src.mmuFlag = 1;

        memset(&dst, 0, sizeof(rga_info_t));
        dst.fd = dst_fd;
        dst.mmuFlag = 0;
        dst.nn.nn_flag = 0;

        // 设置源矩形，指定裁剪区域
        rga_set_rect(&src.rect, crop_x, crop_y, crop_w, crop_h, src_w, src_h, src_fmt);

        // 设置目标矩形，使用相同的宽高（不缩放）
        // 可以指定在目标缓冲区中的位置
        int dst_x = 0;  // 可以自定义目标位置
        int dst_y = 0;
        rga_set_rect(&dst.rect, dst_x, dst_y, crop_w, crop_h, dst_w, dst_h, dst_fmt);

        ret = rga_ctx->blit_func(&src, &dst, NULL);

        // Add error logging
        if (ret < 0) {
            printf("RGA_BLIT failed: %d, src_fd=%d, dst_fd=%d, crop=[%d,%d,%d,%d]\n",
                   ret, src_fd, (int)dst_fd, crop_x, crop_y, crop_w, crop_h);
        }

        return ret;
    }
    return ret;
}

/**
 * 使用 RGA 裁剪图像并居中放置在目标缓冲区中
 */
int rknn_img_crop_centered_phy_to_phy(rga_context *rga_ctx,
                                      int src_fd, int src_w, int src_h, int src_fmt,
                                      uint64_t dst_fd, int dst_w, int dst_h, int dst_fmt,
                                      int crop_x, int crop_y, int crop_w, int crop_h)
{
    // 参数检查
    if (crop_x < 0 || crop_y < 0 ||
        crop_w <= 0 || crop_h <= 0 ||
        crop_x + crop_w > src_w ||
        crop_y + crop_h > src_h) {
        printf("Invalid crop parameters\n");
        return -1;
        }

    // Lock the global RGA mutex
    std::lock_guard<std::mutex> lock(g_rga_mutex);

    int ret = 0;
    if (rga_ctx->rga_handle)
    {
        rga_info_t src, dst;

        memset(&src, 0, sizeof(rga_info_t));
        src.fd = src_fd;
        src.mmuFlag = 1;

        memset(&dst, 0, sizeof(rga_info_t));
        dst.fd = dst_fd;
        dst.mmuFlag = 0;
        dst.nn.nn_flag = 0;

        // 设置源矩形，指定裁剪区域
        rga_set_rect(&src.rect, crop_x, crop_y, crop_w, crop_h, src_w, src_h, src_fmt);

        // 计算目标位置，使裁剪区域居中
        int dst_x = (dst_w - crop_w) / 2;
        int dst_y = (dst_h - crop_h) / 2;

        // 确保目标位置不为负
        dst_x = dst_x < 0 ? 0 : dst_x;
        dst_y = dst_y < 0 ? 0 : dst_y;

        // 设置目标矩形
        rga_set_rect(&dst.rect, dst_x, dst_y, crop_w, crop_h, dst_w, dst_h, dst_fmt);

        // 如果需要，可以先清空目标缓冲区（填充黑色背景）
        // 这需要额外的 RGA 操作

        ret = rga_ctx->blit_func(&src, &dst, NULL);

        // Add error logging
        if (ret < 0) {
            printf("RGA_BLIT failed: %d, src_fd=%d, dst_fd=%d, crop=[%d,%d,%d,%d]\n",
                   ret, src_fd, (int)dst_fd, crop_x, crop_y, crop_w, crop_h);
        }

        return ret;
    }
    return ret;
}



int rknn_rga_deinit(rga_context *rga_ctx)
{
    if (rga_ctx->rga_handle)
    {
        dlclose(rga_ctx->rga_handle);
        rga_ctx->rga_handle = NULL;
    }
    return 0;
}
