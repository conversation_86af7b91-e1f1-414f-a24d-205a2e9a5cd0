#include "ffmpeg_decode2_async.h"
#include <chrono>
#include <iostream>
#include "stream_manager.h"
#include "rknn_utils.h"

// 构造函数
AsyncFFmpegDecoder::AsyncFFmpegDecoder(FFmpegStreamChannel *channel, int queue_size, int num_threads,
                                       const std::vector<std::string> &targetClasses, bool showGui)
    : channel(channel),
      maxQueueSize(queue_size),
      numThreads(num_threads),
      running(false),
      targetClassNames(targetClasses),
      showGUIResult(showGui) {
    // 创建显示用的Mat
    mat4show = std::make_shared<cv::Mat>(cv::Size(WIDTH_P, HEIGHT_P), CV_8UC3, channel->drm_buf_for_rga2.drm_buf_ptr);

    // 创建回调线程池，使用固定数量的线程（可以根据需要调整）
    callbackThreadPool = std::make_shared<ThreadPool>(2); // 使用2个线程的线程池
}

// 析构函数
AsyncFFmpegDecoder::~AsyncFFmpegDecoder() {
    stop();
}

// 启动异步解码
bool AsyncFFmpegDecoder::start(const char *input_stream_url) {
    if (running.load()) {
        std::cout << "Decoder is already running" << std::endl;
        return false;
    }

    running.store(true);
    frameQueue.setStop(false);

    // 启动处理线程
    processThreads.clear();
    for (int i = 0; i < numThreads; i++) {
        processThreads.emplace_back(&AsyncFFmpegDecoder::processThreadFunc, this);
    }

    // 启动解码线程
    decodeThread = std::thread(&AsyncFFmpegDecoder::decodeThreadFunc, this, input_stream_url);

    return true;
}

// 停止异步解码
void AsyncFFmpegDecoder::stop() {
    if (!running.load()) {
        return;
    }

    std::cout << "Stopping AsyncFFmpegDecoder..." << std::endl;
    running.store(false);
    frameQueue.setStop(true);

    // 设置超时机制来等待解码线程结束
    if (decodeThread.joinable()) {
        // 使用条件变量和互斥锁来安全地处理线程超时
        std::mutex mtx;
        std::condition_variable cv;
        bool threadExited = false;

        // 创建一个线程来等待解码线程结束
        std::thread joinThread([this, &mtx, &cv, &threadExited]() {
            try {
                decodeThread.join();
                std::cout << "Decode thread exited normally" << std::endl;

                // 通知主线程解码线程已退出
                std::lock_guard<std::mutex> lock(mtx);
                threadExited = true;
                cv.notify_one();
            } catch (const std::exception &e) {
                std::cerr << "Exception while joining decode thread: " << e.what() << std::endl;

                // 即使发生异常，也通知主线程
                std::lock_guard<std::mutex> lock(mtx);
                threadExited = true;
                cv.notify_one();
            }
        });

        // 等待解码线程退出或超时
        {
            std::unique_lock<std::mutex> lock(mtx);
            if (!cv.wait_for(lock, std::chrono::seconds(3), [&threadExited] { return threadExited; })) {
                // 超时，解码线程未退出
                std::cerr << "Decode thread did not exit in time, proceeding with cleanup" << std::endl;
                // 分离joinThread，让它在后台继续等待
                joinThread.detach();
            } else {
                // 解码线程已退出，等待joinThread完成
                joinThread.join();
            }
        }
    }

    // 使用超时机制等待处理线程结束
    for (size_t i = 0; i < processThreads.size(); i++) {
        if (processThreads[i].joinable()) {
            // 使用条件变量和互斥锁来安全地处理线程超时
            std::mutex mtx;
            std::condition_variable cv;
            bool threadExited = false;

            // 创建一个线程来等待处理线程结束
            std::thread joinThread([this, i, &mtx, &cv, &threadExited]() {
                try {
                    processThreads[i].join();
                    std::cout << "Process thread " << i << " exited normally" << std::endl;

                    // 通知主线程处理线程已退出
                    std::lock_guard<std::mutex> lock(mtx);
                    threadExited = true;
                    cv.notify_one();
                } catch (const std::exception &e) {
                    std::cerr << "Exception while joining process thread " << i << ": " << e.what() << std::endl;

                    // 即使发生异常，也通知主线程
                    std::lock_guard<std::mutex> lock(mtx);
                    threadExited = true;
                    cv.notify_one();
                }
            });

            // 等待处理线程退出或超时
            {
                std::unique_lock<std::mutex> lock(mtx);
                if (!cv.wait_for(lock, std::chrono::seconds(2), [&threadExited] { return threadExited; })) {
                    // 超时，处理线程未退出
                    std::cerr << "Process thread " << i << " did not exit in time, detaching" << std::endl;
                    // 分离joinThread，让它在后台继续等待
                    joinThread.detach();
                } else {
                    // 处理线程已退出，等待joinThread完成
                    joinThread.join();
                }
            }
        }
    }
    processThreads.clear();

    // 清空队列
    frameQueue.clear();
    std::cout << "AsyncFFmpegDecoder stopped" << std::endl;
}

// 解码线程函数
void AsyncFFmpegDecoder::decodeThreadFunc(const char *input_stream_url) {
    try {
        // 检查channel是否有效
        if (!channel) {
            std::cerr << "Error: Invalid channel pointer" << std::endl;
            return;
        }

        // 调用channel的decode方法来处理解码
        channel->decode(input_stream_url, &running, &frameQueue, maxQueueSize);
    } catch (const std::exception &e) {
        std::cerr << "Exception in decodeThreadFunc: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown exception in decodeThreadFunc" << std::endl;
    }

    std::cout << "Decode thread exiting" << std::endl;
}

// 处理线程函数 - 持续从队列获取帧并处理
void AsyncFFmpegDecoder::processThreadFunc() {
    try {
        while (running.load()) {
            // 定期检查running状态，确保可以快速响应停止请求
            // 从队列中获取帧，使用更短的超时时间
            std::shared_ptr<FrameData> frame_data;
            if (frameQueue.pop(frame_data, 200)) {
                // 200ms超时，减少以便更快响应停止请求
                if (!running.load()) break; // 再次检查是否应该停止
                processFrame(frame_data);
            } else {
                // 如果没有帧可处理，让出一些时间给其他线程
                std::this_thread::sleep_for(std::chrono::milliseconds(1000)); // 减少睡眠时间以更快响应停止
                if (streamInfo) {
                    streamInfo->updateFrameCount(false);
                }
                // 生成JSON字符串
                std::string json_result = detections2json({}, channel->channelId);
                // 调用回调函数将JSON结果传递给上层应用
                invokeDetectionCallback(json_result, channel->camId, channel->channelId);
            }
        }
    } catch (const std::exception &e) {
        std::cerr << "Exception in processThreadFunc: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown exception in processThreadFunc" << std::endl;
    }

    std::cout << "Process thread exiting" << std::endl;
}

// 处理单个帧的函数
void AsyncFFmpegDecoder::processFrame(std::shared_ptr<FrameData> frame_data) {
    if (!running.load() || !frame_data) {
        return;
    }
    // 更新帧计数和FPS统计
    if (streamInfo) {
        streamInfo->updateFrameCount(true);
    }

    long long ts_mark = 0;
    ts_mark = current_timestamp();
    // 调用RKNN处理函数
    detect_result_group_t detect_result_group = channel->process_frame_with_rknn(
        frame_data->fd,
        frame_data->width,
        frame_data->height,
        frame_data->pts,
        mat4show,
        ts_mark
    );

    //printf("PROCESS RESULT OK---->[%fms]\n", static_cast<double>(current_timestamp() - ts_mark) / 1000);
    // 处理识别结果

    // 使用缓存的ROI区域坐标，只在需要时更新
    int roi_x1, roi_y1, roi_x2, roi_y2;

    // 如果ROI需要更新或者帧尺寸发生变化，重新计算ROI
    if (roi_needs_update || cached_roi_x2 > frame_data->width || cached_roi_y2 > frame_data->height) {
        // 默认使用整个视频帧
        roi_x1 = 0;
        roi_y1 = 0;
        roi_x2 = frame_data->width;
        roi_y2 = frame_data->height;

        // 如果有StreamInfo并且设置了有效的ROI坐标，则使用这些坐标
        if (streamInfo) {
            // 如果设置了有效的ROI坐标（不全为0或者x2>x1且y2>y1）
            if ((streamInfo->roi_x2 > streamInfo->roi_x1 && streamInfo->roi_y2 > streamInfo->roi_y1) &&
                (streamInfo->roi_x2 != 0 || streamInfo->roi_y2 != 0)) {
                roi_x1 = streamInfo->roi_x1;
                roi_y1 = streamInfo->roi_y1;
                roi_x2 = streamInfo->roi_x2;
                roi_y2 = streamInfo->roi_y2;

                // 确保坐标在有效范围内
                roi_x1 = std::max(0, roi_x1);
                roi_y1 = std::max(0, roi_y1);
                roi_x2 = std::min(frame_data->width, roi_x2);
                roi_y2 = std::min(frame_data->height, roi_y2);
            }
        }

        // 更新缓存的ROI坐标
        cached_roi_x1 = roi_x1;
        cached_roi_y1 = roi_y1;
        cached_roi_x2 = roi_x2;
        cached_roi_y2 = roi_y2;

        // 重置更新标志
        roi_needs_update = false;
    } else {
        // 使用缓存的ROI坐标
        roi_x1 = cached_roi_x1;
        roi_y1 = cached_roi_y1;
        roi_x2 = cached_roi_x2;
        roi_y2 = cached_roi_y2;
    }

    std::vector<::detect_result_t> filtered_results = filterDetectionsByRoiAndClasses(
        roi_x1, roi_y1, roi_x2, roi_y2, targetClassNames, detect_result_group, frame_data->width,
        frame_data->height);

    // 生成JSON字符串
    std::string json_result = detections2json(filtered_results, channel->channelId);

    // 调用回调函数将JSON结果传递给上层应用
    invokeDetectionCallback(json_result, channel->camId, channel->channelId);

    if (!showGUIResult)
        return;
    // 在原始视频帧上绘制过滤后的检测结果
    channel->display_detection_results(frame_data->fd, mat4show, detect_result_group, frame_data->width,
                                       frame_data->height, frame_data->pts, ts_mark);
    drawFilteredDetectionResults(roi_x1, roi_y1, roi_x2, roi_y2, filtered_results, frame_data);
    //printf("PRINT RESULT OK---->[%fms]\n", static_cast<double>(current_timestamp() - ts_mark) / 1000);
    // 显示结果
    displayProcessedFrame(channel->window_name);
    //printf("SHOW RESULT OK---->[%fms]\n", static_cast<double>(current_timestamp() - ts_mark) / 1000);
}

// 根据ROI和类别名称过滤检测结果
std::vector<::detect_result_t> AsyncFFmpegDecoder::filterDetectionsByRoiAndClasses(
    int roi_x1, int roi_y1, int roi_x2, int roi_y2,
    const std::vector<std::string> &target_class_names, // 参数类型和名称已修改
    const ::detect_result_group_t &detect_result_group, int w, int h) {
    std::vector<::detect_result_t> filtered_results;

    if (roi_x1 > roi_x2 || roi_y1 > roi_y2) {
        printf("Invalid ROI parameters: (%d, %d, %d, %d)\n", roi_x1, roi_y1, roi_x2, roi_y2);
        return filtered_results;
    }

    float scale_display_x = (float) WIDTH_P / w;
    float scale_display_y = (float) HEIGHT_P / h;
    int display_roi_x1 = roi_x1 * scale_display_x;
    int display_roi_y1 = roi_y1 * scale_display_y;
    int display_roi_x2 = roi_x2 * scale_display_x;
    int display_roi_y2 = roi_y2 * scale_display_y;

    for (int i = 0; i < detect_result_group.count; i++) {
        const detect_result_t &det_result = detect_result_group.results[i];

        // 将 char[] 转换为 std::string 以便比较
        std::string current_class_name(det_result.name);

        // 检查当前检测结果的类别是否在目标类别列表中
        auto it = std::find(target_class_names.begin(), target_class_names.end(), current_class_name);

        if (it != target_class_names.end()) {
            // 如果找到了 (即类别匹配)
            int x1 = det_result.box.left;
            int y1 = det_result.box.top;
            int x2 = det_result.box.right;
            int y2 = det_result.box.bottom;
            int center_x = (x1 + x2) / 2;
            int center_y = (y1 + y2) / 2;

            // 检查中心点是否在ROI区域内
            if (center_x >= display_roi_x1 && center_x <= display_roi_x2 &&
                center_y >= display_roi_y1 && center_y <= display_roi_y2) {
                filtered_results.push_back(det_result);
            }
        }
    }
    return filtered_results;
}

// 在视频帧上绘制过滤后的检测结果
void AsyncFFmpegDecoder::drawFilteredDetectionResults(int roi_x, int roi_y, int roi_x2, int roi_y2,
                                                      const std::vector<::detect_result_t> &filtered_results,
                                                      std::shared_ptr<FrameData> frame_data) {
    std::lock_guard<std::mutex> lock(showMutex);
    // 绘制ROI区域 - 需要对ROI坐标进行缩放，与 filterDetectionsByRoiAndClasses 函数中的缩放一致
    float scale_display_x = (float) WIDTH_P / frame_data->width;
    float scale_display_y = (float) HEIGHT_P / frame_data->height;

    cv::rectangle(*mat4show.get(),
                  cv::Point(roi_x * scale_display_x, roi_y * scale_display_y),
                  cv::Point(roi_x2 * scale_display_x, roi_y2 * scale_display_y),
                  cv::Scalar(255, 255, 0), 2); // 黄色边框表示ROI区域

    // 绘制过滤后的检测结果
    for (const auto &det_result: filtered_results) {
        // 获取检测框的原始坐标
        int x1 = det_result.box.left;
        int y1 = det_result.box.top;
        int x2 = det_result.box.right;
        int y2 = det_result.box.bottom;

        // 绘制边界框 - 使用绿色表示过滤后的结果
        cv::rectangle(*mat4show.get(),
                      cv::Point(x1, y1),
                      cv::Point(x2, y2),
                      cv::Scalar(0, 255, 0), 2); // 绿色

        // 准备文本标签
        char text[256];
        sprintf(text, "%s %.1f%%", det_result.name, det_result.prop * 100);

        // 绘制文本标签
        cv::putText(*mat4show.get(), text,
                    cv::Point(x1, y1 - 5),
                    cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 255, 0), 2);
    }
}

// 显示处理后的视频帧
void AsyncFFmpegDecoder::displayProcessedFrame(const std::string &window_name) {
    cv::imshow(window_name, *mat4show.get());
    cv::waitKey(1);
}

// 调用检测结果回调函数
void AsyncFFmpegDecoder::invokeDetectionCallback(const std::string &json_result, const std::string &camera_id, const std::string &channel_id) {
    // 复制数据，避免引用已释放的内存
    std::string json_copy = json_result;
    std::string camera_id_copy = camera_id;
    std::string channel_id_copy = channel_id;

    // 获取回调函数的副本
    DetectionCallback callback; {
        std::lock_guard<std::mutex> lock(callbackMutex);
        if (!detectionCallback) {
            return; // 如果没有设置回调函数，直接返回
        }
        callback = detectionCallback;
    }

    // 使用线程池异步执行回调函数
    if (callbackThreadPool) {
        callbackThreadPool->enqueue([callback, json_copy, camera_id_copy, channel_id_copy]() {
            callback(json_copy, camera_id_copy, channel_id_copy);
        });
    } else {
        // 如果线程池不可用，则同步执行
        callback(json_copy, camera_id_copy, channel_id_copy);
    }
}
