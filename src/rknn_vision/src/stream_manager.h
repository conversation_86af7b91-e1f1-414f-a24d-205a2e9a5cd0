#ifndef __STREAM_MANAGER_H__
#define __STREAM_MANAGER_H__

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <mutex>
#include <chrono>
#include "ffmpeg.h"
#include "ffmpeg_decode2_async.h"

// 简化的流信息结构体，用于返回给外部调用者
struct StreamInfoView {
    std::string url;        // RTSP URL
    std::string name;       // 流名称
    bool active;            // 流是否活跃
    float fps;              // 当前FPS
    int roi_x1;             // ROI左上角X坐标
    int roi_y1;             // ROI左上角Y坐标
    int roi_x2;             // ROI右下角X坐标
    int roi_y2;             // ROI右下角Y坐标
    std::string camId;      // 摄像头ID
    std::string channelId;   // 流ID

    StreamInfoView(const std::string& _url, const std::string& _name, bool _active, float _fps,
                  int _roi_x1, int _roi_y1, int _roi_x2, int _roi_y2, const std::string& _camId = "", const std::string& _channelId = "")
        : url(_url), name(_name), active(_active), fps(_fps),
          roi_x1(_roi_x1), roi_y1(_roi_y1), roi_x2(_roi_x2), roi_y2(_roi_y2), camId(_camId), channelId(_channelId) {}
};

// 流信息结构体
struct StreamInfo {
    std::string url;                                // RTSP URL
    std::string name;                               // 流名称
    std::shared_ptr<FFmpegStreamChannel> channel;   // 流通道
    std::shared_ptr<AsyncFFmpegDecoder> decoder;    // 异步解码器
    bool active;                                    // 流是否活跃
    std::string camId;                              // 摄像头ID
    std::vector<std::string> targetClassNames;      // 目标类别名称列表

    // ROI区域坐标
    int roi_x1;                                     // ROI左上角X坐标
    int roi_y1;                                     // ROI左上角Y坐标
    int roi_x2;                                     // ROI右下角X坐标
    int roi_y2;                                     // ROI右下角Y坐标

    // FPS统计相关
    std::atomic<uint64_t> frameCount;               // 处理的总帧数
    std::atomic<float> currentFps;                  // 当前FPS
    std::chrono::steady_clock::time_point lastFpsUpdateTime; // 上次FPS更新时间
    std::unique_ptr<std::mutex> fpsMutex;           // FPS更新互斥锁

    // 更新帧计数并计算FPS
    void updateFrameCount(bool hasFrame);

    // 获取当前FPS
    float getFps() const { return currentFps.load(); }

    // 创建一个可复制的视图
    StreamInfoView createView() const {
        std::string channelId = channel ? channel->channelId : "";
        return StreamInfoView(url, name, active, getFps(), roi_x1, roi_y1, roi_x2, roi_y2, camId, channelId);
    }

    StreamInfo() : active(false), camId(""), roi_x1(0), roi_y1(0), roi_x2(0), roi_y2(0),
                   frameCount(0), currentFps(0.0f),
                   lastFpsUpdateTime(std::chrono::steady_clock::now()),
                   fpsMutex(std::make_unique<std::mutex>()),
                   targetClassNames({"person", "car"}) {} // 默认目标类别

    // 设置ROI区域
    void setRoi(int x1, int y1, int x2, int y2) {
        // 如果ROI发生变化，才通知解码器更新
        bool roi_changed = (roi_x1 != x1 || roi_y1 != y1 || roi_x2 != x2 || roi_y2 != y2);

        roi_x1 = x1;
        roi_y1 = y1;
        roi_x2 = x2;
        roi_y2 = y2;

        // 如果ROI发生变化且解码器存在，通知解码器更新ROI
        if (roi_changed && decoder) {
            decoder->notifyRoiUpdated();
        }
    }
};

// 流管理器类
class StreamManager {
public:
    StreamManager();
    ~StreamManager();

    // 添加新的流
    bool addStream(const std::string& url, const std::string& name = "",
                int roi_x1 = 0, int roi_y1 = 0, int roi_x2 = 0, int roi_y2 = 0, 
                const std::string& camId = "", 
                const std::vector<std::string>& targetClassNames = {"person", "car"},
                bool showGui = false);

    // 移除流
    bool removeStream(const std::string& url);

    // 启动所有流
    bool startAllStreams();

    // 停止所有流
    void stopAllStreams();

    // 启动指定流
    bool startStream(const std::string& url);

    // 停止指定流
    bool stopStream(const std::string& url);

    // 获取流信息
    std::vector<StreamInfoView> getStreamInfoList();

    // 获取流数量
    size_t getStreamCount();

    // 获取指定流的状态
    bool isStreamActive(const std::string& url);

    // 获取指定流的FPS
    float getStreamFps(const std::string& url);

    // 获取指定流的相机ID
    std::string getStreamCamId(const std::string& url) {
        std::lock_guard<std::mutex> lock(streamsMutex);
        auto it = streams.find(url);
        if (it != streams.end()) {
            return it->second.camId;
        }
        return ""; // 返回空字符串表示未找到指定的流
    }

    // 获取所有流的映射表（仅供内部使用）
    std::map<std::string, StreamInfo>& getStreams() {
        return streams;
    }

private:
    std::map<std::string, StreamInfo> streams;     // 流映射表 (URL -> StreamInfo)
    std::mutex streamsMutex;                       // 保护streams的互斥锁
};

#endif // __STREAM_MANAGER_H__
