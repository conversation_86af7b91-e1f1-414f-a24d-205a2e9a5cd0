#include "rknn_utils.h"

#include "config.h"
#include "json.hpp"

void dump_tensor_attr(rknn_tensor_attr *attr)
{
	printf("  index=%d, name=%s, n_dims=%d, dims=[%d, %d, %d, %d], n_elems=%d, size=%d, fmt=%s, type=%s, qnt_type=%s, "
	       "zp=%d, scale=%f\n",
	       attr->index, attr->name, attr->n_dims, attr->dims[0], attr->dims[1], attr->dims[2], attr->dims[3], attr->n_elems, attr->size, get_format_string(attr->fmt), get_type_string(attr->type),
	       get_qnt_type_string(attr->qnt_type), attr->zp, attr->scale);
}

unsigned char *load_data(FILE *fp, size_t ofst, size_t sz)
{
	unsigned char *data;
	int ret;

	data = NULL;

	if (NULL == fp) {
		return NULL;
	}

	ret = fseek(fp, ofst, SEEK_SET);
	if (ret != 0) {
		printf("blob seek failure.\n");
		return NULL;
	}

	data = (unsigned char *)malloc(sz);
	if (data == NULL) {
		printf("buffer malloc failure.\n");
		return NULL;
	}
	ret = fread(data, 1, sz, fp);
	if (ret != sz) {
		printf("fread failed, only read %d bytes of %zu.\n", ret, sz);
		free(data);
		return NULL;
	}
	return data;
}

unsigned char *load_model(const char *filename, int *model_size)
{
	FILE *fp;
	unsigned char *data = NULL;

	if (filename == NULL || model_size == NULL) {
		printf("Invalid parameters.\n");
		return NULL;
	}

	fp = fopen(filename, "rb");
	if (NULL == fp) {
		printf("Open file %s failed.\n", filename);
		return NULL;
	}

	// Get file size
	if (fseek(fp, 0, SEEK_END) != 0) {
		printf("Failed to seek to end of file.\n");
		fclose(fp);
		return NULL;
	}

	int size = ftell(fp);
	if (size <= 0) {
		printf("Invalid file size: %d\n", size);
		fclose(fp);
		return NULL;
	}

	// Load data
	data = load_data(fp, 0, size);
	fclose(fp);

	if (data == NULL) {
		printf("Failed to load data from file %s\n", filename);
		return NULL;
	}

	*model_size = size;
	return data;
}

std::string detections2json(const std::vector<detect_result_t> &detections, const std::string& camIdx){


	// 创建一个 JSON 对象
	nlohmann::json root;

	// 获取当前时间戳
	auto timestamp = getCurrentTimestampSec();
	root["timestamp"] = timestamp;


	// 如果输入为空，直接返回包含时间戳的 JSON 或空字符串（根据需求）
	if (detections.empty()) {
		// 可以选择返回只包含时间戳的 JSON: return root.dump();
		return root.dump();
		// 或者按原逻辑返回空字符串:
		//return "";
	}

	// 用于存储每个类别的下一个可用 ID
	std::map<std::string, int> class_id_counters;

	// 遍历所有过滤后的检测结果
	for (const auto& detection : detections) {
		// 获取当前检测结果的类别名称
		std::string class_name = std::string(detection.name) + 's';


		// 如果根 JSON 对象中还没有这个类别的数组，则创建它
		if (!root.contains(class_name)) {
			root[class_name] = nlohmann::json::array();
			class_id_counters[class_name] = 0; // 初始化该类别的 ID 计数器
		}

		// 创建代表单个检测结果的 JSON 对象
		nlohmann::json detection_object;
		// 使用特定于该类别的 ID 计数器
		detection_object["id"] = class_id_counters[class_name]++;
		detection_object["camIdx"] = camIdx;
		detection_object["pos"] = {
			detection.box.left, detection.box.top, detection.box.right,
			detection.box.bottom
		};
		// 注意：这里不再需要 "class_name" 字段，因为类别由顶层键表示

		// 将这个对象添加到对应类别的数组中
		root[class_name].push_back(detection_object);
	}
	// 返回最终构建的 JSON 字符串
	return root.dump();
}