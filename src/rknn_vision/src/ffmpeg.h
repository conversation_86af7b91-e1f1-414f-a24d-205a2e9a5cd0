#ifndef __FFMPEG_H__
#define __FFMPEG_H__

#include <errno.h>
#include <fcntl.h>
#include <linux/videodev2.h>
#include <signal.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string>
#include <iostream>
#include <sys/ioctl.h>
#include <cstdio>
#include <sys/mman.h>
#include <unistd.h>
#include <dlfcn.h>
#include <sys/time.h>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <memory>
#include <thread>
#include <vector>
#include <fcntl.h>
#include <errno.h>
#include <chrono>

#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/imgproc/types_c.h>
#include <opencv2/core/opengl.hpp>
#include <spdlog/fmt/bundled/chrono.h>

#include "rknn_api.h"
#include "yolov5s_postprocess.h"
#include "rknn_utils.h"

#ifdef __cplusplus
extern "C" {
#endif
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/avassert.h>
#include <libavutil/channel_layout.h>
#include <libavutil/hwcontext_drm.h>
#include <libavutil/imgutils.h>
#include <libavutil/mathematics.h>
#include <libavutil/opt.h>
#include <libavutil/timestamp.h>
#include <libswresample/swresample.h>
#include <libswscale/swscale.h>
#ifdef __cplusplus
}
#endif

#include "config.h"
#include "drm_func.h"
#include "rga_func.h"

// 自定义删除器，用于智能指针管理FFmpeg资源
struct AVFormatContextDeleter {
    void operator()(AVFormatContext* ctx) {
        if (ctx) avformat_close_input(&ctx);
    }
};

struct AVCodecContextDeleter {
    void operator()(AVCodecContext* ctx) {
        if (ctx) avcodec_free_context(&ctx);
    }
};

// 使用自定义删除器的智能指针类型定义
typedef std::shared_ptr<AVFormatContext> AVFormatContextPtr;
typedef std::shared_ptr<AVCodecContext> AVCodecContextPtr;

// 帧数据结构，用于在解码线程和处理线程之间传递数据
struct FrameData {
    int fd;                     // DRM帧文件描述符
    int width;                  // 帧宽度
    int height;                 // 帧高度
    int64_t pts;                // 时间戳

    FrameData(int _fd, int _w, int _h, int64_t _pts)
        : fd(_fd), width(_w), height(_h), pts(_pts) {}
};

// 线程安全的队列，用于存储待处理的帧
template<typename T>
class SafeQueue {
public:
    SafeQueue() : stop(false) {}

    // 析构函数，确保所有等待的线程被唤醒
    ~SafeQueue() {
        setStop(true);
    }

    void push(T item) {
        std::unique_lock<std::mutex> lock(mutex);
        if (stop) return;
        queue.push(std::move(item));
        lock.unlock();
        cond.notify_one();
    }

    bool pop(T& item, int timeout_ms = -1) {
        std::unique_lock<std::mutex> lock(mutex);
        if (timeout_ms < 0) {
            // 无限等待直到有数据或停止
            cond.wait(lock, [this] { return !queue.empty() || stop; });
        } else {
            // 有超时的等待
            if (!cond.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                              [this] { return !queue.empty() || stop; })) {
                return false; // 超时
            }
        }

        if (stop && queue.empty()) return false;
        if (queue.empty()) return false;

        item = std::move(queue.front());
        queue.pop();
        return true;
    }

    void clear() {
        std::unique_lock<std::mutex> lock(mutex);
        while (!queue.empty()) {
            queue.pop();
        }
    }

    size_t size() {
        std::unique_lock<std::mutex> lock(mutex);
        return queue.size();
    }

    void setStop(bool value) {
        std::unique_lock<std::mutex> lock(mutex);
        stop = value;
        lock.unlock();
        cond.notify_all(); // 唤醒所有等待的线程
    }

private:
    std::queue<T> queue;
    std::mutex mutex;
    std::condition_variable cond;
    bool stop;
};

class FFmpegStreamChannel {
    public:
	/* ffmpeg */
	AVFormatContextPtr format_context_input;
	int video_stream_index_input{};
	int audio_stream_index_input{};
	std::string channelId = ""; // 流ID
	std::string camId = ""; // 摄像头ID

	// 推流服务重启检测相关变量 - FPS监控方案
	std::chrono::steady_clock::time_point last_frame_time;
	std::chrono::steady_clock::time_point fps_check_start;
	int recent_frame_count = 0;
	int64_t last_pts = AV_NOPTS_VALUE;
	int same_pts_count = 0;
	int small_packet_count = 0;
	static constexpr float min_fps_threshold = 1.0f;     // 最低FPS阈值
	static constexpr int max_same_pts = 30;              // 最大相同PTS帧数
	static constexpr int max_small_packets = 20;         // 最大小包数量
	static constexpr int min_packet_size = 100;          // 最小包大小阈值

	// 中断回调相关变量
	std::atomic<bool> interrupt_requested{false};        // 中断请求标志
	std::chrono::steady_clock::time_point operation_start_time; // 操作开始时间
	static constexpr int max_operation_timeout_ms = 5000; // 最大操作超时时间(5秒)

	const AVCodec *codec_input_video = nullptr; // 不需要释放，由FFmpeg管理
	const AVCodec *codec_input_audio = nullptr; // 不需要释放，由FFmpeg管理

	AVCodecContextPtr codec_ctx_input_video;
	AVCodecContextPtr codec_ctx_input_audio;

	int video_frame_size = 0;
	int audio_frame_size = 0;
	int video_frame_count = 0;
	int audio_frame_count = 0;

	std::mutex rga_mutex;
	drm_context drm_ctx{};
	int drm_fd = -1; // 存储DRM文件描述符
	rga_context rga_ctx{};
	struct drm_buf drm_buf_for_rga1;
	struct drm_buf drm_buf_for_rga2;
	struct drm_buf drm_buf_for_tmp;
	bool decode(const char *input_stream_url, std::atomic<bool>* running_flag = nullptr, SafeQueue<std::shared_ptr<FrameData>>* frameQueue = nullptr, int maxQueueSize = 1000);

	/* rknn */
	const float nms_threshold = NMS_THRESH;
	const float box_conf_threshold = BOX_THRESH;
	std::mutex rknn_mutex;
	rknn_context rknn_ctx{};
	int rknn_input_channel = 3;
	int rknn_input_width = 0;
	int rknn_input_height = 0;
	rknn_input inputs[1]{};
	rknn_input_output_num io_num{};
	rknn_tensor_attr *output_attrs{};
	int init_rga_drm();
	int init_rknn2();

	// 处理RKNN识别的独立函数
	detect_result_group_t process_frame_with_rknn(int fd, int w, int h, int64_t pts, std::shared_ptr<cv::Mat> &mat4show,
	                                       long long &ts_mark);

	// 中断回调相关函数
	static int interrupt_callback(void *ctx);             // FFmpeg中断回调函数
	void request_interrupt();                             // 请求中断
	void clear_interrupt();                               // 清除中断标志
	bool is_operation_timeout();                          // 检查操作是否超时

	// 判断检测对象是否在ROI区域内
	bool is_object_in_roi(int obj_x1, int obj_y1, int obj_x2, int obj_y2, int roi_x, int roi_y, int roi_w, int roi_h, float scale_x, float
	                      scale_y);

	// 显示检测结果的函数，包括ROI区域和检测到的对象
	void display_detection_results(int fd, std::shared_ptr<cv::Mat> &mat4show, const detect_result_group_t
	                               &detect_result_group, int w, int h, int64_t pts, long long &ts_mark);

	bool decode2(const char *input_stream_url);

	/* opencv */
	std::string window_name;
	int init_window();

	FFmpegStreamChannel(const std::string& _camId = "", const std::string& _channelId = "")
	: camId(_camId), channelId(_channelId)
	{
		// 初始化时间变量
		auto now = std::chrono::steady_clock::now();
		last_frame_time = now;
		fps_check_start = now;
		operation_start_time = now;

		// 初始化中断标志
		interrupt_requested.store(false);

		init_rga_drm();

		init_rknn2();

		//init_window();
	}

	~FFmpegStreamChannel()
	{
		// Free RKNN resources
		if (output_attrs) {
			free(output_attrs);
			output_attrs = nullptr;
		}

		// Destroy RKNN context
		if (rknn_ctx) {
			rknn_destroy(rknn_ctx);
			rknn_ctx = 0;
		}

		// FFmpeg resources are now managed by smart pointers
		// and will be automatically released when the pointers go out of scope

		// Free DRM buffers
		if (drm_buf_for_rga1.drm_buf_ptr) {
			rknn_drm_buf_destroy(&drm_ctx, drm_fd, drm_buf_for_rga1.drm_buf_fd,
				drm_buf_for_rga1.drm_buf_handle, drm_buf_for_rga1.drm_buf_ptr, drm_buf_for_rga1.drm_buf_size);
		}
		if (drm_buf_for_rga2.drm_buf_ptr) {
			rknn_drm_buf_destroy(&drm_ctx, drm_fd, drm_buf_for_rga2.drm_buf_fd,
				drm_buf_for_rga2.drm_buf_handle, drm_buf_for_rga2.drm_buf_ptr, drm_buf_for_rga2.drm_buf_size);
		}
		if (drm_buf_for_tmp.drm_buf_ptr) {
			rknn_drm_buf_destroy(&drm_ctx, drm_fd, drm_buf_for_tmp.drm_buf_fd,
				drm_buf_for_tmp.drm_buf_handle, drm_buf_for_tmp.drm_buf_ptr, drm_buf_for_tmp.drm_buf_size);
		}

		// Destroy RGA context
		rknn_rga_deinit(&rga_ctx);

		// Close DRM
		if (drm_fd > 0) {
			close(drm_fd);
			drm_fd = -1;
		}
	}
};

#endif