#include "ffmpeg.h"

int FFmpegStreamChannel::init_rga_drm()
{
	/* init drm */
	memset(&drm_ctx, 0, sizeof(drm_context));
	drm_fd = rknn_drm_init(&drm_ctx); // 将drm_fd保存到类成员变量中

	/* drm mem1 */
	drm_buf_for_rga1.drm_buf_ptr = rknn_drm_buf_alloc(&drm_ctx, drm_fd, 2560, 1440,
							  4 * 8, // 4 channel x 8bit
							  &drm_buf_for_rga1.drm_buf_fd, &drm_buf_for_rga1.drm_buf_handle, &drm_buf_for_rga1.drm_buf_size);

	/* drm mem2 */
	drm_buf_for_rga2.drm_buf_ptr = rknn_drm_buf_alloc(&drm_ctx, drm_fd, 2560, 1440,
							  4 * 8, // 4 channel x 8bit
							  &drm_buf_for_rga2.drm_buf_fd, &drm_buf_for_rga2.drm_buf_handle, &drm_buf_for_rga2.drm_buf_size);

	/* drm tmp */
	drm_buf_for_tmp.drm_buf_ptr = rknn_drm_buf_alloc(&drm_ctx, drm_fd, 2560, 1440,
							  4 * 8, // 4 channel x 8bit
							  &drm_buf_for_tmp.drm_buf_fd, &drm_buf_for_tmp.drm_buf_handle, &drm_buf_for_tmp.drm_buf_size);
	/* init rga */
	memset(&rga_ctx, 0, sizeof(rga_context));
	rknn_rga_init(&rga_ctx);

	return 0;
}

int FFmpegStreamChannel::init_window()
{
	window_name = "RK3588";
	cv::namedWindow(window_name, cv::WINDOW_AUTOSIZE);
	return 0;
}

int FFmpegStreamChannel::init_rknn2()
{
	printf("Loading mode...\n");
	int model_data_size = 0;
	unsigned char *model_data = load_model(MODEL_PATH, &model_data_size);
	if (model_data == NULL) {
		printf("Failed to load model from %s\n", MODEL_PATH);
		return -1;
	}

	int ret = rknn_init(&rknn_ctx, model_data, model_data_size, 0, NULL);
	// Free model data after initialization
	free(model_data);
	model_data = NULL;

	if (ret < 0) {
		printf("rknn_init error ret=%d\n", ret);
		return -1;
	}

	/*
	ret = rknn_set_core_mask(rknn_ctx, RKNN_NPU_CORE_0_1_2);
	if (ret < 0) {
		printf("rknn_set_core_mask error ret=%d\n", ret);
		return -1;
	}*/

	rknn_sdk_version version;
	ret = rknn_query(rknn_ctx, RKNN_QUERY_SDK_VERSION, &version, sizeof(rknn_sdk_version));
	if (ret < 0) {
		printf("rknn_init error ret=%d\n", ret);
		return -1;
	}
	printf("sdk version: %s driver version: %s\n", version.api_version, version.drv_version);

	ret = rknn_query(rknn_ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
	if (ret < 0) {
		printf("rknn_init error ret=%d\n", ret);
		return -1;
	}
	printf("model input num: %d, output num: %d\n", io_num.n_input, io_num.n_output);

	rknn_tensor_attr input_attrs[io_num.n_input];
	memset(input_attrs, 0, sizeof(input_attrs));
	for (int i = 0; i < io_num.n_input; i++) {
		input_attrs[i].index = i;
		ret = rknn_query(rknn_ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
		if (ret < 0) {
			printf("rknn_init error ret=%d\n", ret);
			return -1;
		}
		dump_tensor_attr(&(input_attrs[i]));
	}

	output_attrs = (rknn_tensor_attr *)malloc(io_num.n_output * sizeof(rknn_tensor_attr));
	memset(output_attrs, 0, sizeof(output_attrs));
	for (int i = 0; i < io_num.n_output; i++) {
		output_attrs[i].index = i;
		ret = rknn_query(rknn_ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
		dump_tensor_attr(&(output_attrs[i]));
	}

	if (input_attrs[0].fmt == RKNN_TENSOR_NCHW) {
		printf("model is NCHW input fmt\n");
		rknn_input_channel = input_attrs[0].dims[1];
		rknn_input_width = input_attrs[0].dims[2];
		rknn_input_height = input_attrs[0].dims[3];
	} else {
		printf("model is NHWC input fmt\n");
		rknn_input_width = input_attrs[0].dims[1];
		rknn_input_height = input_attrs[0].dims[2];
		rknn_input_channel = input_attrs[0].dims[3];
	}
	printf("model input height=%d, width=%d, channel=%d\n", rknn_input_height, rknn_input_width, rknn_input_channel);

	memset(inputs, 0, sizeof(inputs));
	inputs[0].index = 0;
	inputs[0].type = RKNN_TENSOR_UINT8;
	inputs[0].size = rknn_input_width * rknn_input_height * rknn_input_channel;
	inputs[0].fmt = RKNN_TENSOR_NHWC;
	inputs[0].pass_through = 0;

	return 0;
}

// 包含新的decode2实现
#include "ffmpeg_decode2.h"

// 中断回调函数实现
int FFmpegStreamChannel::interrupt_callback(void *ctx) {
    FFmpegStreamChannel* channel = static_cast<FFmpegStreamChannel*>(ctx);
    if (!channel) {
        return 0;
    }

    // 检查是否请求中断
    if (channel->interrupt_requested.load()) {
        printf("🔄 FFmpeg operation interrupted by request\n");
        return 1; // 返回1表示中断
    }

    // 检查操作是否超时
    if (channel->is_operation_timeout()) {
        printf("🔄 FFmpeg operation interrupted by timeout\n");
        channel->interrupt_requested.store(true);
        return 1; // 返回1表示中断
    }

    return 0; // 返回0表示继续
}

// 请求中断
void FFmpegStreamChannel::request_interrupt() {
    interrupt_requested.store(true);
    printf("🔄 Interrupt requested\n");
}

// 清除中断标志
void FFmpegStreamChannel::clear_interrupt() {
    interrupt_requested.store(false);
    operation_start_time = std::chrono::steady_clock::now();
}

// 检查操作是否超时
bool FFmpegStreamChannel::is_operation_timeout() {
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - operation_start_time).count();
    return duration > max_operation_timeout_ms;
}

bool FFmpegStreamChannel::decode(const char *input_stream_url, std::atomic<bool>* running_flag, SafeQueue<std::shared_ptr<FrameData>>* frameQueue, int maxQueueSize)
{
    int ret;
    long long ts_mark = 0;
    // RTSP流重连相关参数
    const int max_reconnect_attempts = 10;
    int reconnect_attempts = 0;
    int reconnect_delay_ms = 1000; // 初始重连延迟1秒
    bool stream_connected = false;
	bool continue_retry = true;

    // 检查参数
    bool use_queue = (running_flag != nullptr && frameQueue != nullptr);

    avformat_network_init();
    av_log_set_level(AV_LOG_INFO);

    // 使用智能指针管理AVPacket和AVFrame的生命周期
    std::shared_ptr<cv::Mat> mat4show = std::make_shared<cv::Mat>(cv::Size(WIDTH_P, HEIGHT_P), CV_8UC3, drm_buf_for_rga2.drm_buf_ptr);
    std::shared_ptr<AVPacket> packet_input_tmp(av_packet_alloc(), [](AVPacket* p) { if(p) av_packet_free(&p); });
    std::shared_ptr<AVFrame> frame_input_tmp(av_frame_alloc(), [](AVFrame* f) { if(f) av_frame_free(&f); });

    // 主循环 - 即使连接断开也会尝试重新连接
    auto last_activity_time = std::chrono::steady_clock::now();
    while (use_queue ? running_flag->load() : true) {
        // 如果未连接或需要重新连接
        if (!stream_connected) {
            // 清理之前的资源 - 使用智能指针的reset方法
            codec_ctx_input_video.reset();
            codec_ctx_input_audio.reset();
            format_context_input.reset();

            // 设置RTSP连接选项
            AVDictionary *opts = nullptr;
            av_dict_set(&opts, "rtsp_transport", "+udp+tcp", 0);
            av_dict_set(&opts, "rtsp_flags", "+prefer_tcp", 0);
            av_dict_set(&opts, "fflags", "nobuffer", 0);             // 降低延迟
            av_dict_set(&opts, "buffer_size", "4194304", 0); // 4MB
            av_dict_set(&opts, "max_delay", "100000", 0);   // 最大延迟500ms

            av_dict_set(&opts, "an", "1", 0);
            av_dict_set(&opts, "sn", "1", 0);
            av_dict_set(&opts, "rw_timeout", "3000000", 0); // 1秒（缩短超时时间）
            av_dict_set(&opts, "stimeout", "3000000", 0); // 1秒超时

            av_dict_set(&opts, "tcp_nodelay", "1", 0);      // 禁用Nagle算法

            //av_dict_set(&opts, "reconnect", "1", 0);      // 启用自动重连
            //av_dict_set(&opts, "reconnect_streamed", "1", 0);
            //av_dict_set(&opts, "reconnect_delay_max", "5", 0); // 最大重连延迟5秒

            printf("Connecting to RTSP stream: %s (attempt %d/%d) continue_retry %d\n",
                   input_stream_url, reconnect_attempts + 1, max_reconnect_attempts, continue_retry);

            // 打开输入流
            AVFormatContext* ctx = avformat_alloc_context();

            // 设置中断回调
            ctx->interrupt_callback.callback = interrupt_callback;
            ctx->interrupt_callback.opaque = this;

            // 清除之前的中断标志并重置操作开始时间
            clear_interrupt();

            ret = avformat_open_input(&ctx, input_stream_url, nullptr, &opts);

            if (ret < 0) {
                char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                printf("avformat_open_input failed: %s (code: %d)\n", errbuf, ret);

                // 释放资源
                if (ctx) avformat_close_input(&ctx);
                if (opts) av_dict_free(&opts); // 释放AVDictionary

                // 增加重连计数并检查是否达到最大尝试次数
                reconnect_attempts++;
            	// 不退出，一直重试
                if (!continue_retry && (reconnect_attempts >= max_reconnect_attempts)) {
                    printf("Max reconnection attempts reached. Giving up.\n");
                    return false;
                }

                // 指数退避重连延迟
                printf("Waiting %d ms before next reconnection attempt...\n", reconnect_delay_ms);
                usleep(reconnect_delay_ms * 1000);
                reconnect_delay_ms = std::min(reconnect_delay_ms * 2, 10000); // 最大10秒
                continue;
            }

            // avformat_open_input成功后，opts可能被修改或释放，需要安全释放
            if (opts) av_dict_free(&opts);

            // 获取流信息
            ret = avformat_find_stream_info(ctx, nullptr);
            if (ret < 0) {
                char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                printf("avformat_find_stream_info failed: %s (code: %d)\n", errbuf, ret);
                avformat_close_input(&ctx);

                // 增加重连计数
                reconnect_attempts++;
                if (!continue_retry && (reconnect_attempts >= max_reconnect_attempts)) {
                    printf("Max reconnection attempts reached. Giving up.\n");
                    return false;
                }

                usleep(reconnect_delay_ms * 1000);
                continue;
            }

            av_dump_format(ctx, 0, input_stream_url, 0);

            // 查找视频流
            video_stream_index_input = av_find_best_stream(ctx, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
            if (video_stream_index_input < 0) {
                printf("Could not find video stream in input\n");
                avformat_close_input(&ctx);

                reconnect_attempts++;
                if (!continue_retry && (reconnect_attempts >= max_reconnect_attempts)) {
                    return false;
                }

                usleep(reconnect_delay_ms * 1000);
                continue;
            }

            AVStream *stream_input = ctx->streams[video_stream_index_input];

            // 使用已设置的channelId
            if (!channelId.empty()) {
                printf("Using channelId: %s\n", channelId.c_str());
            }

            // 查找解码器
            codec_input_video = avcodec_find_decoder_by_name("h264_rkmpp");
            if (codec_input_video == nullptr) {
                // 尝试使用默认解码器
                printf("h264_rkmpp decoder not found, trying default decoder\n");
                codec_input_video = avcodec_find_decoder(stream_input->codecpar->codec_id);
                if (codec_input_video == nullptr) {
                    printf("No suitable decoder found\n");
                    avformat_close_input(&ctx);

                    reconnect_attempts++;
                    if (!continue_retry && (reconnect_attempts >= max_reconnect_attempts)) {
                        return false;
                    }

                    usleep(reconnect_delay_ms * 1000);
                    continue;
                }
            }

            // 分配解码器上下文
            AVCodecContext* codec_ctx = avcodec_alloc_context3(codec_input_video);
            if (codec_ctx == nullptr) {
                printf("avcodec_alloc_context3 failed\n");
                avformat_close_input(&ctx);

                reconnect_attempts++;
                if (!continue_retry && (reconnect_attempts >= max_reconnect_attempts)) {
                    return false;
                }

                usleep(reconnect_delay_ms * 1000);
                continue;
            }

            // 设置解码器参数
            avcodec_parameters_to_context(codec_ctx, stream_input->codecpar);
            codec_ctx->pix_fmt = AV_PIX_FMT_DRM_PRIME;
            codec_ctx->lowres = codec_input_video->max_lowres;
            codec_ctx->flags2 |= AV_CODEC_FLAG2_FAST;
            printf("-->: avcodec_get_hw_config: %s\n", codec_input_video->name);

            // 打开解码器
            AVDictionary *av_dictionary = nullptr;
            av_dict_set(&av_dictionary, "strict", "1", 0);
            ret = avcodec_open2(codec_ctx, codec_input_video, &av_dictionary);
            if (ret < 0) {
                char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                printf("avcodec_open2 failed: %s (code: %d)\n", errbuf, ret);
                avcodec_free_context(&codec_ctx);
                avformat_close_input(&ctx);
                if (av_dictionary) av_dict_free(&av_dictionary); // 释放AVDictionary

                reconnect_attempts++;
                if (!continue_retry && (reconnect_attempts >= max_reconnect_attempts)) {
                    return false;
                }

                usleep(reconnect_delay_ms * 1000);
                continue;
            }

            // avcodec_open2成功后，av_dictionary可能被修改或释放，需要安全释放
            if (av_dictionary) av_dict_free(&av_dictionary);

            // 尝试查找音频流（可选）
            audio_stream_index_input = av_find_best_stream(ctx, AVMEDIA_TYPE_AUDIO, -1, -1, nullptr, 0);
            if (audio_stream_index_input >= 0) {
                AVStream *audio_stream = ctx->streams[audio_stream_index_input];
                codec_input_audio = avcodec_find_decoder(audio_stream->codecpar->codec_id);
                if (codec_input_audio) {
                    AVCodecContext* audio_ctx = avcodec_alloc_context3(codec_input_audio);
                    if (audio_ctx) {
                        avcodec_parameters_to_context(audio_ctx, audio_stream->codecpar);
                        // 创建智能指针并赋值给类成员变量
                        codec_ctx_input_audio = AVCodecContextPtr(audio_ctx, AVCodecContextDeleter());
                    }
                    // 注意：这里我们不强制要求音频解码成功
                }
            }

            // 创建智能指针并赋值给类成员变量
            codec_ctx_input_video = AVCodecContextPtr(codec_ctx, AVCodecContextDeleter());
            format_context_input = AVFormatContextPtr(ctx, AVFormatContextDeleter());

            // 连接成功，重置重连参数和FPS监控变量
            stream_connected = true;
            reconnect_attempts = 0;
            reconnect_delay_ms = 1000;

            // 重置FPS监控变量
            auto now = std::chrono::steady_clock::now();
            fps_check_start = now;
            last_frame_time = now;
            recent_frame_count = 0;
            last_pts = AV_NOPTS_VALUE;
            same_pts_count = 0;
            small_packet_count = 0;

            // 重置中断状态
            clear_interrupt();

            printf("✅ Successfully connected to RTSP stream: %s\n", input_stream_url);
        }

        // 尝试从流中读取数据
        // 重置操作开始时间，为av_read_frame设置超时检测
        clear_interrupt();

        auto read_start_time = std::chrono::steady_clock::now();
        ret = av_read_frame(format_context_input.get(), packet_input_tmp.get());
        auto read_end_time = std::chrono::steady_clock::now();
        auto read_duration = std::chrono::duration_cast<std::chrono::milliseconds>(read_end_time - read_start_time).count();

        // 检查是否被中断
        if (interrupt_requested.load()) {
            printf("🔄 av_read_frame was interrupted, forcing reconnection\n");
            stream_connected = false;
            // 更新活动时间，避免主循环超时检查重复触发重连
            last_activity_time = std::chrono::steady_clock::now();
            av_packet_unref(packet_input_tmp.get());
            continue;
        }

        // 监控av_read_frame的执行时间
        // 注意：如果被中断机制触发，read_duration通常会接近中断超时时间(5秒)
        if (!interrupt_requested.load() && read_duration > 3000) {
            // 只有在非中断情况下才认为是网络性能问题
            printf("🔄 av_read_frame slow response (%ld ms), forcing reconnection\n", read_duration);
            stream_connected = false;
            // 更新活动时间，避免主循环超时检查重复触发重连
            last_activity_time = std::chrono::steady_clock::now();
            av_packet_unref(packet_input_tmp.get());
            continue;
        } else if (!interrupt_requested.load() && read_duration > 1000) {
            // 性能警告（非中断情况）
            printf("⚠️  av_read_frame took %ld ms (possible network congestion)\n", read_duration);
        } else if (interrupt_requested.load()) {
            // 中断情况的特殊日志
            printf("ℹ️  av_read_frame completed after interrupt (%ld ms)\n", read_duration);
        }

        // 处理各种错误情况
        if (ret < 0) {
            char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
            av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
            {
                printf("av_read_frame error: %s (code: %d), trying again...\n", errbuf, ret);

                // 清理资源
                av_packet_unref(packet_input_tmp.get());

                // 如果使用队列模式，清空队列中的残留帧以避免使用无效fd
                if (use_queue && frameQueue) {
                    printf("Clearing frame queue due to stream EOF/error\n");
                    std::shared_ptr<FrameData> old_frame;
                    int cleared_count = 0;
                    while (frameQueue->pop(old_frame, 0)) { // 非阻塞清空
                        cleared_count++;
                    }
                    if (cleared_count > 0) {
                        printf("Cleared %d frames from queue\n", cleared_count);
                    }
                }

                // 重置连接状态，将在下一次循环中重新连接
                stream_connected = false;
                usleep(reconnect_delay_ms * 1000);
                continue;
            }
        }

        // 成功读取到数据包，更新活动时间
        last_activity_time = std::chrono::steady_clock::now();

        // 添加数据包接收日志（每100个包打印一次）
        /*
        static int packet_count = 0;
        packet_count++;
        if (packet_count % 100 == 0) {
            printf("📦 Received packet %d, stream_index: %d, size: %d bytes, activity updated\n",
                   packet_count, packet_input_tmp.get()->stream_index, packet_input_tmp.get()->size);
        }
        */

        // 数据包大小异常检测
        if (packet_input_tmp.get()->stream_index == video_stream_index_input) {
            if (packet_input_tmp.get()->size < min_packet_size) {
                small_packet_count++;
                printf("⚠️  Small packet detected: size=%d bytes, count=%d/%d (possible stream degradation)\n",
                       packet_input_tmp.get()->size, small_packet_count, max_small_packets);

                if (small_packet_count >= max_small_packets) {
                    printf("🔄 STREAM RESTART DETECTED: Too many small packets (%d) - forcing reconnection\n", small_packet_count);
                    stream_connected = false;
                    small_packet_count = 0;
                    // 更新活动时间，避免主循环超时检查重复触发重连
                    last_activity_time = std::chrono::steady_clock::now();
                    av_packet_unref(packet_input_tmp.get());
                    continue;
                }
            } else {
                // 正常大小的包，重置小包计数
                if (small_packet_count > 0) {
                    printf("✅ Normal packet size resumed: %d bytes\n", packet_input_tmp.get()->size);
                }
                small_packet_count = 0;
            }
        } else {
           printf("-->: packet check %d\n", __LINE__);
        }

        AVStream *stream_input = format_context_input.get()->streams[packet_input_tmp.get()->stream_index];

        /* video */
            if (packet_input_tmp.get()->stream_index == video_stream_index_input) {
            video_frame_size += packet_input_tmp.get()->size;
            video_frame_count++;

            ret = avcodec_send_packet(codec_ctx_input_video.get(), packet_input_tmp.get());
            if (ret == AVERROR(EAGAIN)) {
                char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                printf("avcodec_send_packet failed: %s (code: %d)\n", errbuf, ret);
                // 缓冲区满，继续处理
                usleep(10000);

            }
            else if (ret < 0) {
                char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                printf("avcodec_send_packet failed other: %s (code: %d)\n", errbuf, ret);
                // 在外循环结束前释放packet
                av_packet_unref(packet_input_tmp.get());
                continue; // 跳过这个包，处理下一个
            }

            // 添加帧处理计数和时间控制
            int frames_processed = 0;
            long long process_start_time = current_timestamp();

            while ((ret >= 0 || ret == AVERROR(EAGAIN)) && frames_processed < 10) { // 限制每次循环最多处理10帧
                auto decode_start_time = std::chrono::steady_clock::now();
                ret = avcodec_receive_frame(codec_ctx_input_video.get(), frame_input_tmp.get());
                auto decode_end_time = std::chrono::steady_clock::now();
                auto decode_duration = std::chrono::duration_cast<std::chrono::milliseconds>(decode_end_time - decode_start_time).count();

                // 监控解码函数的执行时间
                if (decode_duration > 500) { // 超过500ms
                    printf("⚠️  avcodec_receive_frame took %ld ms (possible decoder issue)\n", decode_duration);
                }
                if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
                    char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                    av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                    break;
                } else if (ret < 0) {
                    char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
                    av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                    printf("avcodec_receive_frame failed: %s (code: %d)\n", errbuf, ret);
                    // 内循环中遇到错误，跳出内循环
                    break;
                }

                frames_processed++;

                // 更新活动时间
                last_activity_time = std::chrono::steady_clock::now();

                // 添加解码成功日志（每30帧打印一次，避免日志过多）
                /*
                static int decode_success_count = 0;
                decode_success_count++;
                if (decode_success_count % 30 == 0) {
                    printf("✅ Successfully decoded frame %d, PTS: %ld, activity updated\n", decode_success_count, frame_input_tmp.get()->pts);
                }
                */

                // FPS监控和PTS检测
                auto frame_time = std::chrono::steady_clock::now();
                recent_frame_count++;

                // 检测PTS重复（帧停滞）
                if (frame_input_tmp.get()->pts == last_pts && last_pts != AV_NOPTS_VALUE) {
                    same_pts_count++;
                    printf("⚠️  Same PTS detected: pts=%ld, count=%d/%d (possible stream freeze)\n",
                           frame_input_tmp.get()->pts, same_pts_count, max_same_pts);

                    if (same_pts_count >= max_same_pts) {
                        printf("🔄 STREAM RESTART DETECTED: %d frames with same PTS - forcing reconnection\n", same_pts_count);
                        stream_connected = false;
                        same_pts_count = 0;
                        // 更新活动时间，避免主循环超时检查重复触发重连
                        last_activity_time = std::chrono::steady_clock::now();
                        av_frame_unref(frame_input_tmp.get());
                        av_packet_unref(packet_input_tmp.get());
                        break; // 跳出内循环，触发重连
                    }
                } else {
                    // PTS正常变化，重置计数
                    if (same_pts_count > 0) {
                        printf("✅ PTS resumed normal progression: %ld -> %ld\n", last_pts, frame_input_tmp.get()->pts);
                    }
                    same_pts_count = 0;
                    last_pts = frame_input_tmp.get()->pts;
                }

                // 每5秒检查一次FPS
                auto time_diff = std::chrono::duration_cast<std::chrono::seconds>(frame_time - fps_check_start).count();
                if (time_diff >= 5) {
                    float current_fps = recent_frame_count / (float)time_diff;
                    printf("📊 FPS Monitor: %.2f fps (frames: %d, time: %ld seconds)\n", current_fps, recent_frame_count, time_diff);

                    if (current_fps < min_fps_threshold) {
                        printf("🔄 STREAM RESTART DETECTED: FPS too low (%.2f < %.2f) - forcing reconnection\n",
                               current_fps, min_fps_threshold);
                        stream_connected = false;
                        // 更新活动时间，避免主循环超时检查重复触发重连
                        last_activity_time = std::chrono::steady_clock::now();
                        av_frame_unref(frame_input_tmp.get());
                        av_packet_unref(packet_input_tmp.get());
                        break; // 跳出内循环，触发重连
                    }

                    // 重置FPS计数
                    recent_frame_count = 0;
                    fps_check_start = frame_time;
                }

                // 提取DRM帧信息
                if (!frame_input_tmp.get()->data[0]) {
                    printf("Warning: Invalid frame data\n");
                    av_frame_unref(frame_input_tmp.get());
                    continue;
                }

                auto av_drm_frame = reinterpret_cast<const AVDRMFrameDescriptor *>(frame_input_tmp.get()->data[0]);
                if (!av_drm_frame) {
                    printf("Warning: Invalid DRM frame descriptor\n");
                    av_frame_unref(frame_input_tmp.get());
                    continue;
                }

                auto layer = &reinterpret_cast<AVDRMFrameDescriptor *>(frame_input_tmp.get()->data[0])->layers[0];
                int fd = av_drm_frame->objects[0].fd;
                int w = layer->planes[0].pitch;
                int h = layer->planes[1].offset / w;

                // 验证宽高是否合理
                if (w <= 0 || h <= 0 || w > 8192 || h > 8192) {
                    printf("Warning: Invalid frame dimensions: %dx%d\n", w, h);
                    av_frame_unref(frame_input_tmp.get());
                    continue;
                }

#if 0
                // 定义 ROI 区域
                int roi_x = 401;
                int roi_y = 433;
                int roi_x2 = 1450;
                int roi_y2 = 597;
                int roi_w = roi_x2 - roi_x;
                int roi_h = roi_y2 - roi_y;

                // 在 RGA 缩放前保存原始帧和 ROI 区域
                static int frame_count = 0;
                if (frame_count % 30 == 0) {  // 每 30 帧保存一次
                    // 创建一个临时缓冲区用于保存原始帧
                    uint8_t* temp_buffer = new uint8_t[w * h * 3];  // RGB 格式需要 3 倍空间

                    // 使用 RGA 将 YUV 转换为 RGB，但不缩放
                    rknn_img_resize_phy_to_phy(&rga_ctx,
                                              fd, w, h, RK_FORMAT_YCbCr_420_SP,
                                              drm_buf_for_tmp.drm_buf_fd, w, h, RK_FORMAT_BGR_888);

                    // 复制数据到临时缓冲区
                    memcpy(temp_buffer, drm_buf_for_tmp.drm_buf_ptr, w * h * 3);

                    // 创建 OpenCV Mat 引用临时缓冲区
                    cv::Mat original_frame(h, w, CV_8UC3, temp_buffer);

                    // 在原始帧上绘制 ROI 矩形
                    cv::rectangle(original_frame,
                                 cv::Point(roi_x, roi_y),
                                 cv::Point(roi_x + roi_w, roi_y + roi_h),
                                 cv::Scalar(0, 255, 255),  // 黄色
                                 2);  // 线宽

                    // 保存带有 ROI 标记的原始帧
                    std::string frame_filename = "./output/original_frame_" + std::to_string(frame_count) + ".jpg";
                    cv::imwrite(frame_filename, original_frame);
                    printf("Saved original frame with ROI to %s\n", frame_filename.c_str());

                    // 提取并保存 ROI 区域
                    // 确保 ROI 不超出图像边界
                    roi_w = std::min(roi_w, w - roi_x);
                    roi_h = std::min(roi_h, h - roi_y);

                    cv::Mat roi_region = original_frame(cv::Rect(roi_x, roi_y, roi_w, roi_h));
                    std::string roi_filename = "./output/original_roi_" + std::to_string(frame_count) + ".jpg";
                    cv::imwrite(roi_filename, roi_region);
                    printf("Saved original ROI to %s\n", roi_filename.c_str());

                    // 释放临时缓冲区
                    delete[] temp_buffer;
                }
                frame_count++;
#endif

                // 如果使用队列模式，将帧放入队列
                if (use_queue) {
                    // 创建帧数据并放入队列
                    auto frame_data = std::make_shared<FrameData>(fd, w, h, frame_input_tmp.get()->pts);
                    // 检查队列大小，如果队列已满则丢弃最旧的帧
                    if (frameQueue->size() >= maxQueueSize) {
                        std::shared_ptr<FrameData> old_frame;
                        frameQueue->pop(old_frame, 0); // 非阻塞弹出
                    }
                    // 将新帧放入队列
                    frameQueue->push(frame_data);
                } else {
                    // 直接处理模式
                    // 添加帧率控制，只处理部分帧以减轻处理负担
                    static int frame_counter = 0;
                    if (++frame_counter % 2 != 0) { // 每2帧处理1帧，可以根据实际情况调整
                        // 调用独立的RKNN识别函数
                        detect_result_group_t detect_result_group = process_frame_with_rknn(fd, w, h, frame_input_tmp.get()->pts, mat4show, ts_mark);
                    }
                }

                // 如果不使用队列模式，显示处理结果
                if (!use_queue) {
                    // 可以在这里添加额外的处理逻辑，例如绘制边界框
                    /*
                    for (int i = 0; i < detect_result_group.count; i++) {
                        detect_result_t *det_result = &(detect_result_group.results[i]);
                        int x1 = det_result->box.left;
                        int y1 = det_result->box.top;
                        int x2 = det_result->box.right;
                        int y2 = det_result->box.bottom;

                        // 绘制边界框
                        rectangle(*mat4show.get(), cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(255, 0, 0, 255), 2);

                        // 绘制文本标签
                        char text[256];
                        sprintf(text, "%s %.1f%%", det_result->name, det_result->prop * 100);
                        putText(*mat4show.get(), text, cv::Point(x1, y1 + 12), cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0));
                    }
                    printf("DRAW BOX OK---->[%fms]\n", static_cast<double>(current_timestamp() - ts_mark) / 1000);
                    */
                    // /* OpenGL */
                    // if (image_texture != 0) {
                    //     glDeleteTextures(1, &image_texture);
                    //     image_texture = 0;
                    // }
                    // bind_cv_mat_to_gl_texture(*mat4show.get(), image_texture);
                    //
                    // /* Opencv */
                    //cv::imshow(window_name, *mat4show.get());
                    //cv::waitKey(1);
                }

                //printf("SHOW OK---->[%fms]\n", ((double)(current_timestamp() - ts_mark)) / 1000);
                // 在内循环结束时释放frame
                av_frame_unref(frame_input_tmp.get());
                // 检查处理时间，如果处理时间过长则提前退出循环
                long long current_time = current_timestamp();
                if (current_time - process_start_time > 100000) { // 100ms
                    printf("Frame processing time limit reached, processed %d frames in %f ms\n",
                           frames_processed, (current_time - process_start_time) / 1000.0);
                    break;
                }
            }

            // 如果处理了帧，给一个短暂的休眠让系统有时间处理网络事件
            if (frames_processed > 0) {
                usleep(1000); // 1ms的休眠
            }
        }

        /* audio */
        if (packet_input_tmp.get()->stream_index == audio_stream_index_input) {
            audio_frame_size += packet_input_tmp.get()->size;
            audio_frame_count++;

            // 音频包也表示有活动，更新活动时间
            last_activity_time = std::chrono::steady_clock::now();

            // 如果音频解码器存在，可以处理音频帧
            if (codec_ctx_input_audio) {
                // 处理音频帧的代码可以在这里添加
            }
        }

        // 检查主循环是否长时间没有活动
        auto current_loop_time = std::chrono::steady_clock::now();
        auto inactivity_duration = std::chrono::duration_cast<std::chrono::seconds>(current_loop_time - last_activity_time).count();
        if (inactivity_duration > 10) { // 10秒没有活动
            printf("🔄 MAIN LOOP TIMEOUT: No activity for %ld seconds - forcing reconnection\n", inactivity_duration);
            stream_connected = false;
            last_activity_time = current_loop_time;
        }

        // 只在外循环结束时释放packet
        av_packet_unref(packet_input_tmp.get());
    }
    // 智能指针会自动释放AVPacket、AVFrame、AVCodecContext和AVFormatContext
    // 不需要手动释放这些资源
    return true;
};
