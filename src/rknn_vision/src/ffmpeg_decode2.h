// 判断检测对象是否在ROI区域内
bool FFmpegStreamChannel::is_object_in_roi(int obj_x1, int obj_y1, int obj_x2, int obj_y2, int roi_x, int roi_y, int
                                                  roi_w, int roi_h, float scale_x, float scale_y) {

    int display_roi_x = roi_x * scale_x;
    int display_roi_y = roi_y * scale_y;
    int display_roi_w = roi_w * scale_x;
    int display_roi_h = roi_h * scale_y;
    // 计算检测框的中心点
    int center_x = (obj_x1 + obj_x2) / 2;
    int center_y = (obj_y1 + obj_y2) / 2;

    // 判断中心点是否在 ROI 内
    return (center_x >= display_roi_x && center_x <= display_roi_x + display_roi_w &&
            center_y >= display_roi_y && center_y <= display_roi_y + display_roi_h);
}

// 显示检测结果的函数，包括ROI区域和检测到的对象
void FFmpegStreamChannel::display_detection_results(int fd, std::shared_ptr<cv::Mat>& mat4show, const
                                                           detect_result_group_t& detect_result_group, int w, int h, int64_t pts, long long &ts_mark) {
    // 检查fd有效性
    if (fd <= 0) {
        printf("display_detection_results: fd is invalid, fd: %d\n", fd);
        return;
    }

    // 验证fd是否仍然有效
    int fd_flags = fcntl(fd, F_GETFD);
    if (fd_flags == -1) {
        printf("display_detection_results: fd %d is no longer valid (errno: %d, %s)\n", fd, errno, strerror(errno));
        return;
    }

    /* YUV 4 Show */
    int rga_ret = rknn_img_resize_phy_to_phy(&rga_ctx,
                       fd, w, h, RK_FORMAT_YCbCr_420_SP,
                       drm_buf_for_rga2.drm_buf_fd, WIDTH_P, HEIGHT_P, RK_FORMAT_BGR_888);

    if (rga_ret < 0) {
        printf("display_detection_results: RGA resize failed: ret=%d, fd=%d, size=%dx%d\n", rga_ret, fd, w, h);
        return;
    }
    return;
    /* 定义 ROI 区域 */
    int roi_x = 613;
    int roi_y = 329;
    int roi_x2 = 1658;
    int roi_y2 = 518;
    int roi_w = roi_x2 - roi_x;
    int roi_h = roi_y2 - roi_y;

    /* 计算 ROI 在显示图像中的坐标 */
    float scale_display_x = (float)WIDTH_P / w;
    float scale_display_y = (float)HEIGHT_P / h;

    int display_roi_x = roi_x * scale_display_x;
    int display_roi_y = roi_y * scale_display_y;
    int display_roi_w = roi_w * scale_display_x;
    int display_roi_h = roi_h * scale_display_y;

    /* 绘制黄色矩形框表示 ROI 区域 */
    cv::Scalar yellow_color(0, 255, 255);  // BGR 格式，黄色 = (0, 255, 255)
    int thickness = 2;  // 线条粗细
    cv::rectangle(*mat4show.get(),
                  cv::Point(display_roi_x, display_roi_y),
                  cv::Point(display_roi_x + display_roi_w, display_roi_y + display_roi_h),
                  yellow_color,
                  thickness);

    /* 添加 ROI 标签和摄像头ID */
    char roi_label[256];
    sprintf(roi_label, "ROI (Cam %s)", camId.c_str());
    int font_face = cv::FONT_HERSHEY_SIMPLEX;
    double font_scale = 0.5;
    int font_thickness = 1;
    cv::putText(*mat4show.get(),
                roi_label,
                cv::Point(display_roi_x, display_roi_y - 5),
                font_face,
                font_scale,
                yellow_color,
                font_thickness);

    /* Draw Objects */
    char text[256];
    char file_name[256];
    for (int i = 0; i < detect_result_group.count; i++) {
        const __detect_result_t *det_result = &(detect_result_group.results[i]);
        sprintf(text, "%s %.1f%% (Cam %s)", det_result->name, det_result->prop * 100, camId.c_str());
        sprintf(file_name, "%ld_%s_%.1f_cam%s", pts, det_result->name, det_result->prop * 100, camId.c_str());

        // printf("---->%s @ (%d %d %d %d) %f\n", det_result->name, det_result->box.left, det_result->box.top,
        //        det_result->box.right, det_result->box.bottom, det_result->prop);

        int x1 = det_result->box.left;
        int y1 = det_result->box.top;
        int x2 = det_result->box.right;
        int y2 = det_result->box.bottom;

        // 使用新函数判断对象是否在ROI内
        bool in_roi = is_object_in_roi(x1, y1, x2, y2, roi_x, roi_y,
                                       roi_w, roi_h, scale_display_x, scale_display_x);

        // 根据是否在 ROI 内选择不同的颜色
        cv::Scalar box_color = in_roi ? cv::Scalar(0, 255, 0) : cv::Scalar(0, 0, 255);  // 在 ROI 内为绿色，否则为红色
        // 绘制边界框
        rectangle(*mat4show.get(), cv::Point(x1, y1), cv::Point(x2, y2), box_color, 2);
        // 绘制文本标签
        putText(*mat4show.get(), text, cv::Point(x1, y1 + 12), cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0));
    }
}

// 处理RKNN识别的独立函数
detect_result_group_t FFmpegStreamChannel::process_frame_with_rknn(int fd, int w, int h, int64_t pts,
                                                                          std::shared_ptr<cv::Mat> &mat4show,
                                                                          long long &ts_mark)
{
    std::lock_guard<std::mutex> lock(rknn_mutex);
    detect_result_group_t detect_result_group;
#if 1
    if (fd <=  0) {
        printf("fd is invalid, fd: %d\n", fd);
        return detect_result_group;
    }

    // 额外的fd有效性检查 - 尝试fcntl来验证fd是否仍然有效
    int fd_flags = fcntl(fd, F_GETFD);
    if (fd_flags == -1) {
        printf("fd %d is no longer valid (errno: %d, %s)\n", fd, errno, strerror(errno));
        return detect_result_group;
    }

    /* YUV 4 RKNN2 Compute */
    int rga_ret = rknn_img_resize_phy_to_phy(&rga_ctx,
                       fd, w, h, RK_FORMAT_YCbCr_420_SP,
                       drm_buf_for_rga1.drm_buf_fd, rknn_input_width, rknn_input_height, RK_FORMAT_RGB_888);

    if (rga_ret < 0) {
        printf("RGA resize failed: ret=%d, fd=%d, size=%dx%d\n", rga_ret, fd, w, h);
        return detect_result_group;
    }

#else
    /* YUV 4 RKNN2 Compute */
    rknn_img_crop_phy_to_phy(&rga_ctx,
                         fd, w, h, RK_FORMAT_YCbCr_420_SP,
                         drm_buf_for_rga1.drm_buf_fd, rknn_input_width, rknn_input_height,
                         RK_FORMAT_RGB_888, roi_x, roi_y, roi_w, roi_h);
    rknn_img_crop_phy_to_phy(&rga_ctx,
                         fd, w, h, RK_FORMAT_YCbCr_420_SP,
                         drm_buf_for_rga2.drm_buf_fd, WIDTH_P, HEIGHT_P,
                         RK_FORMAT_RGB_888, roi_x, roi_y, roi_w, roi_h);
#endif
    /* rknn2 compute */
    inputs[0].buf = drm_buf_for_rga1.drm_buf_ptr;
    rknn_inputs_set(rknn_ctx, io_num.n_input, inputs);

    rknn_output outputs[io_num.n_output];
    memset(outputs, 0, sizeof(outputs));
    for (int i = 0; i < io_num.n_output; i++) {
        outputs[i].want_float = 0;
    }


    int ret = rknn_run(rknn_ctx, nullptr);
    if (ret < 0) {
        printf("rknn_run error ret=%d\n", ret);
        rknn_outputs_release(rknn_ctx, io_num.n_output, outputs);
        return detect_result_group;
    }
    ret = rknn_outputs_get(rknn_ctx, io_num.n_output, outputs, nullptr);
    if (ret < 0) {
        printf("rknn_outputs_get fail! ret=%d\n", ret);
        rknn_outputs_release(rknn_ctx, io_num.n_output, outputs);
        return detect_result_group;
    }

    /* post process */
    float scale_w = (float)rknn_input_width / WIDTH_P;
    float scale_h = (float)rknn_input_height / HEIGHT_P;

    std::vector<float> out_scales;
    std::vector<int32_t> out_zps;
    for (int i = 0; i < io_num.n_output; ++i) {
        out_scales.push_back(output_attrs[i].scale);
        out_zps.push_back(output_attrs[i].zp);
    }

    post_process((int8_t *)outputs[0].buf, (int8_t *)outputs[1].buf, (int8_t *)outputs[2].buf,
                 rknn_input_height, rknn_input_width, box_conf_threshold, nms_threshold,
                 scale_w, scale_h, out_zps, out_scales, &detect_result_group);

    /* Free Outputs - 重要：释放输出缓冲区，避免内存泄漏 */
    rknn_outputs_release(rknn_ctx, io_num.n_output, outputs);
    return detect_result_group;
}