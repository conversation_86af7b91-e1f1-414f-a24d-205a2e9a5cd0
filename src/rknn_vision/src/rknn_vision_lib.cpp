#include "rknn_vision_lib.h"
#include "stream_manager.h"
#include <memory>
#include <mutex>
#include <csignal>
#include <thread>
#include <chrono>
#include <iostream>
#include <unordered_map>

// 确保使用 fmt 的头文件模式
//#define FMT_HEADER_ONLY
#include <spdlog/spdlog.h>
#include "CRedisClient.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/sinks/syslog_sink.h"
#include "utils/version_info.hpp"
#include "json.hpp"



// Global variables
static std::unique_ptr<StreamManager> g_stream_manager;
static volatile sig_atomic_t g_flag_run = false;
static std::mutex g_mutex;
static bool g_initialized = false;
static std::unordered_map<std::string, std::string> g_stream_id_map; // Maps external IDs to internal URLs
static std::unordered_map<std::string, std::string> g_camera_to_stream_map; // Maps camera_id to stream_id for efficient lookup
static std::shared_ptr<CRedisClient> g_redis_client; // Redis client for storing detection results
static int g_redis_db_index = 2; // Redis数据库索引，默认为2

// 检测结果回调函数，将结果写入Redis
static void detection_callback(const std::string& json_result, const std::string& camera_id, const std::string& stream_id) {
    if (json_result.empty()) {
        return;
    }

    try {
        // 解析JSON字符串
        nlohmann::json json_obj;
        try {
            json_obj = nlohmann::json::parse(json_result);
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to parse JSON result: {}", e.what());
            return;
        }

        // 获取相应流的FPS
        float fps = 0.0f;
        if (g_stream_manager) {
            fps = rknn_vision_get_stream_fps(stream_id.c_str());
        }

        // 将FPS添加到JSON对象中
        json_obj["fps"] = fps;

        // 将JSON对象转换回字符串
        std::string updated_json = json_obj.dump();

        // 使用格式 recognition["stream.id"] 组装Redis key
        std::string key = "recognition[" + camera_id + "]";

        // 将检测结果写入Redis，使用配置的数据库索引
        if (g_redis_client) {
            g_redis_client->selectDB(g_redis_db_index);
            g_redis_client->xadd_withtrim(key, "*", "data", updated_json);
        }

        SPDLOG_DEBUG("Detection result with FPS for camera {} written to Redis key {}", camera_id, key);
    } catch (const std::exception& e) {
        SPDLOG_ERROR("Failed to write detection result to Redis: {}", e.what());
    }
}

// Signal handler function
static void signal_handler(int signo) {
    std::cout << "\nReceived signal " << signo << ". Initiating graceful shutdown..." << std::endl;
    g_flag_run = false;
}

// Setup signal handling
static void setup_signals() {
    struct sigaction sa{};
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;

    // Register signals
    const int signals[] = {
        SIGINT, SIGTERM, SIGQUIT,
        SIGABRT, SIGFPE, SIGILL, SIGBUS
    };

    for (int sig : signals) {
        if (sigaction(sig, &sa, nullptr) == -1) {
            throw std::runtime_error("Failed to set up signal handler for signal " + std::to_string(sig));
        }
    }

    // Ignore SIGPIPE
    signal(SIGPIPE, SIG_IGN);
}


static void setup_logger() {
    try {
        // Setup logging
        spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%s:%#] - %v");
        // 1. 创建控制台 Sink (带颜色，线程安全)
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        // 可以为控制台 Sink 单独设置日志级别，例如，控制台显示所有 debug 信息
        console_sink->set_level(spdlog::level::debug);
        // console_sink->set_pattern("[%H:%M:%S.%e %L] %v"); // 为控制台设置不同格式 (可选)

        // 2. 创建 Syslog Sink (线程安全)
        std::string syslog_ident = "RKNN_Vision_Library"; // Syslog 中的标识符
        // 参数: ident, syslog options (e.g., LOG_PID), facility, enable formatting
        auto syslog_sink = std::make_shared<spdlog::sinks::syslog_sink_mt>(syslog_ident, LOG_PID, LOG_USER, true);
        // 可以为 Syslog Sink 单独设置日志级别，例如，只记录 info 及以上级别
        syslog_sink->set_level(spdlog::level::info);

        // 3. 将 Sinks 放入一个 vector 中
        std::vector<spdlog::sink_ptr> sinks;
        sinks.push_back(console_sink);
        sinks.push_back(syslog_sink);

        // 4. 创建一个包含多个 Sink 的 Logger
        // 参数: logger 名称, sink 列表的开始迭代器, sink 列表的结束迭代器
        auto combined_logger = std::make_shared<spdlog::logger>("combined_logger", sinks.begin(), sinks.end());

        // 5. 设置 Logger 的全局日志级别
        // 注意：Logger 的级别是主过滤器。只有级别 >= 这个设置的消息才会被尝试发送到 Sinks。
        // Sinks 自己的级别是第二层过滤。
        // 所以这里应该设置为所有 Sink 中最低的那个级别，或者更低。
        combined_logger->set_level(spdlog::level::debug); // 允许 debug 及以上级别通过

        // 6. (可选) 设置 Logger 的刷新策略
        // 例如，任何 error 或更高级别的日志都立即刷新
        combined_logger->flush_on(spdlog::level::err);

        // 7. 将新创建的 Logger 设置为全局默认 Logger
        spdlog::set_default_logger(combined_logger);

        // 现在，使用 spdlog::info() 等函数会同时输出到控制台和 syslog
        SPDLOG_INFO("spdlog configured for both console (level >= debug) and syslog (level >= info, ident: {})",
                    syslog_ident);
        SPDLOG_DEBUG("This debug message will go to console, but not syslog.");
        SPDLOG_WARN("This warning message will go to both console and syslog.");
    } catch (const spdlog::spdlog_ex &ex) {
        // 如果日志设置失败，输出到标准错误
        std::cerr << "Log initialization failed: " << ex.what() << std::endl;
        // 最好恢复到简单的控制台日志记录，或者处理错误
        spdlog::set_default_logger(spdlog::stderr_color_mt("console_fallback"));
        SPDLOG_ERROR("Failed to set up combined logging, falling back to stderr.");
        // return -5; // Or throw std::runtime_error("Log init failed");
    }
}

// C API Implementation
extern "C" {

int rknn_vision_init(const RknnStreamInfo* streams, int stream_count, const RknnRedisConfig* redis_config, bool handle_signals) {
    std::lock_guard<std::mutex> lock(g_mutex);
    setup_logger();

    if (g_initialized) {
        SPDLOG_WARN("RKNN Vision library already initialized");
        return -1;
    }
    SPDLOG_INFO("RKNN Vision library version: {}", version::getVersionInfo());

    try {
        // Set environment variables
        setenv("OPENCV_FFMPEG_CAPTURE_OPTIONS", "video_codec;h264_rkmpp", 1);
        setenv("OPENCV_LOG_LEVEL", "ERROR", 1);
        setenv("OPENCV_FFMPEG_LOG_LEVEL", "48", 1);

        SPDLOG_INFO("Initializing RKNN Vision library");

        // Setup signal handling if requested
        if (handle_signals) {
            SPDLOG_INFO("Setting up signal handlers in library");
            setup_signals();
        } else {
            SPDLOG_INFO("Signal handling disabled, application should handle signals");
        }

        // Initialize Redis client if configuration is provided
        if (redis_config != nullptr) {
            try {
                SPDLOG_INFO("Initializing Redis client: {}:{}, DB: {}", redis_config->host, redis_config->port, redis_config->db_index);
                g_redis_client = std::make_shared<CRedisClient>(redis_config->host, redis_config->port, redis_config->password);
                g_redis_db_index = redis_config->db_index; // 保存数据库索引
                SPDLOG_INFO("Redis client initialized successfully");
            } catch (const std::exception& e) {
                SPDLOG_ERROR("Failed to initialize Redis client: {}", e.what());
                // Continue without Redis - we'll just log the error but not fail initialization
            }
        } else {
            SPDLOG_WARN("No Redis configuration provided, detection results will not be stored");
        }

        // Create a stream manager
        g_stream_manager = std::make_unique<StreamManager>();

        // Add streams
        std::vector<std::string> failed_streams;
        for (int i = 0; i < stream_count; i++) {
            const RknnStreamInfo& stream = streams[i];

            // Store mapping for later use
            g_stream_id_map[stream.id] = stream.url;

            // Add stream to manager, passing the showGui parameter
            if (!g_stream_manager->addStream(stream.url, stream.id,
                                           stream.roi.x1, stream.roi.y1,
                                           stream.roi.x2, stream.roi.y2,
                                           stream.camId, stream.targetClassNames,
                                           stream.showGui)) {
                SPDLOG_ERROR("Failed to add stream: {}", stream.url);
                failed_streams.push_back(stream.url);
                continue;
            }

            // Store camera_id to stream_id mapping for efficient lookup
            g_camera_to_stream_map[stream.camId] = stream.id;
            SPDLOG_DEBUG("Added mapping: camera_id {} -> stream_id {}, showGui: {}",
                         stream.camId, stream.id, stream.showGui ? "true" : "false");

            // Get the stream info and set the detection callback
            auto& stream_map = g_stream_manager->getStreams();
            auto it = stream_map.find(stream.url);
            if (it != stream_map.end() && it->second.decoder) {
                SPDLOG_INFO("Setting detection callback for stream: {}", stream.url);
                it->second.decoder->setDetectionCallback(detection_callback);
            }
        }

        // Check if all streams failed
        if (failed_streams.size() == static_cast<size_t>(stream_count)) {
            SPDLOG_ERROR("All streams failed to initialize");
            g_stream_manager.reset();
            return -2;
        }

        // If some streams failed, log a warning
        if (!failed_streams.empty()) {
            SPDLOG_WARN("Failed to add {} stream(s)", failed_streams.size());
            for (const auto& url : failed_streams) {
                SPDLOG_WARN("  - {}", url);
            }
        }

        g_initialized = true;
        SPDLOG_INFO("RKNN Vision library initialized successfully");
        return 0;

    } catch (const std::exception& e) {
        SPDLOG_ERROR("Error initializing RKNN Vision library: {}", e.what());
        g_stream_manager.reset();
        return -3;
    } catch (...) {
        SPDLOG_ERROR("Unknown error initializing RKNN Vision library");
        g_stream_manager.reset();
        return -4;
    }
}

int rknn_vision_start() {
    std::lock_guard<std::mutex> lock(g_mutex);

    if (!g_initialized) {
        SPDLOG_ERROR("RKNN Vision library not initialized");
        return -1;
    }

    if (g_flag_run) {
        SPDLOG_WARN("RKNN Vision library already running");
        return -2;
    }

    try {
        SPDLOG_INFO("Starting RKNN Vision library");

        // Start all streams
        if (!g_stream_manager->startAllStreams()) {
            SPDLOG_WARN("Some streams failed to start");
        }

        g_flag_run = true;
        SPDLOG_INFO("RKNN Vision library started successfully");
        return 0;

    } catch (const std::exception& e) {
        SPDLOG_ERROR("Error starting RKNN Vision library: {}", e.what());
        return -3;
    } catch (...) {
        SPDLOG_ERROR("Unknown error starting RKNN Vision library");
        return -4;
    }
}

int rknn_vision_stop() {
    std::lock_guard<std::mutex> lock(g_mutex);

    if (!g_initialized) {
        SPDLOG_ERROR("RKNN Vision library not initialized");
        return -1;
    }

    if (!g_flag_run) {
        SPDLOG_WARN("RKNN Vision library not running");
        return -2;
    }

    try {
        SPDLOG_INFO("Stopping RKNN Vision library");
        g_flag_run = false;

        // Stop all streams
        g_stream_manager->stopAllStreams();

        // Clean up Redis client
        if (g_redis_client) {
            SPDLOG_INFO("Cleaning up Redis client");
            g_redis_client.reset();
        }

        // Clear camera to stream mapping
        g_camera_to_stream_map.clear();

        SPDLOG_INFO("RKNN Vision library stopped successfully");
        return 0;

    } catch (const std::exception& e) {
        SPDLOG_ERROR("Error stopping RKNN Vision library: {}", e.what());
        return -3;
    } catch (...) {
        SPDLOG_ERROR("Unknown error stopping RKNN Vision library");
        return -4;
    }
}

float rknn_vision_get_stream_fps(const char* stream_id) {
    std::lock_guard<std::mutex> lock(g_mutex);

    if (!g_initialized || !g_stream_manager) {
        return 0.0f;
    }

    // Find URL for the given ID
    auto it = g_stream_id_map.find(stream_id);
    if (it == g_stream_id_map.end()) {
        return 0.0f;
    }

    return g_stream_manager->getStreamFps(it->second);
}

int rknn_vision_is_stream_active(const char* stream_id) {
    std::lock_guard<std::mutex> lock(g_mutex);

    if (!g_initialized || !g_stream_manager) {
        return 0;
    }

    // Find URL for the given ID
    auto it = g_stream_id_map.find(stream_id);
    if (it == g_stream_id_map.end()) {
        return 0;
    }

    return g_stream_manager->isStreamActive(it->second) ? 1 : 0;
}

/**
 * @brief Set the Redis key for storing detection results (deprecated)
 *
 * This function is deprecated as we now use the format recognition["stream.id"] directly.
 *
 * @param key The Redis key to use
 * @return int 0 on success, negative value on error
 */
int rknn_vision_set_redis_key(const char* key) {
    std::lock_guard<std::mutex> lock(g_mutex);

    if (!key) {
        SPDLOG_ERROR("Invalid Redis key (null)");
        return -1;
    }

    // This function is deprecated, but we'll keep it for backward compatibility
    SPDLOG_WARN("rknn_vision_set_redis_key is deprecated, Redis keys are now automatically set to recognition[\"stream.id\"]");
    return 0;
}

} // extern "C"

// C++ API Implementation
namespace rknn_vision {

bool init(const std::vector<RknnStreamInfo>& streams, const RknnRedisConfig& redis_config, bool handle_signals) {
    // Call C API directly with the original vector data
    // The C API will copy any necessary data internally
    int result = rknn_vision_init(streams.data(), static_cast<int>(streams.size()), &redis_config, handle_signals);

    return result == 0;
}

bool start() {
    return rknn_vision_start() == 0;
}

bool stop() {
    return rknn_vision_stop() == 0;
}

float getStreamFps(const std::string& stream_id) {
    return rknn_vision_get_stream_fps(stream_id.c_str());
}

bool isStreamActive(const std::string& stream_id) {
    return rknn_vision_is_stream_active(stream_id.c_str()) != 0;
}

bool setRedisKey(const std::string& key) {
    // This function is deprecated, but we'll keep it for backward compatibility
    SPDLOG_WARN("setRedisKey is deprecated, Redis keys are now automatically set to recognition[\"stream.id\"]");
    return true;
}

} // namespace rknn_vision
