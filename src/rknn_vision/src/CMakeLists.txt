
if(NOT (CMAKE_SYSTEM_PROCESSOR MATCHES "arm" OR CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64"))
    message(STATUS "Skipping rknn_vision src project build on non-ARM platform (${CMAKE_SYSTEM_PROCESSOR})")
    return()
endif()


#add_definitions(-g -O0 -ggdb -gdwarf -funwind-tables -rdynamic)

#set(CMAKE_CXX_STANDARD 11)
#set(CMAKE_CXX_STANDARD_REQUIRED ON)
#add_definitions(-D_GLIBCXX_USE_C99=1)

# 启用详细输出，查看实际编译命令
set(CMAKE_VERBOSE_MAKEFILE ON)

# 直接添加到编译标志中
# 使用更强力的方式添加系统头文件路径
#set(CMAKE_C_FLAGS "-isystem /usr/include -Wno-attributes -Wno-write-strings -Wno-deprecated-declarations ${CMAKE_C_FLAGS} -s")
#set(CMAKE_CXX_FLAGS "-isystem /usr/include -Wno-attributes -Wno-write-strings -Wno-deprecated-declarations ${CMAKE_CXX_FLAGS} -s")
set(CMAKE_EXE_LINKER_FLAGS "-Wl,-rpath=${PROJECT_SOURCE_DIR}/third_party/onboard_libs/ ${CMAKE_EXE_LINKER_FLAGS}")

#aux_source_directory(. SRC_LIST)
file(GLOB SRC_LIST "*.cpp" "*.c")

# Exclude the library implementation file from the executable
list(FILTER SRC_LIST EXCLUDE REGEX "rknn_vision_lib\.cpp$")

# 添加异步解码器源文件
list(APPEND SRC_LIST "${CMAKE_CURRENT_SOURCE_DIR}/ffmpeg_decode2_async.cpp")

# target
# 使用更明确的方式添加系统头文件路径
#include_directories(SYSTEM "/usr/include")
include_directories(${PROJECT_SOURCE_DIR}/third_party/rga/include)

#opencv
find_package(OpenCV 4 REQUIRED PATHS /opt/destdir/opencv/lib/cmake/opencv4)
message(STATUS "OpenCV_INCLUDE_DIRS: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OpenCV_LIBS: ${OpenCV_LIBS}")
include_directories(${OpenCV_INCLUDE_DIRS})
link_directories(${OpenCV_LIB_DIR})

#ffmpeg-mpp
set(FFMPEG_DIR /opt/destdir/ffmpeg)
message(STATUS "FFMPEG_DIR: ${FFMPEG_DIR}")
include_directories(${FFMPEG_DIR}/include)
link_directories(${FFMPEG_DIR}/lib)


#redis
set(PUB_LIB_PATH "/opt/xiaolu/pubLib")
set(REDIS_ROOT "${PUB_LIB_PATH}/redis")
# 添加 Redis 头文件路径
include_directories(${REDIS_ROOT}/include)
# 添加 Redis 库文件路径
link_directories(${REDIS_ROOT}/lib ${PUB_LIB_PATH}/hiredis/lib)

# rockchip
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/rockchip)
aux_source_directory(rockchip RK_SRCS)

# Add the new RGA utility files explicitly
list(APPEND RK_SRCS "${CMAKE_CURRENT_SOURCE_DIR}/rockchip/rga_utils.cpp")

list(APPEND SRC_LIST ${RK_SRCS})

link_directories(
        ${PROJECT_SOURCE_DIR}/third_party/onboard_libs/
)

add_executable(ffmpeg_tutorial ${SRC_LIST})
target_link_libraries(ffmpeg_tutorial rga drm rknnrt mali rockchip_mpp)
# target_link_libraries(ffmpeg_tutorial SDL2)
target_link_libraries(ffmpeg_tutorial opencv_features2d opencv_videoio opencv_highgui opencv_imgproc opencv_imgcodecs opencv_core)
target_link_libraries(ffmpeg_tutorial avcodec swresample swscale avfilter avdevice avutil avformat)
target_link_libraries(ffmpeg_tutorial -pthread stdc++ dl)

# 不再需要链接spdlog和fmt库，因为我们不再使用ThreadPool

# 添加C++17支持
set_property(TARGET ffmpeg_tutorial PROPERTY CXX_STANDARD 17)

INSTALL(TARGETS ffmpeg_tutorial DESTINATION bin)
