#ifndef __FFMPEG_DECODE2_ASYNC_H__
#define __FFMPEG_DECODE2_ASYNC_H__

#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <memory>
#include <thread>
#include <vector>
#include <functional>
#include <future>
#include "ffmpeg.h"
#include "thread_pool.h"
//#include "stream_manager.h"

// 前向声明StreamInfo结构体，避免循环引用
struct StreamInfo;

// 使用ffmpeg.h中定义的FrameData和SafeQueue

// 异步解码器类，扩展FFmpegStreamChannel类
// 定义检测结果回调函数类型
using DetectionCallback = std::function<void(const std::string& json_result, const std::string& camera_id, const
std::string& channel_id)>;

class AsyncFFmpegDecoder {
public:
    explicit AsyncFFmpegDecoder(FFmpegStreamChannel* channel, int queue_size = 1000, int num_threads = 1,
        const std::vector<std::string> &targetClassNames = {"person", "car"}, bool showGui = false);
    ~AsyncFFmpegDecoder();

    // 启动异步解码
    bool start(const char* input_stream_url);

    // 停止异步解码
    void stop();

    // 检查是否正在运行
    bool isRunning() const { return running.load(); }

    // 设置关联的StreamInfo，用于更新FPS
    void setStreamInfo(StreamInfo* info) {
        streamInfo = info;
        roi_needs_update = true; // 设置新的StreamInfo时，需要更新ROI
    }

    // 设置检测结果回调函数
    void setDetectionCallback(DetectionCallback callback) {
        std::lock_guard<std::mutex> lock(callbackMutex);
        detectionCallback = std::move(callback);
    }

    // 根据ROI和类别名称过滤检测结果
    std::vector<::detect_result_t> filterDetectionsByRoiAndClasses(int roi_x1, int roi_y1, int roi_x2, int roi_y2, const std::vector<std::string> &target_class_names, const
                                                                   detect_result_group_t &detect_result_group, int w, int h);

    // 在视频帧上绘制过滤后的检测结果
    void drawFilteredDetectionResults(int roi_x, int roi_y, int roi_x2, int roi_y2,
                                    const std::vector<::detect_result_t>& filtered_results,
                                    std::shared_ptr<FrameData> frame_data);

    // 显示处理后的视频帧
    void displayProcessedFrame(const std::string& window_name);

    // 通知ROI已更新，需要重新计算
    void notifyRoiUpdated() { roi_needs_update = true; }

private:
    // 解码线程函数
    void decodeThreadFunc(const char* input_stream_url);

    // 处理线程函数 - 每个处理线程持续从队列获取帧并处理
    void processThreadFunc();

    // 处理单个帧的函数
    void processFrame(std::shared_ptr<FrameData> frame_data);

    // 调用检测结果回调函数
    void invokeDetectionCallback(const std::string &json_result, const std::string &camera_id, const std::string &channel_id);

    FFmpegStreamChannel* channel;          // 指向FFmpegStreamChannel的指针
    std::vector<std::thread> processThreads; // 处理线程
    SafeQueue<std::shared_ptr<FrameData>> frameQueue; // 帧队列

    std::atomic<bool> running;             // 运行标志
    std::thread decodeThread;              // 解码线程

    int maxQueueSize;                      // 最大队列大小
    int numThreads;                        // 处理线程数量
    std::mutex showMutex;                  // 显示互斥锁，用于保护显示操作
    std::mutex callbackMutex;              // 回调函数互斥锁
    std::shared_ptr<cv::Mat> mat4show;     // 用于显示的Mat对象
    StreamInfo* streamInfo = nullptr;      // 指向关联的StreamInfo的指针
    DetectionCallback detectionCallback;   // 检测结果回调函数
    std::shared_ptr<ThreadPool> callbackThreadPool; // 用于异步执行回调函数的线程池
    std::vector<std::string> targetClassNames; // 多个目标类型名称
    bool showGUIResult; // 是否显示GUI


    // 缓存的ROI区域坐标
    int cached_roi_x1 = 0;
    int cached_roi_y1 = 0;
    int cached_roi_x2 = 0;
    int cached_roi_y2 = 0;
    bool roi_needs_update = true;  // 标记ROI是否需要更新


};

#endif // __FFMPEG_DECODE2_ASYNC_H__
