#include <errno.h>
#include <fcntl.h>
#include <linux/videodev2.h>
#include <signal.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <unistd.h>
#include <exception>  // 为std::set_terminate
#include <memory>
#include <csignal>
#include <iostream>
#include <thread>
#include <chrono>
#include "stream_manager.h"

// 全局变量
volatile sig_atomic_t g_flag_run = true;
std::unique_ptr<StreamManager> g_stream_manager;

// 信号处理函数
static void signal_handler(int signo) {
    static bool shutdownInProgress = false;

    // 防止多次调用信号处理函数
    if (shutdownInProgress) {
        std::cout << "\nShutdown already in progress. Forcing exit..." << std::endl;
        exit(1); // 强制退出
    }

    shutdownInProgress = true;
    std::cout << "\nReceived signal " << signo << ". Initiating graceful shutdown..." << std::endl;
    g_flag_run = false;

    // 设置一个定时器，如果5秒内没有正常退出，则强制退出
    std::thread([signo]() {
        std::this_thread::sleep_for(std::chrono::seconds(5));
        std::cerr << "\nForced exit after timeout. Signal: " << signo << std::endl;
        exit(1);
    }).detach();
}

// 设置信号处理
static void setup_signals() {
    struct sigaction sa{};
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;

    // 注册需要处理的信号
    const int signals[] = {
        SIGINT, SIGTERM, SIGQUIT,
        SIGABRT, SIGFPE, SIGILL, SIGBUS
    };

    for (int sig : signals) {
        if (sigaction(sig, &sa, nullptr) == -1) {
            throw std::runtime_error("Failed to set up signal handler for signal " + std::to_string(sig));
        }
    }

    // 忽略 SIGPIPE
    signal(SIGPIPE, SIG_IGN);
}

// 异常处理函数
static void handle_terminate() {
    std::cout << "Unhandled exception detected. Cleaning up resources..." << std::endl;

    if (g_stream_manager) {
        g_stream_manager->stopAllStreams();
        g_stream_manager.reset();
    }

    std::abort();
}

int main(int argc, char **argv) {
    //add an environment variable
    setenv("OPENCV_FFMPEG_CAPTURE_OPTIONS", "video_codec;h264_rkmpp", 1);
    //set an opencv log level environment variable
    setenv("OPENCV_LOG_LEVEL", "ERROR", 1);
    //set an opencv ffmpeg log level environment variable
    setenv("OPENCV_FFMPEG_LOG_LEVEL", "48", 1);

    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <rtsp_url1> [roi_x1,roi_y1,roi_x2,roi_y2] [rtsp_url2] [roi_x1,roi_y1,roi_x2,roi_y2] ...\n";
        std::cerr << "ROI coordinates are optional. If not provided, the entire frame will be used.\n";
        std::cerr << "Example: " << argv[0] << " rtsp://example.com/stream1 100,200,500,600 rtsp://example.com/stream2\n";
        return EXIT_FAILURE;
    }

    // 设置未捕获异常的处理函数
    std::set_terminate(handle_terminate);

    try {
        // 设置信号处理
        setup_signals();

        // 创建流管理器
        g_stream_manager = std::make_unique<StreamManager>();

        // 添加所有输入的流
        std::vector<std::string> failed_streams;
        for (int i = 1; i < argc; i++) {
            std::string arg = argv[i];

            // 检查参数是否是ROI坐标
            if (arg.find(',') != std::string::npos && i > 1) {
                // 这是ROI坐标参数，跳过
                continue;
            }

            std::string url = arg;
            std::string name = "Stream_" + std::to_string((i+1)/2); // 考虑可能的ROI参数

            // 检查下一个参数是否是ROI坐标
            int roi_x1 = 0, roi_y1 = 0, roi_x2 = 0, roi_y2 = 0;
            if (i + 1 < argc) {
                std::string next_arg = argv[i + 1];
                if (next_arg.find(',') != std::string::npos) {
                    // 解析ROI坐标
                    try {
                        size_t pos1 = next_arg.find(',');
                        size_t pos2 = next_arg.find(',', pos1 + 1);
                        size_t pos3 = next_arg.find(',', pos2 + 1);

                        if (pos1 != std::string::npos && pos2 != std::string::npos && pos3 != std::string::npos) {
                            roi_x1 = std::stoi(next_arg.substr(0, pos1));
                            roi_y1 = std::stoi(next_arg.substr(pos1 + 1, pos2 - pos1 - 1));
                            roi_x2 = std::stoi(next_arg.substr(pos2 + 1, pos3 - pos2 - 1));
                            roi_y2 = std::stoi(next_arg.substr(pos3 + 1));
                            i++; // 跳过下一个参数，因为它是ROI坐标
                        }
                    } catch (const std::exception& e) {
                        std::cerr << "Error parsing ROI coordinates: " << next_arg << ": " << e.what() << std::endl;
                    }
                }
            }

            try {
                int camId = (i+1)/2; // Use the same calculation as for name
                if (!g_stream_manager->addStream(url, name, roi_x1, roi_y1, roi_x2, roi_y2, camId)) {
                    failed_streams.push_back(url);
                    std::cerr << "Failed to add stream: " << url << std::endl;
                    continue;
                }
                std::cout << "Successfully added stream: " << url;
                if (roi_x2 > roi_x1 && roi_y2 > roi_y1) {
                    std::cout << " with ROI: (" << roi_x1 << "," << roi_y1 << "," << roi_x2 << "," << roi_y2 << ")";
                }
                std::cout << std::endl;
            } catch (const std::exception& e) {
                failed_streams.push_back(url);
                std::cerr << "Exception while adding stream " << url << ": " << e.what() << std::endl;
            }
        }

        // 检查是否所有流都添加失败
        if (failed_streams.size() == static_cast<size_t>(argc - 1)) {
            throw std::runtime_error("All streams failed to initialize");
        }

        // 如果有部分流添加失败，输出警告
        if (!failed_streams.empty()) {
            std::cerr << "\nWarning: Failed to add " << failed_streams.size() << " stream(s):" << std::endl;
            for (const auto& url : failed_streams) {
                std::cerr << "  - " << url << std::endl;
            }
        }

        // 启动所有成功添加的流
        std::cout << "\nStarting streams..." << std::endl;

        // 添加超时机制，防止startAllStreams卡住
        std::atomic<bool> startComplete(false);
        std::thread startThread([&]() {
            if (!g_stream_manager->startAllStreams()) {
                std::cerr << "Warning: Some streams failed to start" << std::endl;
            }
            startComplete.store(true);
        });

        // 等待启动完成或超时
        const int startTimeoutSec = 10; // 10秒超时
        for (int i = 0; i < startTimeoutSec && !startComplete.load() && g_flag_run; i++) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            std::cout << "Waiting for streams to start... " << (i+1) << "/" << startTimeoutSec << std::endl;
        }

        // 如果超时或被中断，强制结束启动线程
        if (!startComplete.load()) {
            std::cerr << "Stream startup timed out or was interrupted. Proceeding anyway." << std::endl;
            // 不等待线程结束，让它在后台继续尝试
            startThread.detach();
        } else {
            startThread.join();
        }

        std::cout << "\nAll streams initialized. Press Ctrl+C to exit." << std::endl;

        // 主循环
        while (g_flag_run) {
            // 显示所有流的FPS信息
            auto streamInfoList = g_stream_manager->getStreamInfoList();
            if (!streamInfoList.empty()) {
                std::cout << "\n----- Stream Status -----" << std::endl;
                for (const auto& streamInfo : streamInfoList) {
                    std::cout << "Stream [" << streamInfo.name << "] - "
                              << (streamInfo.active ? "Active" : "Inactive")
                              << ", FPS: " << streamInfo.fps;

                    // 显示channelId
                    if (!streamInfo.channelId.empty()) {
                        std::cout << ", ChannelID: " << streamInfo.channelId;
                    }

                    // 显示ROI信息，如果设置了的话
                    if (streamInfo.roi_x2 > streamInfo.roi_x1 && streamInfo.roi_y2 > streamInfo.roi_y1) {
                        std::cout << ", ROI: (" << streamInfo.roi_x1 << "," << streamInfo.roi_y1
                                  << "," << streamInfo.roi_x2 << "," << streamInfo.roi_y2 << ")";
                    }
                    std::cout << std::endl;
                }
                std::cout << "------------------------" << std::endl;
            }
            // 每3秒更新一次状态
            std::this_thread::sleep_for(std::chrono::seconds(3));
        }

        // 开始清理
        std::cout << "\nStarting cleanup process..." << std::endl;

        // 添加超时机制，防止stopAllStreams卡住
        std::atomic<bool> stopComplete(false);
        std::thread stopThread([&]() {
            try {
                // 停止所有流
                g_stream_manager->stopAllStreams();
                stopComplete.store(true);
            } catch (const std::exception& e) {
                std::cerr << "Exception during stream shutdown: " << e.what() << std::endl;
            } catch (...) {
                std::cerr << "Unknown exception during stream shutdown" << std::endl;
            }
        });

        // 等待停止完成或超时
        const int stopTimeoutSec = 5; // 5秒超时
        for (int i = 0; i < stopTimeoutSec && !stopComplete.load(); i++) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            std::cout << "Waiting for streams to stop... " << (i+1) << "/" << stopTimeoutSec << std::endl;
        }

        // 如果超时，不再等待
        if (!stopComplete.load()) {
            std::cerr << "Stream shutdown timed out. Proceeding with cleanup anyway." << std::endl;
            stopThread.detach();
        } else {
            stopThread.join();
        }

        // 释放资源
        g_stream_manager.reset();

        std::cout << "Cleanup completed. Exiting normally." << std::endl;
        return EXIT_SUCCESS;

    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;

        // 确保资源被清理
        if (g_stream_manager) {
            try {
                g_stream_manager->stopAllStreams();
                g_stream_manager.reset();
            } catch (...) {
                std::cerr << "Error during cleanup" << std::endl;
            }
        }

        return EXIT_FAILURE;
    } catch (...) {
        std::cerr << "Unknown fatal error occurred" << std::endl;

        // 确保资源被清理
        if (g_stream_manager) {
            try {
                g_stream_manager->stopAllStreams();
                g_stream_manager.reset();
            } catch (...) {
                std::cerr << "Error during cleanup" << std::endl;
            }
        }

        return EXIT_FAILURE;
    }
}

