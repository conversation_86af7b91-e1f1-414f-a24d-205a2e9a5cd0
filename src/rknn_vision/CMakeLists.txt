
if(NOT (CMAKE_SYSTEM_PROCESSOR MATCHES "arm" OR CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64"))
    message(STATUS "Skipping RKNN Vision project build on non-ARM platform (${CMAKE_SYSTEM_PROCESSOR})")
    return()
endif()

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Enable verbose output to see actual compilation commands
set(CMAKE_VERBOSE_MAKEFILE ON)

# Set linker flags for runtime path
set(CMAKE_EXE_LINKER_FLAGS "-Wl,-rpath=${PROJECT_SOURCE_DIR}/third_party/onboard_libs/:${PROJECT_SOURCE_DIR}/third_party/rga/lib/ ${CMAKE_EXE_LINKER_FLAGS}")
set(CMAKE_SHARED_LINKER_FLAGS "-Wl,-rpath=${PROJECT_SOURCE_DIR}/third_party/onboard_libs/:${PROJECT_SOURCE_DIR}/third_party/rga/lib/ ${CMAKE_SHARED_LINKER_FLAGS}")

# Define FMT_HEADER_ONLY to use fmt library in header-only mode
add_definitions(-DFMT_HEADER_ONLY)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${PROJECT_SOURCE_DIR}/third_party/rga/include
    ${PROJECT_SOURCE_DIR}/third_party/spdlog/include
)

# Add library directories
link_directories(
    ${PROJECT_SOURCE_DIR}/third_party/rga/lib
    ${PROJECT_SOURCE_DIR}/third_party/onboard_libs
)

# Find OpenCV
find_package(OpenCV 4 REQUIRED PATHS /opt/destdir/opencv/lib/cmake/opencv4)
message(STATUS "OpenCV_INCLUDE_DIRS: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OpenCV_LIBS: ${OpenCV_LIBS}")
include_directories(${OpenCV_INCLUDE_DIRS})
link_directories(${OpenCV_LIB_DIR})

# FFmpeg
set(FFMPEG_DIR /opt/destdir/ffmpeg)
message(STATUS "FFMPEG_DIR: ${FFMPEG_DIR}")
include_directories(${FFMPEG_DIR}/include)
link_directories(${FFMPEG_DIR}/lib)

# Redis
set(PUB_LIB_PATH "/opt/xiaolu/pubLib")
set(REDIS_ROOT "${PUB_LIB_PATH}/redis")
# 添加 Redis 头文件路径
include_directories(${REDIS_ROOT}/include)
# 添加 Redis 库文件路径
link_directories(${REDIS_ROOT}/lib ${PUB_LIB_PATH}/hiredis/lib)

# Rockchip
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/src/rockchip)
file(GLOB RK_SRCS "${CMAKE_CURRENT_SOURCE_DIR}/src/rockchip/*.cpp" "${CMAKE_CURRENT_SOURCE_DIR}/src/rockchip/*.c")

# Add the RGA utility files
list(APPEND RK_SRCS "${CMAKE_CURRENT_SOURCE_DIR}/src/rockchip/rga_utils.cpp")

# Library source files
file(GLOB LIB_SRCS
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*.c"
)

# Add the library implementation file
list(APPEND LIB_SRCS "${CMAKE_CURRENT_SOURCE_DIR}/src/rknn_vision_lib.cpp")

# Remove main.cpp and optimized_main.cpp from library sources
list(FILTER LIB_SRCS EXCLUDE REGEX ".*main\.cpp$")
list(FILTER LIB_SRCS EXCLUDE REGEX ".*optimized_main\.cpp$")

# Add the library
add_library(rknn_vision SHARED
    ${LIB_SRCS}
    ${RK_SRCS}
)

# Set library properties
set_target_properties(rknn_vision PROPERTIES
    VERSION 1.0.0
    SOVERSION 1
    PUBLIC_HEADER "${CMAKE_CURRENT_SOURCE_DIR}/include/rknn_vision_lib.h"
)
# Link libraries
target_link_libraries(rknn_vision
    # Specify full path to RGA library to avoid linking issues
    ${PROJECT_SOURCE_DIR}/third_party/rga/lib/librga.so
    drm
    mali
    rknnrt
    rockchip_mpp
    opencv_features2d
    opencv_videoio
    opencv_highgui
    opencv_imgproc
    opencv_imgcodecs
    opencv_core
    avcodec
    swresample
    swscale
    avfilter
    avdevice
    avutil
    avformat
    -pthread
    stdc++
    dl
    RedisClient
)

# Example application
add_executable(rknn_vision_example
    ${CMAKE_CURRENT_SOURCE_DIR}/examples/rknn_vision_example.cpp
)

# Link the example with the library
target_link_libraries(rknn_vision_example
    rknn_vision
    ${OpenCV_LIBS}
    ${PROJECT_SOURCE_DIR}/third_party/rga/lib/librga.so
    -pthread
)

# Installation rules
install(TARGETS rknn_vision
    LIBRARY DESTINATION lib
    PUBLIC_HEADER DESTINATION include
)

install(TARGETS rknn_vision_example
    RUNTIME DESTINATION bin
)
