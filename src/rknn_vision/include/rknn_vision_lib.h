/**
 * @file rknn_vision_lib.h
 * @brief RKNN Vision Library Interface
 *
 * This library provides an interface for video stream processing with RKNN models
 * and Redis integration for result publishing.
 */

#ifndef __RKNN_VISION_LIB_H__
#define __RKNN_VISION_LIB_H__

#include <string>
#include <vector>

/**
 * @brief Region of Interest (ROI) structure
 */
typedef struct {
    int x1;  // Top-left X coordinate
    int y1;  // Top-left Y coordinate
    int x2;  // Bottom-right X coordinate
    int y2;  // Bottom-right Y coordinate
} RknnRoi;

/**
 * @brief Stream information structure
 */
typedef struct {
    std::string url;                       // Stream URL (RTSP, HTTP, file, etc.)
    std::string id;                        // Unique stream identifier
    RknnRoi roi;                           // Region of interest
    std::string camId;                     // Camera ID
    std::vector<std::string> targetClassNames = {"person"}; // Target class names to detect (default: {"person"})
    bool showGui = false;                  // Whether to show GUI result (default: false)
} RknnStreamInfo;

/**
 * @brief Redis configuration structure
 */
typedef struct {
    std::string host;    // Redis host address
    int port;            // Redis port
    std::string password; // Redis password
    int db_index = 2;        // Redis database index (default: 2)
} RknnRedisConfig;

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize the RKNN Vision library
 *
 * @param streams Array of stream information (including targetClassNames for each stream)
 * @param stream_count Number of streams
 * @param redis_config Redis configuration
 * @param handle_signals Whether the library should handle signals (default: false)
 * @return int 0 on success, negative value on error
 */
int rknn_vision_init(const RknnStreamInfo* streams, int stream_count, const RknnRedisConfig* redis_config, bool handle_signals = false);

/**
 * @brief Start processing the streams
 *
 * @return int 0 on success, negative value on error
 */
int rknn_vision_start();

/**
 * @brief Stop processing the streams
 *
 * @return int 0 on success, negative value on error
 */
int rknn_vision_stop();

/**
 * @brief Get the current FPS for a specific stream
 *
 * @param stream_id Stream identifier
 * @return float Current FPS, 0.0 if stream not found or not active
 */
float rknn_vision_get_stream_fps(const char* stream_id);

/**
 * @brief Get the status of a specific stream
 *
 * @param stream_id Stream identifier
 * @return int 1 if active, 0 if inactive or not found
 */
int rknn_vision_is_stream_active(const char* stream_id);

/**
 * @brief Set the Redis key for storing detection results (deprecated)
 *
 * This function is deprecated as we now use the format recognition["stream.id"] directly.
 *
 * @param key The Redis key to use
 * @return int 0 on success, negative value on error
 */
int rknn_vision_set_redis_key(const char* key);

#ifdef __cplusplus
}
#endif

// C++ API
#ifdef __cplusplus

namespace rknn_vision {

/**
 * @brief Initialize the RKNN Vision library
 *
 * @param streams Vector of stream information (including targetClassNames for each stream)
 * @param redis_config Redis configuration
 * @param handle_signals Whether the library should handle signals (default: false)
 * @return bool true on success, false on error
 */
bool init(const std::vector<RknnStreamInfo>& streams, const RknnRedisConfig& redis_config, bool handle_signals = false);

/**
 * @brief Start processing the streams
 *
 * @return bool true on success, false on error
 */
bool start();

/**
 * @brief Stop processing the streams
 *
 * @return bool true on success, false on error
 */
bool stop();

/**
 * @brief Get the current FPS for a specific stream
 *
 * @param stream_id Stream identifier
 * @return float Current FPS, 0.0 if stream not found or not active
 */
float getStreamFps(const std::string& stream_id);

/**
 * @brief Get the status of a specific stream
 *
 * @param stream_id Stream identifier
 * @return bool true if active, false if inactive or not found
 */
bool isStreamActive(const std::string& stream_id);

/**
 * @brief Set the Redis key for storing detection results (deprecated)
 *
 * This function is deprecated as we now use the format recognition["stream.id"] directly.
 *
 * @param key The Redis key to use
 * @return bool true on success, false on error
 */
bool setRedisKey(const std::string& key);

} // namespace rknn_vision

#endif // __cplusplus

#endif // __RKNN_VISION_LIB_H__
