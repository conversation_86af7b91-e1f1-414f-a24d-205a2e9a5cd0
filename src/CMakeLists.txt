#cmake_minimum_required(VERSION 3.10)
# 添加 include 目录
if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm" OR CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
    message(STATUS "SRC Current platform is ARM")
    message(STATUS "CMAKE_SYSROOT: ${CMAKE_SYSROOT}")
    set(OPENAPI_LIB_DIR ${PROJECT_SOURCE_DIR}/third_party/OpenAPI_C/aarch64/public/lib/linux/Release)
    set(OPENAPI_INCLUDE_DIR ${PROJECT_SOURCE_DIR}/third_party/OpenAPI_C/aarch64/public/include/)
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64" OR CMAKE_SYSTEM_PROCESSOR MATCHES "i686")
    message(STATUS "SRC Current platform is x86")
    set(OPENAPI_LIB_DIR ${PROJECT_SOURCE_DIR}/third_party/OpenAPI_C/x86_64/public/lib/linux/Release)
    set(OPENAPI_INCLUDE_DIR ${PROJECT_SOURCE_DIR}/third_party/OpenAPI_C/x86_64/public/include/)
else()
    message(STATUS "Current platform is neither ARM nor x86")
endif()

include_directories(
        ${PROJECT_SOURCE_DIR}/src/
        ${PROJECT_SOURCE_DIR}/third_party/nlohmann_jsoncpp/include/
        ${PROJECT_SOURCE_DIR}/third_party/spdlog/include/
        ${PROJECT_SOURCE_DIR}/third_party/tinyxml2/
        ${PROJECT_SOURCE_DIR}/third_party/
        ${OPENAPI_INCLUDE_DIR}
)

file(GLOB_RECURSE STNCore_SOURCES
        "${PROJECT_SOURCE_DIR}/src/core/*.cpp"
        "${PROJECT_SOURCE_DIR}/src/core/*.hpp"
        "${PROJECT_SOURCE_DIR}/src/components/*.cpp"
        "${PROJECT_SOURCE_DIR}/src/components/*.hpp"
)
# 排除 FlowControlModule 相关文件
list(FILTER STNCore_SOURCES EXCLUDE REGEX ".*/FlowControlModule\\.(cpp|hpp)$")
list(FILTER STNCore_SOURCES EXCLUDE REGEX ".*PythonInterface\\.(cpp|hpp)$")

list(FILTER STNCore_SOURCES EXCLUDE REGEX ".*/TrafficControlServer\\.(cpp|hpp)$")
list(FILTER STNCore_SOURCES EXCLUDE REGEX ".*TrafficControlServer\\.(cpp|hpp)$")

add_library(STNCore STATIC ${STNCore_SOURCES}
        components/data_providers/RedisDataProvider.cpp
        components/data_providers/RedisDataProvider.hpp)

file(GLOB_RECURSE STNUtils_SOURCES
        "${PROJECT_SOURCE_DIR}/src/utils/*.cpp"
        "${PROJECT_SOURCE_DIR}/src/utils/*.hpp"
        "${PROJECT_SOURCE_DIR}/third_party/tinyxml2/tinyxml2.cpp"
)
add_library(STNUtils STATIC ${STNUtils_SOURCES})

#file(GLOB_RECURSE TrafficControlServer_SOURCES
#        "${PROJECT_SOURCE_DIR}/src/TrafficControlServer/*.cpp"
#        "${PROJECT_SOURCE_DIR}/src/TrafficControlServer/*.hpp"
#)
#add_executable(TrafficControlServer ${TrafficControlServer_SOURCES})

# 查找 hiredis 库
find_path(HIREDIS_INCLUDE_DIR hiredis/hiredis.h  PATHS /opt/xiaolu/pubLib/redis/include NO_DEFAULT_PATH)
message(STATUS "HIREDIS_INCLUDE_DIR: ${HIREDIS_INCLUDE_DIR}")
find_library(HIREDIS_LIBS RedisClient PATHS /opt/xiaolu/pubLib/redis/lib NO_DEFAULT_PATH)
message(STATUS "HIREDIS_LIBS ${HIREDIS_LIBS}")

# 查找 event 库
find_path(EVENT_INCLUDE_DIR event2/event.h)
find_library(EVENT_LIBS event PATHS ${CMAKE_SYSROOT}/usr/lib/aarch64-linux-gnu)
message(STATUS "EVENT_INCLUDE_DIR: ${EVENT_INCLUDE_DIR}")
message(STATUS "EVENT_LIBS: ${EVENT_LIBS}")


# 查找 BOOST 库
find_path(BOOST_INCLUDE_DIR boost/asio.hpp PATHS /opt/xiaolu/pubLib/boost/include NO_DEFAULT_PATH)
find_library(BOOST_LIBRARY_DIR libboost_thread.so.1.75.0 PATHS /opt/xiaolu/pubLib/boost/lib/ NO_DEFAULT_PATH)
message(STATUS "BOOST_INCLUDE_DIR: ${BOOST_INCLUDE_DIR}")
message(STATUS "BOOST_LIBS: ${BOOST_LIBRARY_DIR}")


# 查找 Python3 的解释器、开发库和头文件
#find_package(Python3 REQUIRED COMPONENTS Interpreter Development)
# 打印一些找到的值
#message(STATUS "Python3 interpreter: ${Python3_EXECUTABLE}")
#message(STATUS "Python3 include dirs: ${Python3_INCLUDE_DIRS}")
#message(STATUS "Python3 libraries: ${Python3_LIBRARIES}")
#message(STATUS "Python3 CFLAGS: ${Python3_COMPILER}")
#message(STATUS "Python3 LDFLAGS: ${Python3_LINK_OPTIONS}")


# 查找 nats库和头文件
find_path(NATS_INCLUDE_DIR
        NAMES nats/nats.h
        PATHS /opt/xiaolu/pubLib/nats_clt/include
)

find_library(NATS_LIBRARIES
        NAMES libnats.so.3.9.0
        PATHS /opt/xiaolu/pubLib/nats_clt/lib
)
message(STATUS "NATS include dirs:" ${NATS_INCLUDE_DIR})
message(STATUS "NATS library dirs:" ${NATS_LIBRARIES})

# 检查是否找到头文件和库
if (NATS_INCLUDE_DIR AND NATS_LIBRARIES)
    include_directories(${NATS_INCLUDE_DIR})
    target_link_libraries(STNUtils PRIVATE ${NATS_LIBRARIES})
endif()

set(OPENAPI_LIBS "-lHttpUtil")
target_link_directories(STNCore PUBLIC ${OPENAPI_LIB_DIR})

target_include_directories(STNUtils PRIVATE ${HIREDIS_INCLUDE_DIR}  ${Python3_INCLUDE_DIRS})
target_link_libraries(STNUtils PRIVATE ${HIREDIS_LIBS} ${Python3_LIBRARIES})

#target_include_directories(TrafficControlServer PUBLIC  ${Python3_INCLUDE_DIRS} ${BOOST_INCLUDE_DIR})
#target_link_libraries(TrafficControlServer PRIVATE pthread ${Python3_LIBRARIES} ${BOOST_LIBRARY_DIR})

target_include_directories(STNCore PUBLIC ${Python3_INCLUDE_DIRS} ${HIREDIS_INCLUDE_DIR})
target_link_libraries(STNCore PRIVATE pthread ${Python3_LIBRARIES} ${OPENAPI_LIBS} ${HIREDIS_LIBS})


# 设置安装目录，可通过 CMAKE_INSTALL_PREFIX 覆盖
set(CMAKE_INSTALL_PREFIX ${PROJECT_SOURCE_DIR})
set(LIBRARY_OUTPUT_PATH ${CMAKE_INSTALL_PREFIX}/lib)
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_INSTALL_PREFIX}/bin)
message(STATUS "Installation directory: ${CMAKE_INSTALL_PREFIX}")

# 安装库文件
install(TARGETS STNCore STNUtils
        LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/lib
        ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/lib)

# 安装可执行文件
#install(TARGETS TrafficControlServer
#        RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin)

# 安装可能需要的头文件（如果有公共API）
# install(DIRECTORY ${PROJECT_SOURCE_DIR}/src/
#         DESTINATION ${CMAKE_INSTALL_PREFIX}/include
#         FILES_MATCHING PATTERN "*.hpp")

# 添加自动安装目标
add_custom_target(auto_install
    COMMAND ${CMAKE_COMMAND} -DCOMPONENT=Runtime -P ${CMAKE_BINARY_DIR}/cmake_install.cmake
    COMMENT "自动安装到 ${CMAKE_INSTALL_PREFIX}"
)

# 设置依赖关系，确保先编译完成，再执行安装
add_dependencies(auto_install STNCore STNUtils ${PROJECT_NAME})

# 将自动安装添加为ALL目标的依赖，使其在构建时自动执行
# add_dependencies(ALL auto_install)

add_subdirectory(VisionProcess)
add_subdirectory(rknn_vision)
#add_subdirectory(rknn_vision/src)
