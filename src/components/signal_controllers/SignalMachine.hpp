#ifndef STN_SIGNALMACHINE_HPP
#define STN_SIGNALMACHINE_HPP

#include <unordered_set>

#include "core/interfaces/ISignalController.hpp"
#include "utils/Logger.hpp"
#include "components/ComponentsType.hpp"
#include "json.hpp"
#include "SignalDevices/ISignalDevice.hpp"
#include "utils/NatsClient.hpp"
#include "utils/RedisClient.hpp"

using json = nlohmann::json;

class SignalMachine : public ISignalController {
public:
    static void sendAlgErrorNotification(int algErrCount);

    void controlSignals(int phase) override;

    void addObserver(IObserver* observer) override {
        observers.push_back(observer);
    }

    void removeObserver(IObserver* observer) override {
        observers.erase(std::remove(observers.begin(), observers.end(), observer), observers.end());
    }

    void notifyAllObservers(const std::string& message) override {
        for (auto observer : observers) {
            observer->update(this, message);
        }
    }

    void notifyObservers(const std::string& message, int component) override {
        for (auto observer : observers) {
            if (observer->getType() == component) {
                observer->update(this, message);
            }
        }
    }

    [[nodiscard]] int getType() const override {
       return static_cast<int>(ComponentsType::SignalMachine);
    }
    void update(ISubject* subject, const std::string& message) override;

    void sendControlModeStatusMessage() const;

    std::string handleUpstreamControlMessage(const std::string &subject, const std::string &msg);

    void querySignalState() override;

    void sendSignalStateStatusMessage(const SignalEnvState &ses) const;


    void resetSignalStates() override;

    /**
     * 重连信号机
     * @param waitSeconds 等待时间（秒）
     * @return 重连是否成功
     */
    bool reconnectSignalDevice(int waitSeconds);

    /**
     * 设置信号机重连阈值
     * @param seconds 重连阈值（秒）
     */
    void setSignalDeviceReconnectThreshold(int seconds);

    SignalMachine();
    ~SignalMachine() override;
private:
    /**
     * 更新Redis中的signalCtl配置
     * @param isInControlValue 要设置的isInControl值
     */
    void updateRedisSignalConfig(bool isInControlValue);

    /**
     * 构建完整的Redis键名
     */
    std::string getSignalConfigKey() const;
    std::vector<IObserver*> observers;
    json status;
    std::unique_ptr<ISignalDevice> signalDevice = nullptr;
    bool signalDeviceInitialized = false;
    bool isInControl = false; //default control or not.
    bool isStartControl = false;
    bool ctlModeChanged = false;
    std::shared_ptr<SignalCtl> signalCtl;
    int lastAlgPhase = 0;
    bool lastSignalCtlState = false;
    bool currentSignalCtlState = false;
    int algErrCount = 999;
    std::string intersectionCode_;
    std::mutex signalDeviceMutex;
    int durationInterval = 15;
    std::shared_ptr<RedisClient> redisClient_;
};

#endif