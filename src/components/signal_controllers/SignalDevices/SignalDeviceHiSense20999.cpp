#include "SignalDeviceHiSense20999.hpp"
#include <json.hpp>
#include <sstream>

using json = nlohmann::json;

// 前向声明
template<typename T>
void replace_next_placeholder(std::string& str, T&& value);

// 辅助函数，用于替代fmt::format
template<typename... Args>
std::string format_string(const std::string& format, Args&&... args) {
    // 这个简单实现只支持{}占位符，不支持格式化选项
    std::string result = format;
    // 使用折叠表达式处理所有参数
    (replace_next_placeholder(result, std::forward<Args>(args)), ...);
    return result;
}

// 替换下一个占位符
template<typename T>
void replace_next_placeholder(std::string& str, T&& value) {
    size_t pos = str.find("{}");
    if (pos != std::string::npos) {
        std::ostringstream oss;
        oss << value;
        str.replace(pos, 2, oss.str());
    }
}

// 获取当前时间戳
static int64_t getCurrentTimestamp() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

//=============================================================================
// HiSense20999Driver 实现
//=============================================================================

SignalDeviceHiSense20999::HiSense20999Driver::HiSense20999Driver(const Config& config)
    : m_config(config)
    , m_running(false)
    , m_connected(false)
    , m_lastAliveTimestamp(std::time(nullptr))
    , m_lastQueryTime(std::time(nullptr))
    , m_lastSetPhase(-1)
    , m_inTimetableMode(false)
    , m_timetableModeStartTime(0)
    , m_phaseStartTimestamp(std::time(nullptr))
    , m_currentPhase(0)
{
    try {
        // 创建UDP客户端和执行器
        LOG(INFO, format_string("Initializing HiSense20999Driver with IP: {}, Port: {}",
            m_config.hostIP, m_config.hostPort).c_str());

        // 创建UDP客户端
        m_udpClient = std::make_shared<UDPClient>(m_config.hostIP, m_config.hostPort);
        if (!m_udpClient) {
            LOG(ERROR, "Failed to create UDP client during initialization");
            throw std::runtime_error("Failed to create UDP client");
        }

        // 创建查询执行器
        m_queryExecutor = std::make_shared<gb20999::QueryExecutor>(
            m_udpClient, m_config.hostId, m_config.tscId, m_config.intersectionId);
        if (!m_queryExecutor) {
            LOG(ERROR, "Failed to create query executor during initialization");
            throw std::runtime_error("Failed to create query executor");
        }

        // 创建设置执行器
        m_setterExecutor = std::make_shared<gb20999::SetterExecutor>(
            m_udpClient, m_config.hostId, m_config.tscId, m_config.intersectionId);
        if (!m_setterExecutor) {
            LOG(ERROR, "Failed to create setter executor during initialization");
            throw std::runtime_error("Failed to create setter executor");
        }

        // 创建状态信息
        m_statusInfo = std::make_shared<gb20999::RunStatusInfo>();

        LOG(INFO, format_string("HiSense20999Driver initialized with IP: {}, Port: {}",
            m_config.hostIP, m_config.hostPort).c_str());

        // 测试连接
        try {
            LOG(INFO, format_string("Testing initial connection to {}:{}", m_config.hostIP, m_config.hostPort).c_str());

            // 尝试多次查询，提高成功率
            std::optional<gb20999::QueryResult> result;
            bool success = false;

            for (int retry = 0; retry < 5; ++retry) { // 增加重试次数
                LOG(INFO, format_string("Initial connection test attempt {}/5", retry + 1).c_str());
                result = m_queryExecutor->execute<gb20999::RunStatusQuery>();
                if (result.has_value()) {
                    LOG(INFO, format_string("Initial connection test successful on attempt {}", retry + 1).c_str());
                    success = true;
                    break;
                }
                LOG(WARNING, format_string("Initial connection test attempt {} failed, retrying...", retry + 1).c_str());
                std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 等待时间增加
            }

            if (success) {
                m_connected = true;

                // 更新状态信息
                if (auto* status = std::get_if<gb20999::RunStatusInfo>(&*result)) {
                    *m_statusInfo = *status;
                    // 初始化相位开始时间
                    m_currentPhase = status->currentPhaseStage;
                    m_phaseStartTimestamp = std::time(nullptr);
                    LOG(INFO, format_string("Initial status: phase={}, mode={}, plan={}",
                        static_cast<int>(status->currentPhaseStage),
                        static_cast<int>(status->runMode),
                        static_cast<int>(status->currentPlan)).c_str());
                }
            } else {
                LOG(WARNING, "All initial connection test attempts failed");
                m_connected = false;
            }
        } catch (const std::exception& e) {
            LOG(WARNING, format_string("Exception during initial connection test: {}", e.what()).c_str());
            // 不抛出异常，允许初始化继续
            m_connected = false;
        }
    } catch (const std::exception& e) {
        LOG(ERROR, format_string("Exception during HiSense20999Driver initialization: {}", e.what()).c_str());
        throw; // 重新抛出异常，让调用者处理
    }
}

SignalDeviceHiSense20999::HiSense20999Driver::~HiSense20999Driver() {
    stop();
}

void SignalDeviceHiSense20999::HiSense20999Driver::start() {
    if (m_running) {
        LOG(WARNING, "HiSense20999Driver already running");
        return;
    }

    m_running = true;
    m_thread = std::thread(&HiSense20999Driver::workerThread, this);
    LOG(INFO, "HiSense20999Driver worker thread started");
}

void SignalDeviceHiSense20999::HiSense20999Driver::stop() {
    if (!m_running) {
        return;
    }

    LOG(INFO, "Stopping HiSense20999Driver worker thread");

    // 清空命令队列并添加退出命令
    {
        std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
        std::queue<std::pair<std::string, int>> empty;
        std::swap(m_cmdQueue, empty);
        m_cmdQueue.push({"exit", 0});
    }

    // 设置运行标志为false
    m_running = false;

    // 等待线程结束，使用超时机制
    if (m_thread.joinable()) {
        // 创建一个线程来等待工作线程结束
        std::mutex mtx;
        std::condition_variable cv;
        bool threadExited = false;

        std::thread joinThread([this, &mtx, &cv, &threadExited]() {
            try {
                if (m_thread.joinable()) {
                    m_thread.join();
                }
                // 通知主线程工作线程已退出
                std::lock_guard<std::mutex> lock(mtx);
                threadExited = true;
                cv.notify_one();
            } catch (const std::exception& e) {
                LOG(ERROR, format_string("Exception while joining worker thread: {}", e.what()).c_str());
            } catch (...) {
                LOG(ERROR, "Unknown exception while joining worker thread");
            }
        });

        // 等待工作线程退出，最多等待3秒
        {
            std::unique_lock<std::mutex> lock(mtx);
            if (!cv.wait_for(lock, std::chrono::seconds(3), [&threadExited]{ return threadExited; })) {
                LOG(WARNING, "Worker thread did not exit within timeout, proceeding with cleanup");
                // 不等待joinThread线程，它将会被分离
            }
        }

        // 分离连接线程
        if (joinThread.joinable()) {
            joinThread.detach();
        }
    }

    // 清理资源
    m_setterExecutor.reset();
    m_queryExecutor.reset();
    m_udpClient.reset();

    LOG(INFO, "HiSense20999Driver worker thread stopped");
}

void SignalDeviceHiSense20999::HiSense20999Driver::setPhase(int phase) {
    if (phase <= 0) {
        LOG(ERROR, format_string("Invalid phase: {}", phase).c_str());
        return;
    }

    LOG(INFO, format_string("Queueing set phase command: {}", phase).c_str());
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"setPhase", phase});
}

void SignalDeviceHiSense20999::HiSense20999Driver::setControlMode(ControlMode mode) {
    LOG(INFO, format_string("Queueing set control mode command: {}", static_cast<int>(mode)).c_str());
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"setControlMode", static_cast<int>(mode)});
}

bool SignalDeviceHiSense20999::HiSense20999Driver::reconnect(int waitSeconds) {
    try {
        LOG(INFO, "Reconnecting HiSense20999 signal machine");

        // 标记连接状态为断开
        m_connected = false;

        // 清空命令队列，避免重连期间执行旧命令
        {
            std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
            std::queue<std::pair<std::string, int>> empty;
            std::swap(m_cmdQueue, empty);
        }

        // 释放资源
        m_setterExecutor.reset();
        m_queryExecutor.reset();
        m_udpClient.reset();

        // 等待指定的秒数
        if (waitSeconds > 0) {
            LOG(INFO, format_string("Waiting {} seconds before reconnecting", waitSeconds).c_str());
            std::this_thread::sleep_for(std::chrono::seconds(waitSeconds));
        }

        // 重新创建UDP客户端和执行器
        LOG(INFO, format_string("Creating new UDP client and executors for {}:{}", m_config.hostIP, m_config.hostPort).c_str());
        try {
            // 创建UDP客户端
            m_udpClient = std::make_shared<UDPClient>(m_config.hostIP, m_config.hostPort);
            if (!m_udpClient) {
                LOG(ERROR, "Failed to create UDP client");
                return false;
            }

            // 创建查询执行器
            m_queryExecutor = std::make_shared<gb20999::QueryExecutor>(
                m_udpClient, m_config.hostId, m_config.tscId, m_config.intersectionId);
            if (!m_queryExecutor) {
                LOG(ERROR, "Failed to create query executor");
                return false;
            }

            // 创建设置执行器
            m_setterExecutor = std::make_shared<gb20999::SetterExecutor>(
                m_udpClient, m_config.hostId, m_config.tscId, m_config.intersectionId);
            if (!m_setterExecutor) {
                LOG(ERROR, "Failed to create setter executor");
                return false;
            }
        } catch (const std::exception& e) {
            LOG(ERROR, format_string("Exception during creating UDP client and executors: {}", e.what()).c_str());
            return false;
        }

        // 重置状态信息
        {
            std::lock_guard<std::mutex> lock(m_statusMutex);
            m_statusInfo = std::make_shared<gb20999::RunStatusInfo>();
        }
        m_lastAliveTimestamp = std::time(nullptr);
        m_connected = true;

        // 重置timetable模式跟踪
        m_inTimetableMode = false;
        m_timetableModeStartTime = 0;

        // 重置相位跟踪
        m_currentPhase = 0;
        m_phaseStartTimestamp = std::time(nullptr);

        // 测试连接是否成功
        try {
            // 直接发送查询命令，而不是调用sendQueryStateMsg，避免日志混乱
            LOG(INFO, format_string("Testing connection to {}:{}", m_config.hostIP, m_config.hostPort).c_str());

            // 尝试多次查询，提高成功率
            std::optional<gb20999::QueryResult> result;
            bool success = false;

            for (int retry = 0; retry < 5; ++retry) { // 增加重试次数
                LOG(INFO, format_string("Connection test attempt {}/5", retry + 1).c_str());
                result = m_queryExecutor->execute<gb20999::RunStatusQuery>();
                if (result.has_value()) {
                    LOG(INFO, format_string("Connection test successful on attempt {}", retry + 1).c_str());
                    success = true;
                    break;
                }
                LOG(WARNING, format_string("Connection test attempt {} failed, retrying...", retry + 1).c_str());
                std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 等待时间增加
            }

            if (success) {
                // 更新状态信息
                std::lock_guard<std::mutex> lock(m_statusMutex);
                if (auto* status = std::get_if<gb20999::RunStatusInfo>(&*result)) {
                    *m_statusInfo = *status;
                    // 重新初始化相位开始时间
                    m_currentPhase = status->currentPhaseStage;
                    m_phaseStartTimestamp = std::time(nullptr);
                    LOG(INFO, format_string("Initial status after reconnection: phase={}, mode={}, plan={}",
                        status->currentPhaseStage,
                        static_cast<int>(status->runMode),
                        status->currentPlan).c_str());
                }
                return true;
            } else {
                LOG(ERROR, "All connection test attempts failed");
                return false;
            }
        } catch (const std::exception& e) {
            LOG(ERROR, format_string("Exception during testing reconnection: {}", e.what()).c_str());
            return false;
        }
    } catch (const std::exception& e) {
        LOG(ERROR, format_string("Exception during HiSense20999 reconnection: {}", e.what()).c_str());
        return false;
    } catch (...) {
        LOG(ERROR, "Unknown exception during HiSense20999 reconnection");
        return false;
    }
}

std::shared_ptr<gb20999::RunStatusInfo> SignalDeviceHiSense20999::HiSense20999Driver::getStatusInfo() const {
    std::lock_guard<std::mutex> lock(m_statusMutex);
    return m_statusInfo;
}

bool SignalDeviceHiSense20999::HiSense20999Driver::isConnected() const {
    return m_connected;
}

time_t SignalDeviceHiSense20999::HiSense20999Driver::getLastAliveTimestamp() const {
    return m_lastAliveTimestamp;
}

time_t SignalDeviceHiSense20999::HiSense20999Driver::getPhaseStartTimestamp() const {
    return m_phaseStartTimestamp;
}

void SignalDeviceHiSense20999::HiSense20999Driver::setInTimetableMode(bool inTimetable) {
    if (inTimetable && !m_inTimetableMode) {
        // 首次进入timetable模式，记录开始时间
        m_timetableModeStartTime = std::time(nullptr);
    } else if (!inTimetable) {
        // 退出timetable模式，重置开始时间
        m_timetableModeStartTime = 0;
    }

    m_inTimetableMode = inTimetable;
}

bool SignalDeviceHiSense20999::HiSense20999Driver::getInTimetableMode() const {
    return m_inTimetableMode;
}

time_t SignalDeviceHiSense20999::HiSense20999Driver::getTimetableModeStartTime() const {
    return m_timetableModeStartTime;
}

void SignalDeviceHiSense20999::HiSense20999Driver::workerThread() {
    LOG(INFO, "HiSense20999Driver worker thread started");

    // 设置线程中断点
    try {
        while (m_running) {
            // 处理命令队列
            std::pair<std::string, int> cmd;
            bool hasCmd = false;

            // 先检查是否还在运行
            if (!m_running) break;

            {
                std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
                if (!m_cmdQueue.empty()) {
                    cmd = m_cmdQueue.front();
                    m_cmdQueue.pop();
                    hasCmd = true;
                }
            }

            if (hasCmd) {
                if (cmd.first == "exit") {
                    LOG(INFO, "Received exit command, exiting worker thread");
                    break;
                } else if (cmd.first == "setPhase") {
                    try {
                        sendSetPhaseMsg(cmd.second);
                    } catch (const std::exception& e) {
                        LOG(ERROR, format_string("Exception during setPhase: {}", e.what()).c_str());
                    } catch (...) {
                        LOG(ERROR, "Unknown exception during setPhase");
                    }
                } else if (cmd.first == "setControlMode") {
                    try {
                        sendSetControlModeMsg(static_cast<ControlMode>(cmd.second));
                    } catch (const std::exception& e) {
                        LOG(ERROR, format_string("Exception during setControlMode: {}", e.what()).c_str());
                    } catch (...) {
                        LOG(ERROR, "Unknown exception during setControlMode");
                    }
                } else {
                    LOG(WARNING, format_string("Unknown command: {}", cmd.first).c_str());
                }
            }

            // 再次检查是否还在运行
            if (!m_running) break;

            // 定期查询状态
            time_t now = std::time(nullptr);
            if (now - m_lastQueryTime >= m_config.pollingPeriod / 1000) {
                // 检查UDP客户端和执行器是否有效
                if (m_queryExecutor && m_udpClient) {
                    try {
                        sendQueryStateMsg();
                    } catch (const std::exception& e) {
                        LOG(ERROR, format_string("Exception during query state: {}", e.what()).c_str());
                    } catch (...) {
                        LOG(ERROR, "Unknown exception during query state");
                    }
                    m_lastQueryTime = now;
                } else {
                    // 如果无效，可能正在重连中，更新查询时间以避免频繁尝试
                    m_lastQueryTime = now;
                    LOG(DEBUG, "Skipping query state: reconnection may be in progress");
                }
            }

            // 使用更短的睡眠时间，以便更快响应终止请求
            for (int i = 0; i < 10 && m_running; i++) {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    } catch (const std::exception& e) {
        LOG(ERROR, format_string("Exception in worker thread: {}", e.what()).c_str());
    } catch (...) {
        LOG(ERROR, "Unknown exception in worker thread");
    }

    LOG(INFO, "HiSense20999Driver worker thread exited");
}

void SignalDeviceHiSense20999::HiSense20999Driver::sendQueryStateMsg() {
    // 在调用前已经检查了UDP客户端和执行器是否有效，这里再次检查以确保安全
    if (!m_queryExecutor) {
        LOG(WARNING, "Cannot query state: query executor is null");
        m_connected = false;
        return;
    }

    if (!m_udpClient) {
        LOG(WARNING, "Cannot query state: UDP client is null");
        m_connected = false;
        return;
    }

    try {
        LOG(DEBUG, format_string("Querying status from {}:{}", m_config.hostIP, m_config.hostPort).c_str());

        // 执行查询
        LOG(DEBUG, format_string("Executing RunStatusQuery to {}:{}", m_config.hostIP, m_config.hostPort).c_str());

        // 尝试多次查询，提高成功率
        std::optional<gb20999::QueryResult> result;
        for (int retry = 0; retry < 3; ++retry) {
            result = m_queryExecutor->execute<gb20999::RunStatusQuery>();
            if (result.has_value()) {
                LOG(DEBUG, format_string("Query successful on attempt {}", retry + 1).c_str());
                break;
            }
            LOG(DEBUG, format_string("Query attempt {} failed, retrying...", retry + 1).c_str());
            std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 等待一小段时间再重试
        }

        if (result.has_value()) {
            // 更新最后活动时间
            m_lastAliveTimestamp = std::time(nullptr);
            m_connected = true;

            // 更新状态信息
            std::lock_guard<std::mutex> lock(m_statusMutex);
            if (auto* status = std::get_if<gb20999::RunStatusInfo>(&*result)) {
                // 检查相位是否发生变化
                if (m_currentPhase != status->currentPhaseStage) {
                    m_currentPhase = status->currentPhaseStage;
                    m_phaseStartTimestamp = std::time(nullptr);
                    LOG(INFO, format_string("Phase changed to {}, reset phase start timestamp",
                        static_cast<int>(status->currentPhaseStage)).c_str());
                }

                *m_statusInfo = *status;

                // 检查是否处于timetable模式
                bool isTimetable = (status->runMode == gb20999::RunModeData::Mode::LOCAL_FIXCYCLE);
                setInTimetableMode(isTimetable);

                LOG(INFO, format_string("Updated status: phase={}, mode={}, plan={}",
                    static_cast<int>(status->currentPhaseStage),
                    static_cast<int>(status->runMode),
                    static_cast<int>(status->currentPlan)).c_str());
            } else {
                LOG(WARNING, "Query returned a value but it's not a RunStatusInfo");
            }
        } else {
            LOG(WARNING, "Failed to query status: execute returned no value");
            m_connected = false;
        }
    } catch (const std::exception& e) {
        LOG(ERROR, format_string("Exception during query status: {}", e.what()).c_str());
        m_connected = false;
    } catch (...) {
        LOG(ERROR, "Unknown exception during query status");
        m_connected = false;
    }
}

void SignalDeviceHiSense20999::HiSense20999Driver::sendSetPhaseMsg(int phase) {
    if (!m_setterExecutor || !m_udpClient) {
        LOG(DEBUG, "Cannot set phase: executor or UDP client is null, command queued for later execution");
        // 将命令重新加入队列，等待重连后执行
        std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
        m_cmdQueue.push({"setPhase", phase});
        return;
    }

    try {
        LOG(INFO, format_string("Setting phase to {}", phase).c_str());
        auto result = m_setterExecutor->execute<gb20999::PhaseSetter>(m_config.intersectionId, phase);
        if (result.has_value()) {
            LOG(INFO, format_string("Set phase result: {}", result ? "success" : "failure").c_str());
            m_lastSetPhase = phase;
        } else {
            LOG(ERROR, format_string("Failed to set phase {}", phase).c_str());
        }
    } catch (const std::exception& e) {
        LOG(ERROR, format_string("Exception during setting phase: {}", e.what()).c_str());
    } catch (...) {
        LOG(ERROR, "Unknown exception during setting phase");
    }
}

void SignalDeviceHiSense20999::HiSense20999Driver::sendSetControlModeMsg(ControlMode mode) {
    if (!m_setterExecutor || !m_udpClient) {
        LOG(DEBUG, "Cannot set control mode: executor or UDP client is null, command queued for later execution");
        // 将命令重新加入队列，等待重连后执行
        std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
        m_cmdQueue.push({"setControlMode", static_cast<int>(mode)});
        return;
    }

    try {
        LOG(INFO, format_string("Setting control mode to {}", static_cast<int>(mode)).c_str());

        gb20999::ModeSetter::Mode gbMode;
        switch (mode) {
            case ControlMode::MANUAL:
                gbMode = gb20999::ModeSetter::Mode::CENTER_MANUAL;
                break;
            case ControlMode::MONITORING:
                gbMode = gb20999::ModeSetter::Mode::LOCAL_FIXCYCLE;
                sendSetPhaseMsg(0);
                break;
            default:
                LOG(ERROR, format_string("Unsupported control mode: {}", static_cast<int>(mode)).c_str());
                return;
        }

        auto result = m_setterExecutor->execute<gb20999::ModeSetter>(m_config.intersectionId, gbMode);
        if (result.has_value()) {
            LOG(INFO, format_string("Set control mode result: {}", result ? "success" : "failure").c_str());

            // 如果设置为手动模式，需要发送一个相位命令使其生效
            if (mode == ControlMode::MANUAL && m_lastSetPhase > 0) {
                LOG(INFO, "Sending phase command to make manual mode effective");
                sendSetPhaseMsg(m_lastSetPhase);
            }
        } else {
            LOG(ERROR, format_string("Failed to set control mode {}", static_cast<int>(mode)).c_str());
        }
    } catch (const std::exception& e) {
        LOG(ERROR, format_string("Exception during setting control mode: {}", e.what()).c_str());
    } catch (...) {
        LOG(ERROR, "Unknown exception during setting control mode");
    }
}

//=============================================================================
// SignalDeviceHiSense20999 实现
//=============================================================================

SignalDeviceHiSense20999::SignalDeviceHiSense20999(const SignalCtl& signalCtl)
    : m_reconnectThreshold(60)
    , m_timetableModeReconnectThreshold(60)
    , m_lastCheckTime(std::time(nullptr))
{
    try {
        // 初始化驱动配置
        m_driverConfig.hostIP = signalCtl.hostIP;
        m_driverConfig.hostPort = signalCtl.hostPort;
        m_driverConfig.hostId = 0x01;
        m_driverConfig.tscId = 0x01;
        m_driverConfig.intersectionId = 0x01;
        m_driverConfig.timeout = signalCtl.timeout;
        m_driverConfig.pollingPeriod = 1000; // 默认1秒

        // 从JSON中读取更多配置
        try {
            json j = json::parse(signalCtl.rawJson);
            const auto& signalCtl_json = j.value("signalCtl", json::object());

            // 读取轮询周期
            if (signalCtl_json.contains("tscPollingPeriod_ms")) {
                m_driverConfig.pollingPeriod = signalCtl_json["tscPollingPeriod_ms"].get<int>();
            }
        } catch (const std::exception& e) {
            LOG(WARNING, format_string("Failed to parse additional config from JSON: {}", e.what()).c_str());
        }

        // 创建驱动实例
        m_driver = std::make_shared<HiSense20999Driver>(m_driverConfig);

        // 启动驱动
        if (m_driver) {
            m_driver->start();
            LOG(INFO, "HiSense20999 signal device initialized");
        } else {
            LOG(ERROR, "Failed to create HiSense20999 driver");
        }
    } catch (const std::exception& e) {
        LOG(ERROR, format_string("Exception during HiSense20999 initialization: {}", e.what()).c_str());
    } catch (...) {
        LOG(ERROR, "Unknown exception during HiSense20999 initialization");
    }
}

SignalDeviceHiSense20999::~SignalDeviceHiSense20999() {
    try {
        LOG(INFO, "Destroying HiSense20999 signal device");

        // 先尝试将信号机设置为监控模式
        try {
            if (m_driver) {
                m_driver->setControlMode(HiSense20999Driver::ControlMode::MONITORING);
            }
        } catch (...) {
            // 忽略异常，继续清理
        }

        // 停止驱动
        if (m_driver) {
            m_driver->stop();
            m_driver.reset();
        }

        LOG(INFO, "HiSense20999 signal device destroyed");
    } catch (const std::exception& e) {
        LOG(ERROR, format_string("Exception during HiSense20999 destruction: {}", e.what()).c_str());
    } catch (...) {
        LOG(ERROR, "Unknown exception during HiSense20999 destruction");
    }
}

void SignalDeviceHiSense20999::controlSignal(int phase) {
    if (!m_driver) {
        LOG(ERROR, "Cannot control signal: driver is null");
        return;
    }

    DeviceState currentState = deviceState.getCurrentState();
    if (currentState != DeviceState::CONTROLABLE && currentState != DeviceState::TRANSITIONTOCONTROLABLE) {
        LOG(ERROR, "Signal Machine is not controlable, cannot control signal.");
        return;
    }

    LOG(INFO, format_string("Controlling signal to phase {}", phase).c_str());
    m_driver->setPhase(phase);
}

SignalEnvState SignalDeviceHiSense20999::getSignalState() {
    if (!m_driver) {
        LOG(ERROR, "Cannot get signal state: driver is null");
        return {0, 0, 0, false, "-1"};
    }

    // 检查是否需要重连
    time_t now = std::time(nullptr);
    if (m_reconnectThreshold > 0 && now - m_lastCheckTime >= 5) { // 每5秒检查一次
        m_lastCheckTime = now;

        // 检查连接状态
        if (!m_driver->isConnected()) {
            LOG(WARNING, "Signal machine connection lost, attempting reconnection");
            reconnect(3); // 等待3秒后重连
        }

        // 检查timetable模式超时
        if (m_timetableModeReconnectThreshold > 0 && m_driver->getInTimetableMode()) {
            time_t timeInTimetableMode = now - m_driver->getTimetableModeStartTime();
            if (timeInTimetableMode >= m_timetableModeReconnectThreshold) {
                LOG(WARNING, format_string("Signal machine has been in timetable mode for {} seconds, triggering reconnection",
                    timeInTimetableMode).c_str());
                reconnect(3); // 等待3秒后重连
            }
        }
    }

    // 获取状态信息
    auto statusInfo = m_driver->getStatusInfo();
    if (!statusInfo) {
        LOG(ERROR, "Failed to get status info");
        return {0, 0, 0, false, "-1"};
    }

    switch (statusInfo->runMode) {
        case gb20999::RunModeData::Mode::LOCAL_FIXCYCLE:
            if (deviceState.getCurrentState() != DeviceState::TRANSITIONTOCONTROLABLE)
            deviceState.chageCurState(DeviceState::MONITORING);
            break;
        case gb20999::RunModeData::Mode::CENTER_TIMETABLE:
            if (deviceState.getTargetState() != DeviceState::CONTROLABLE)
                deviceState.chageCurState(DeviceState::TRANSITIONTOMONITORING);
            break;
        case gb20999::RunModeData::Mode::CENTER_MANUAL:
            if (deviceState.getCurrentState() != DeviceState::TRANSITIONTOMONITORING)
            deviceState.chageCurState(DeviceState::CONTROLABLE);
            break;
        default:
            deviceState.chageCurState(DeviceState::ERROR);
            break;
    }

    if (statusInfo->currentPhaseStage != deviceState.currentPhase) {
        deviceState.currentPhase = statusInfo->currentPhaseStage;
    }


    // 构造返回值
    SignalEnvState state = {
        getCurrentTimestamp(),
        statusInfo->currentPhaseStage,
        static_cast<int>(std::time(nullptr) - m_driver->getPhaseStartTimestamp()),
        statusInfo->runMode == gb20999::RunModeData::Mode::CENTER_MANUAL,
        std::to_string(statusInfo->currentPlan)
    };

    return state;
}

bool SignalDeviceHiSense20999::reset() {
    if (!m_driver) {
        LOG(ERROR, "Cannot reset: driver is null");
        return false;
    }

    LOG(INFO, "Resetting HiSense20999 signal machine");
    m_driver->setControlMode(HiSense20999Driver::ControlMode::MONITORING);
    deviceState.chageCurState(DeviceState::TRANSITIONTOMONITORING);
    return true;
}

bool SignalDeviceHiSense20999::setControlMode(int mode) {
    if (!m_driver) {
        LOG(ERROR, "Cannot set control mode: driver is null");
        return false;
    }

    if (mode == 1) { // 手动控制模式
        LOG(INFO, "Setting HiSense20999 to manual control mode");
        m_driver->setControlMode(HiSense20999Driver::ControlMode::MANUAL);
        deviceState.chageCurState(DeviceState::TRANSITIONTOCONTROLABLE);
    } else { // 监控模式
        LOG(INFO, "Setting HiSense20999 to monitoring mode");
        m_driver->setControlMode(HiSense20999Driver::ControlMode::MONITORING);
        deviceState.chageCurState(DeviceState::TRANSITIONTOMONITORING);
    }

    return true;
}

bool SignalDeviceHiSense20999::reconnect(int waitSeconds) {
    if (!m_driver) {
        LOG(ERROR, "Cannot reconnect: driver is null");
        return false;
    }

    LOG(INFO, format_string("Reconnecting HiSense20999 signal machine with wait time {} seconds", waitSeconds).c_str());
    bool success = m_driver->reconnect(waitSeconds);

    if (success) {
        LOG(INFO, "Successfully reconnected to HiSense20999 signal machine");
    } else {
        LOG(ERROR, "Failed to reconnect to HiSense20999 signal machine");
    }

    return success;
}

void SignalDeviceHiSense20999::setReconnectThreshold(int seconds) {
    if (seconds < 0) {
        LOG(WARNING, format_string("Invalid reconnect threshold: {}, setting to 0 (disabled)", seconds).c_str());
        m_reconnectThreshold = 0;
    } else {
        m_reconnectThreshold = seconds;
        LOG(INFO, format_string("Set reconnect threshold to {} seconds", seconds).c_str());
    }
}

void SignalDeviceHiSense20999::setTimetableModeReconnectThreshold(int seconds) {
    if (seconds < 0) {
        LOG(WARNING, format_string("Invalid timetable mode reconnect threshold: {}, setting to 0 (disabled)", seconds).c_str());
        m_timetableModeReconnectThreshold = 0;
    } else {
        m_timetableModeReconnectThreshold = seconds;
        LOG(INFO, format_string("Set timetable mode reconnect threshold to {} seconds", seconds).c_str());
    }
}
