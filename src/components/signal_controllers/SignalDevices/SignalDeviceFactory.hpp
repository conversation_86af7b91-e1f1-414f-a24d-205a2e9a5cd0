//
// Created by lth on 2024/10/18.
//

#ifndef SIGNALDEVICEFACTORY_HPP
#define SIGNALDEVICEFACTORY_HPP

#include "ISignalDevice.hpp"
#include "SignalDevice1049.hpp"
#include "SignalDeviceHiSense.hpp"
#include "SignalDeviceHKOPENAPI.hpp"

class SignalDeviceFactory {
public:
    static std::unique_ptr<ISignalDevice> createSignalDevice(const SignalCtl& signalCtl) {
        LOG(INFO, "create SignalDevice {}", signalCtl.protocol);
        switch (signalCtl.protocol) {
            case 1:
                return std::make_unique<SignalDevice1049>(signalCtl);
            case 2:
                return std::make_unique<SignalDeviceHiSense>(signalCtl);
            case 3:
                return std::make_unique<SignalDeviceHKOPENAPI>(signalCtl);
            default:
                throw std::invalid_argument("Unsupported protocol type");
        }
    }
};





#endif //SIGNALDEVICEFACTORY_HPP
