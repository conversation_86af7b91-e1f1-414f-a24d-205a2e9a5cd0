#ifndef SIGNALDEVICECHAINZONE_HPP
#define SIGNALDEVICECHAINZONE_HPP

#include "ISignalDevice.hpp"
#include <memory>
#include <thread>
#include <atomic>
#include <queue>
#include <mutex>
#include <chrono>

namespace chainzone {
    struct Config {
        std::string hostIP;
        int hostPort;
        int timeout;
        int pollingPeriod;
        std::string crossId;
    };

    class UdpClient; // 前向声明

    class ChainzoneDriver {
    public:
        explicit ChainzoneDriver(const Config& config);
        ~ChainzoneDriver();

        void start();
        void stop();
        
        void setPhase(int phase);
        void setControlMode(int mode);
        void sendDate(const std::chrono::system_clock::time_point& time = std::chrono::system_clock::now());
        
        bool isConnected() const;
        std::map<std::string, int> getPhaseInfo() const;

    private:
        void workerThread();
        void sendQueryStateMsg();
        void sendSetPhaseMsg(int phase);
        void sendSetControlModeMsg(int mode);
        void sendDateMsg(const std::chrono::system_clock::time_point& time);
        
        Config m_config;
        std::unique_ptr<UdpClient> m_client;
        
        std::atomic<bool> m_running;
        std::thread m_thread;
        std::queue<std::pair<std::string, int>> m_cmdQueue;
        std::mutex m_cmdQueueMutex;
        
        std::atomic<bool> m_connected;
        std::map<std::string, int> m_phaseInfo;
        mutable std::mutex m_phaseInfoMutex; // 声明为 mutable 以在 const 函数中使用
        int64_t m_lastQueryTime;
        int m_errCount;
    };
}

class SignalDeviceChainzone final : public ISignalDevice {
public:
    explicit SignalDeviceChainzone(const SignalCtl& signalCtl);
    ~SignalDeviceChainzone() override;

    void controlSignal(int phase) override;
    SignalEnvState getSignalState() override;
    bool reset() override;
    bool setControlMode(int mode) override;
    bool reconnect(int waitSeconds) override;
    void setReconnectThreshold(int seconds) override;

private:
    std::shared_ptr<chainzone::ChainzoneDriver> m_driver;
    chainzone::Config m_driverConfig;
    int m_reconnectThreshold;
};

#endif // SIGNALDEVICECHAINZONE_HPP