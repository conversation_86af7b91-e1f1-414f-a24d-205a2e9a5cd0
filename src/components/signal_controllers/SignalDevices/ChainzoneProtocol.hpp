#ifndef CHAINZONEPROTOCOL_HPP
#define CHAINZONEPROTOCOL_HPP

#include <vector>
#include <cstdint>
#include <memory>
#include <chrono>
#include <string>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include "utils/UDPClient.hpp"  // 直接包含而不是前向声明

namespace chainzone {
    class Response;
    class Request;

    // 地址结构
struct Address {
    uint8_t group = 0;
    uint8_t unit = 0;
};

// 数据包结构 - 完全按照TypeScript的JetPacket实现
struct Packet : public std::enable_shared_from_this<Packet> {
    uint16_t dataLen = 0;
    Address sourceAddress;
    Address destinationAddress;
    uint16_t packetSerial = 0;
    uint16_t command = 0;
    uint8_t argLen = 0;
    uint8_t flag = 0;
    std::vector<uint8_t> body;
    
    // 静态工厂方法 - 对应TypeScript的JetPacket.request()
    static std::shared_ptr<Packet> createRequest(std::shared_ptr<Request> req);
    
    // 解析为响应 - 对应TypeScript的packet.response()
    template<typename T>
    std::shared_ptr<T> response(std::shared_ptr<T> res) {
        // 允许响应自定义准备工作
        res->prepare(shared_from_this());
        
        // 解析body数据
        size_t offset = 0;
        if (argLen > 0) {
            unpackArg(res, offset);
        }
        if (dataLen > 0) {
            unpackData(res, offset);
        }
        
        return res;
    }
    
    std::vector<uint8_t> pack() const;
    static std::shared_ptr<Packet> unpack(const std::vector<uint8_t>& buffer);
    
private:
    // 解析参数部分 - 对应TypeScript的unpackArg()
    void unpackArg(std::shared_ptr<Response> response, size_t& offset);
    // 解析数据部分 - 对应TypeScript的unpackData()
    void unpackData(std::shared_ptr<Response> response, size_t& offset);
    
    uint8_t packArg(std::vector<uint8_t>& buffer, std::shared_ptr<Request> req);
    uint16_t packData(std::vector<uint8_t>& buffer, std::shared_ptr<Request> req);
    
    static void writeUint16LE(std::vector<uint8_t>& buffer, uint16_t value);
    static uint16_t readUint16LE(const uint8_t* buffer);
};

// Jet协议封装
class JetPacker {
private:
    static const uint16_t REQ_CODE = 0xA755;
    static const uint16_t RES_CODE = 0xA855;
    static const uint16_t CHECKSUM_MASK = 0xFFFF;
    
public:
    static std::vector<uint8_t> pack(const Packet& packet);
    static std::shared_ptr<Packet> unpack(const std::vector<uint8_t>& buffer);
    
private:
    static uint16_t calculateChecksum(const std::vector<uint8_t>& data);
    static void writeUint16LE(std::vector<uint8_t>& buffer, uint16_t value);
    static uint16_t readUint16LE(const uint8_t* buffer);
};

// 请求基类
class Request {
public:
    virtual ~Request() = default;
    virtual uint16_t getCommand() const = 0;
    virtual std::vector<uint8_t> packData() const = 0;
    
    // 新增：打包参数方法 - 对应TypeScript的packArg()
    virtual void packArg(std::vector<uint8_t>& buffer) const {}
    
    // 新增：准备数据包方法 - 对应TypeScript的prepare()
    virtual void prepare(std::shared_ptr<Packet> packet) const {}
};

// 响应基类 - 添加解析方法
class Response {
public:
    virtual ~Response() = default;
    
    // 解析数据部分 - 对应TypeScript的unpackData()
    virtual void unpackData(const std::vector<uint8_t>& data) = 0;
    
    // 新增：解析参数部分 - 对应TypeScript的unpackArg()
    virtual void unpackArg(const std::vector<uint8_t>& argData, uint8_t argLen) {}
    
    // 新增：准备解析 - 对应TypeScript的prepare()
    virtual void prepare(std::shared_ptr<Packet> packet) {}
};

    // 监控请求
class SigStatusRequest : public Request {
public:
    uint16_t getCommand() const override { return 0x0a06; }
    std::vector<uint8_t> packData() const override;
};

class SigStatusResponse : public Response {
public:
    int sigStatus = 0x08;
    void unpackData(const std::vector<uint8_t>& data) override;
};

// 监控请求
class MonitorRequest : public Request {
public:
    uint16_t getCommand() const override { return 0x0a05; }
    std::vector<uint8_t> packData() const override;
};

// 监控响应
class MonitorResponse : public Response {
public:
    int stepSeconds = 0;
    int dataSize = 0;
    int stepCountdown = 0;
    int phaseIndex = 0;
    int phaseActiveCount = 0;
    int pedLightOffset = 0;
    int stepIndex = 0;
    int syncStatus = 0;
    int syncOffset = 0;
    int phaseCreateDate = 0;
    int phaseFileSum = 0;
    bool selfAdaption = false;
    int selfAdaptionDynamicSeconds = 0;
    std::string phaseFile;
    std::vector<int> lightGroupStatus;
    
    void unpackData(const std::vector<uint8_t>& data) override;
/*
    {
        if (data.size() >= 53) { // 最小需要53字节
            stepSeconds = data[0];            // uint8
            dataSize = data[1];               // uint8
            stepCountdown = data[2];          // uint8
            phaseIndex = data[3];             // uint8
            phaseActiveCount = data[4];       // uint8
            pedLightOffset = data[5];         // uint8
            stepIndex = data[6];              // uint8
            syncStatus = data[7];             // uint8
            syncOffset = data[8] | (data[9] << 8);  // uint16 (little endian)
            // 从BCD解码日期
            uint32_t bcdDate = data[10] | (data[11] << 8) | (data[12] << 16) | (data[13] << 24);
            phaseCreateDate = bcdDate; // 简化处理，实际应进行BCD解码
            phaseFileSum = data[14] | (data[15] << 8); // uint16 (little endian)
            selfAdaption = data[16] != 0;     // uint8转bool
            selfAdaptionDynamicSeconds = data[17]; // uint8
            // 跳过4字节保留字段 (data[18]到data[21])
            // 从偏移量22开始读取25字节的文件名
            if (data.size() >= 47) {
                phaseFile.assign(reinterpret_cast<const char*>(data.data()) + 22, 25);
                // 移除字符串末尾的空字符
                phaseFile.erase(phaseFile.find_last_not_of('\0') + 1);
            }
            // 从偏移量47开始读取灯组状态
            if (data.size() > 47) {
                for (size_t i = 47; i < data.size(); i++) {
                    lightGroupStatus.push_back(data[i]);
                }
            }
        }
    }
*/
};

// 控制模式枚举 - 对应TypeScript的ManualType
enum class ControlMode {
    LAST = 0,           // 上一相位
    NEXT = 1,           // 下一相位
    PAUSE = 2,          // 暂停
    ALL_RED = 3,        // 全红
    YELLOW_BLINK = 4,   // 黄闪
    AUTOMATIC = 0x08,   // 自动模式
    PHASE_0 = 0x09      // 相位0开始 (Phase0-Phase31: 0x09-0x28)
};

// 手动控制请求 - 对应TypeScript的Manual类
class ManualControlRequest : public Request {
private:
    ControlMode m_controlMode;
    int m_timeout;          // 保持时间(分钟)
    bool m_sequentialJump;  // 顺序跳转
    uint8_t m_crossNumber;  // 控制路口号

public:
    explicit ManualControlRequest(ControlMode mode, int timeout = 5, bool sequentialJump = true, uint8_t crossNumber = 0)
        : m_controlMode(mode), m_timeout(timeout), m_sequentialJump(sequentialJump), m_crossNumber(crossNumber) {}

    // 兼容旧接口 - 相位控制
    explicit ManualControlRequest(int phase)
        : m_controlMode(static_cast<ControlMode>(static_cast<int>(ControlMode::PHASE_0) + phase)),
          m_timeout(5), m_sequentialJump(true), m_crossNumber(0) {}

    uint16_t getCommand() const override { return 0x0a02; }  // 使用正确的命令码

    // 重写packArg方法 - 对应TypeScript的packArg()
    void packArg(std::vector<uint8_t>& buffer) const override {
        buffer.push_back(static_cast<uint8_t>(m_controlMode));

        // 处理超时时间
        uint8_t timeoutByte = 0;
        if (m_timeout <= 0) {
            timeoutByte = 0;
        } else {
            int minutes = (m_timeout > 0x7f) ? 0x7f : m_timeout;
            timeoutByte = (1 << 7) | minutes;  // 设置高位并保留分钟数
        }
        buffer.push_back(timeoutByte);
        buffer.push_back(m_crossNumber);
    }

    std::vector<uint8_t> packData() const override {
        // Manual控制使用参数而不是数据部分
        return std::vector<uint8_t>();
    }
};

// 延长当前相位请求 - 对应TypeScript的Extend类
class ExtendPhaseRequest : public Request {
private:
    int m_seconds;

public:
    explicit ExtendPhaseRequest(int seconds) : m_seconds(seconds) {}

    uint16_t getCommand() const override { return 0x0a01; }

    void packArg(std::vector<uint8_t>& buffer) const override {
        buffer.push_back(static_cast<uint8_t>(m_seconds));
        // 填充3个字节
        buffer.push_back(0);
        buffer.push_back(0);
        buffer.push_back(0);
    }

    std::vector<uint8_t> packData() const override {
        return std::vector<uint8_t>();
    }
};

// 早切断当前相位请求 - 对应TypeScript的CutOff类
class CutOffPhaseRequest : public Request {
public:
    CutOffPhaseRequest() {}

    uint16_t getCommand() const override { return 0x0a03; }

    void packArg(std::vector<uint8_t>& buffer) const override {
        // CutOff不需要参数，但保留接口
    }

    std::vector<uint8_t> packData() const override {
        return std::vector<uint8_t>();
    }
};
class ManualControlResponse : public Response {
    public:
        int retCode = 0;
        void unpackData(const std::vector<uint8_t>& data) override;
};

// 读取时间响应
class ReadDateResponse : public Response {
public:
    std::chrono::system_clock::time_point dateTime;
    
    void unpackData(const std::vector<uint8_t>& data) override {
        if (data.size() >= 7) {
            struct tm tm = {};
            tm.tm_year = (data[0] | (data[1] << 8)) - 1900;
            tm.tm_mon = data[2] - 1;
            tm.tm_mday = data[3];
            tm.tm_hour = data[4];
            tm.tm_min = data[5];
            tm.tm_sec = data[6];
            dateTime = std::chrono::system_clock::from_time_t(std::mktime(&tm));
        }
    }
};

// 时间同步请求
class SendDateRequest : public Request {
private:
    std::chrono::system_clock::time_point m_time;
public:
    explicit SendDateRequest(const std::chrono::system_clock::time_point& time) : m_time(time) {}
    uint16_t getCommand() const override { return 0x0003; }
    std::vector<uint8_t> packData() const override {
        auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(
            m_time.time_since_epoch()).count();
        std::vector<uint8_t> data(8);
        for (int i = 0; i < 8; i++) {
            data[i] = (timestamp >> (i * 8)) & 0xFF;
        }
        return data;
    }
};

// UDP 客户端 - 使用utils/UDPClient.hpp
class UdpClient {
public:
    UdpClient(const std::string& host, int port, int timeout);
    ~UdpClient();
    
    bool connect();
    void disconnect();
    bool isConnected() const;
    
    std::shared_ptr<Response> request(std::shared_ptr<Request> req, std::shared_ptr<Response> resp);
    
private:
    std::string m_host;
    int m_port;
    int m_timeout;
    std::unique_ptr<UDPClient> m_udpClient;  // 使用utils中的UDPClient
    bool m_connected;
    
    bool sendPacket(const Packet& packet);
    std::shared_ptr<Packet> receivePacket();
};

// TCP 客户端
class TcpClient {
public:
    TcpClient(const std::string& host, int port, int timeout);
    ~TcpClient();
    
    bool connect();
    void disconnect();
    bool isConnected() const;
    
    std::shared_ptr<Response> request(std::shared_ptr<Request> req, std::shared_ptr<Response> resp);
    
private:
    std::string m_host;
    int m_port;
    int m_timeout;
    int m_socket;
    bool m_connected;
    
    bool sendPacket(const Packet& packet);
    std::shared_ptr<Packet> receivePacket();
};

} // namespace chainzone

#endif // CHAINZONEPROTOCOL_HPP