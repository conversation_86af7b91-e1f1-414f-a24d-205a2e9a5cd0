#include "SignalDeviceChainzone.hpp"
#include "ChainzoneProtocol.hpp"
#include "utils/Logger.hpp"
#include <json.hpp>
#include <thread>
#include <chrono>

using json = nlohmann::json;

namespace chainzone {

// 添加时间获取函数
static int64_t getCurrentTime_int64() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

ChainzoneDriver::ChainzoneDriver(const Config& config) 
    : m_config(config), m_running(false), m_connected(false), 
      m_lastQueryTime(0), m_errCount(0) {
    m_client = std::make_unique<UdpClient>(config.hostIP, config.hostPort, config.timeout);
}

ChainzoneDriver::~ChainzoneDriver() {
    stop();
}

void ChainzoneDriver::start() {
    if (!m_running) {
        m_running = true;
        m_thread = std::thread(&ChainzoneDriver::workerThread, this);
        LOG(INFO, "Chainzone driver started");
    }
}

void ChainzoneDriver::stop() {
    if (m_running) {
        m_running = false;
        if (m_thread.joinable()) {
            m_thread.join();
        }
        if (m_client) {
            m_client->disconnect();
        }
        LOG(INFO, "Chainzone driver stopped");
    }
}

void ChainzoneDriver::setPhase(int phase) {
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"setPhase", phase});
}

void ChainzoneDriver::setControlMode(int mode) {
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"setControlMode", mode});
}

void ChainzoneDriver::sendDate(const std::chrono::system_clock::time_point& time) {
    auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(
        time.time_since_epoch()).count();
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"sendDate", static_cast<int>(timestamp)});
}

void ChainzoneDriver::workerThread() {
    while (m_running) {
        // 处理命令队列
        {
            std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
            while (!m_cmdQueue.empty()) {
                auto cmd = m_cmdQueue.front();
                m_cmdQueue.pop();
                
                if (cmd.first == "exit") {
                    return;
                } else if (cmd.first == "setPhase") {
                    sendSetPhaseMsg(cmd.second);
                } else if (cmd.first == "setControlMode") {
                    sendSetControlModeMsg(cmd.second);
                } else if (cmd.first == "sendDate") {
                    auto timePoint = std::chrono::system_clock::from_time_t(cmd.second);
                    sendDateMsg(timePoint);
                }
            }
        }

        // 定期查询状态 - 修复时间函数调用
        int64_t now = getCurrentTime_int64();
        if (now - m_lastQueryTime >= m_config.pollingPeriod) {
            sendQueryStateMsg();
            m_lastQueryTime = now;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void ChainzoneDriver::sendQueryStateMsg() {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            m_connected = false;
            m_errCount++;
            LOG(ERROR, "Failed to connect to chainzone device");
            return;
        }
        
        auto request = std::make_shared<MonitorRequest>();
        auto response = std::make_shared<MonitorResponse>();
        
        auto result = m_client->request(request, response);
        if (result) {
            std::lock_guard<std::mutex> lock(m_phaseInfoMutex);
            m_phaseInfo["currentPhase"] = response->phaseIndex;
            m_phaseInfo["phaseTime"] = response->stepSeconds;
            m_phaseInfo["stepCountdown"] = response->stepCountdown;
            m_phaseInfo["IsInCtrl"] = 1;
            m_phaseInfo["currentPlan"] = 1;
            
            m_connected = true;
            m_errCount = 0;
            
            LOG(DEBUG, "Chainzone state: phase={}, time={}, countdown={}", 
                response->phaseIndex, response->stepSeconds, response->stepCountdown);
        } else {
            m_connected = false;
            m_errCount++;
            LOG(ERROR, "Failed to query chainzone state");
        }
    } catch (const std::exception& e) {
        m_connected = false;
        m_errCount++;
        LOG(ERROR, "Exception in sendQueryStateMsg: {}", e.what());
    }
}

void ChainzoneDriver::sendSetPhaseMsg(int phase) {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            LOG(ERROR, "Failed to connect to chainzone device for phase control");
            return;
        }
        
        auto request = std::make_shared<ManualControlRequest>(phase);
        auto response = std::make_shared<MonitorResponse>();
        
        auto result = m_client->request(request, response);
        if (result) {
            LOG(INFO, "Successfully set chainzone phase to {}", phase);
        } else {
            LOG(ERROR, "Failed to set chainzone phase to {}", phase);
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in sendSetPhaseMsg: {}", e.what());
    }
}

void ChainzoneDriver::sendSetControlModeMsg(int mode) {
    LOG(INFO, "Setting chainzone control mode to {}", mode);
    // 实现设置控制模式的具体逻辑
}

void ChainzoneDriver::sendDateMsg(const std::chrono::system_clock::time_point& time) {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            LOG(ERROR, "Failed to connect to chainzone device for date sync");
            return;
        }
        
        auto request = std::make_shared<SendDateRequest>(time);
        auto response = std::make_shared<MonitorResponse>();
        
        auto result = m_client->request(request, response);
        if (result) {
            auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(
                time.time_since_epoch()).count();
            LOG(INFO, "Successfully synchronized time with chainzone device: {}", timestamp);
        } else {
            LOG(ERROR, "Failed to synchronize time with chainzone device");
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in sendDateMsg: {}", e.what());
    }
}

bool ChainzoneDriver::isConnected() const {
    return m_connected;
}

// 修复 const 成员函数中的互斥锁问题
std::map<std::string, int> ChainzoneDriver::getPhaseInfo() const {
    std::lock_guard<std::mutex> lock(m_phaseInfoMutex);
    return m_phaseInfo;
}

} // namespace chainzone

// SignalDeviceChainzone 实现
SignalDeviceChainzone::SignalDeviceChainzone(const SignalCtl& signalCtl) 
    : m_reconnectThreshold(30) {
    
    // 配置驱动参数
    m_driverConfig.hostIP = signalCtl.hostIP;
    m_driverConfig.hostPort = signalCtl.hostPort;
    m_driverConfig.timeout = signalCtl.timeout;
    m_driverConfig.pollingPeriod = signalCtl.tscPollingPeriod_ms;
    
    // 从JSON中读取更多配置
    try {
        json j = json::parse(signalCtl.rawJson);
        const auto& signalCtl_json = j.value("signalCtl", json::object());
        
        if (signalCtl_json.contains("crossId")) {
            m_driverConfig.crossId = signalCtl_json["crossId"].get<std::string>();
        }
    } catch (const std::exception& e) {
        LOG(WARNING, "Failed to parse additional config from JSON: {}", e.what());
    }

    // 创建并启动驱动
    m_driver = std::make_shared<chainzone::ChainzoneDriver>(m_driverConfig);
    if (m_driver) {
        m_driver->start();
        LOG(INFO, "Chainzone signal device initialized");
    } else {
        LOG(ERROR, "Failed to create Chainzone driver");
    }
}

SignalDeviceChainzone::~SignalDeviceChainzone() {
    if (m_driver) {
        m_driver->stop();
    }
    LOG(INFO, "Chainzone signal device destroyed");
}

void SignalDeviceChainzone::controlSignal(int phase) {
    if (!m_driver) {
        LOG(ERROR, "Cannot control signal: driver is null");
        return;
    }

    DeviceState currentState = deviceState.getCurrentState();
    if (currentState != DeviceState::CONTROLABLE && currentState != DeviceState::TRANSITIONTOCONTROLABLE) {
        LOG(ERROR, "Signal Machine is not controlable, cannot control signal.");
        return;
    }

    LOG(INFO, "Controlling chainzone signal to phase {}", phase);
    m_driver->setPhase(phase);
}

SignalEnvState SignalDeviceChainzone::getSignalState() {
    if (!m_driver) {
        LOG(ERROR, "Cannot get signal state: driver is null");
        return {0, 0, 0, false, "-1"};
    }

    auto phaseInfo = m_driver->getPhaseInfo();
    
    // 更新设备状态
    if (phaseInfo.find("currentPhase") != phaseInfo.end() && 
        phaseInfo["currentPhase"] != deviceState.currentPhase) {
        deviceState.currentPhase = phaseInfo["currentPhase"];
    }

    // 构造返回值
    SignalEnvState state = {
        getCurrentTimestamp(),
        phaseInfo.count("currentPhase") ? phaseInfo["currentPhase"] : 0,
        phaseInfo.count("phaseTime") ? phaseInfo["phaseTime"] : 0,
        phaseInfo.count("IsInCtrl") ? static_cast<bool>(phaseInfo["IsInCtrl"]) : false,
        phaseInfo.count("currentPlan") ? std::to_string(phaseInfo["currentPlan"]) : "-1"
    };

    return state;
}

bool SignalDeviceChainzone::reset() {
    if (!m_driver) {
        LOG(ERROR, "Cannot reset: driver is null");
        return false;
    }

    LOG(INFO, "Resetting Chainzone signal machine");
    m_driver->setControlMode(0); // 0 表示监控模式
    deviceState.chageCurState(DeviceState::TRANSITIONTOMONITORING);
    return true;
}

bool SignalDeviceChainzone::setControlMode(int mode) {
    if (!m_driver) {
        LOG(ERROR, "Cannot set control mode: driver is null");
        return false;
    }

    LOG(INFO, "Setting Chainzone control mode to {}", mode);
    m_driver->setControlMode(mode);
    return true;
}

bool SignalDeviceChainzone::reconnect(int waitSeconds) {
    if (!m_driver) {
        LOG(ERROR, "Cannot reconnect: driver is null");
        return false;
    }

    LOG(INFO, "Reconnecting Chainzone signal machine, waiting {} seconds", waitSeconds);
    
    // 停止当前驱动
    m_driver->stop();
    
    // 等待指定时间
    std::this_thread::sleep_for(std::chrono::seconds(waitSeconds));
    
    // 重新启动驱动
    m_driver->start();
    
    return m_driver->isConnected();
}

void SignalDeviceChainzone::setReconnectThreshold(int seconds) {
    m_reconnectThreshold = seconds;
    LOG(INFO, "Set Chainzone reconnect threshold to {} seconds", seconds);
}
