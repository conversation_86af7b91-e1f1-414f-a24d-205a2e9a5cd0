#include "SignalDeviceChainzone.hpp"
#include "ChainzoneProtocol.hpp"
#include "utils/Logger.hpp"
#include <json.hpp>
#include <thread>
#include <chrono>

using json = nlohmann::json;

namespace chainzone {

// 添加时间获取函数
static int64_t getCurrentTime_int64() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

ChainzoneDriver::ChainzoneDriver(const Config& config) 
    : m_config(config), m_running(false), m_connected(false), 
      m_lastQueryTime(0), m_errCount(0) {
    m_client = std::make_unique<UdpClient>(config.hostIP, config.hostPort, config.timeout);
}

ChainzoneDriver::~ChainzoneDriver() {
    stop();
}

void ChainzoneDriver::start() {
    if (!m_running) {
        m_running = true;
        m_thread = std::thread(&ChainzoneDriver::workerThread, this);
        LOG(INFO, "Chainzone driver started");
    }
}

void ChainzoneDriver::stop() {
    if (m_running) {
        m_running = false;
        if (m_thread.joinable()) {
            m_thread.join();
        }
        if (m_client) {
            m_client->disconnect();
        }
        LOG(INFO, "Chainzone driver stopped");
    }
}

void ChainzoneDriver::setPhase(int phase) {
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"setPhase", phase});
}

void ChainzoneDriver::setControlMode(int mode) {
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"setControlMode", mode});
}

void ChainzoneDriver::sendDate(const std::chrono::system_clock::time_point& time) {
    auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(
        time.time_since_epoch()).count();
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"sendDate", static_cast<int>(timestamp)});
}

// 新增控制方法实现
void ChainzoneDriver::extendPhase(int seconds) {
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"extendPhase", seconds});
}

void ChainzoneDriver::cutOffPhase() {
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"cutOffPhase", 0});
}

void ChainzoneDriver::nextPhase() {
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"nextPhase", 0});
}

void ChainzoneDriver::previousPhase() {
    std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
    m_cmdQueue.push({"previousPhase", 0});
}

void ChainzoneDriver::setAutomaticMode() {
    setControlMode(0);  // 使用现有的setControlMode方法
}

void ChainzoneDriver::setPauseMode() {
    setControlMode(1);
}

void ChainzoneDriver::setAllRedMode() {
    setControlMode(2);
}

void ChainzoneDriver::setYellowBlinkMode() {
    setControlMode(3);
}

void ChainzoneDriver::workerThread() {
    while (m_running) {
        // 处理命令队列
        {
            std::lock_guard<std::mutex> lock(m_cmdQueueMutex);
            while (!m_cmdQueue.empty()) {
                auto cmd = m_cmdQueue.front();
                m_cmdQueue.pop();
                
                if (cmd.first == "exit") {
                    return;
                } else if (cmd.first == "setPhase") {
                    sendSetPhaseMsg(cmd.second);
                } else if (cmd.first == "setControlMode") {
                    sendSetControlModeMsg(cmd.second);
                } else if (cmd.first == "sendDate") {
                    auto timePoint = std::chrono::system_clock::from_time_t(cmd.second);
                    sendDateMsg(timePoint);
                } else if (cmd.first == "extendPhase") {
                    sendExtendPhaseMsg(cmd.second);
                } else if (cmd.first == "cutOffPhase") {
                    sendCutOffPhaseMsg();
                } else if (cmd.first == "nextPhase") {
                    sendNextPhaseMsg();
                } else if (cmd.first == "previousPhase") {
                    sendPreviousPhaseMsg();
                }
            }
        }

        // 定期查询状态 - 修复时间函数调用
        int64_t now = getCurrentTime_int64();
        if (now - m_lastQueryTime >= m_config.pollingPeriod) {
            sendQueryStateMsg();
            m_lastQueryTime = now;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void ChainzoneDriver::sendQueryStateMsg() {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            m_connected = false;
            m_errCount++;
            LOG(ERROR, "Failed to connect to chainzone device");
            return;
        }

        auto statusReq = std::make_shared<SigStatusRequest>();
        auto statusResp = std::make_shared<SigStatusResponse>();

        auto result = m_client->request(statusReq, statusResp);
        if (result) {
            m_phaseInfo["CtrlMode"] = statusResp->sigStatus;
            //LOG(INFO, "Chainzone state: CtlMode={}",statusResp->sigStatus);
        } else {
            m_connected = false;
            m_errCount++;
            LOG(ERROR, "Failed to query ctl mode state");
        }

        auto request = std::make_shared<MonitorRequest>();
        auto response = std::make_shared<MonitorResponse>();
        
        auto ret = m_client->request(request, response);
        if (ret) {
            std::lock_guard<std::mutex> lock(m_phaseInfoMutex);
            m_phaseInfo["currentPhase"] = response->phaseIndex;
            m_phaseInfo["phaseTime"] = response->stepSeconds;
            m_phaseInfo["stepCountdown"] = response->stepCountdown;
            m_phaseInfo["IsInCtrl"] = 1;
            m_phaseInfo["currentPlan"] = 1;
            
            m_connected = true;
            m_errCount = 0;
            
            LOG(INFO, "Chainzone state: phase={}, time={}, countdown={}, ctlMode={}",
                response->phaseIndex, response->stepSeconds, response->stepCountdown, m_phaseInfo["CtrlMode"]);
        } else {
            m_connected = false;
            m_errCount++;
            LOG(ERROR, "Failed to query chainzone state");
        }
    } catch (const std::exception& e) {
        m_connected = false;
        m_errCount++;
        LOG(ERROR, "Exception in sendQueryStateMsg: {}", e.what());
    }
}

void ChainzoneDriver::sendSetPhaseMsg(int phase) {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            LOG(ERROR, "Failed to connect to chainzone device for phase control");
            return;
        }

        // 使用相位控制模式，相位号从0开始，对应ControlMode::PHASE_0 + phase
        ControlMode phaseMode = static_cast<ControlMode>(static_cast<int>(ControlMode::PHASE_0) + phase);
        auto request = std::make_shared<ManualControlRequest>(phaseMode, 5, true, 0);  // 5分钟超时，顺序跳转，路口号0
        auto response = std::make_shared<MonitorResponse>();

        auto result = m_client->request(request, response);
        if (result) {
            LOG(INFO, "Successfully set chainzone phase to {} (mode: 0x{:02X})", phase, static_cast<int>(phaseMode));
        } else {
            LOG(ERROR, "Failed to set chainzone phase to {}", phase);
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in sendSetPhaseMsg: {}", e.what());
    }
}

void ChainzoneDriver::sendSetControlModeMsg(int mode) {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            LOG(ERROR, "Failed to connect to chainzone device for control mode setting");
            return;
        }

        // 根据模式创建相应的请求
        std::shared_ptr<Request> request;
        std::shared_ptr<Response> response = std::make_shared<MonitorResponse>();

        switch (mode) {
            case 0: // 监控模式 - 自动运行
                request = std::make_shared<ManualControlRequest>(ControlMode::AUTOMATIC);
                LOG(INFO, "Setting chainzone to automatic mode");
                break;
            case 1: // 手动控制模式 - 暂停
                request = std::make_shared<ManualControlRequest>(ControlMode::PAUSE);
                LOG(INFO, "Setting chainzone to manual pause mode");
                break;
            case 2: // 全红模式
                request = std::make_shared<ManualControlRequest>(ControlMode::ALL_RED);
                LOG(INFO, "Setting chainzone to all red mode");
                break;
            case 3: // 黄闪模式
                request = std::make_shared<ManualControlRequest>(ControlMode::YELLOW_BLINK);
                LOG(INFO, "Setting chainzone to yellow blink mode");
                break;
            default:
                LOG(ERROR, "Unsupported control mode: {}", mode);
                return;
        }

        auto result = m_client->request(request, response);
        if (result) {
            LOG(INFO, "Successfully set chainzone control mode to {}", mode);
        } else {
            LOG(ERROR, "Failed to set chainzone control mode to {}", mode);
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in sendSetControlModeMsg: {}", e.what());
    }
}

void ChainzoneDriver::sendDateMsg(const std::chrono::system_clock::time_point& time) {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            LOG(ERROR, "Failed to connect to chainzone device for date sync");
            return;
        }
        
        auto request = std::make_shared<SendDateRequest>(time);
        auto response = std::make_shared<MonitorResponse>();
        
        auto result = m_client->request(request, response);
        if (result) {
            auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(
                time.time_since_epoch()).count();
            LOG(INFO, "Successfully synchronized time with chainzone device: {}", timestamp);
        } else {
            LOG(ERROR, "Failed to synchronize time with chainzone device");
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in sendDateMsg: {}", e.what());
    }
}

// 新增发送消息方法的实现
void ChainzoneDriver::sendExtendPhaseMsg(int seconds) {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            LOG(ERROR, "Failed to connect to chainzone device for phase extension");
            return;
        }

        auto request = std::make_shared<ExtendPhaseRequest>(seconds);
        auto response = std::make_shared<MonitorResponse>();

        auto result = m_client->request(request, response);
        if (result) {
            LOG(INFO, "Successfully extended current phase by {} seconds", seconds);
        } else {
            LOG(ERROR, "Failed to extend current phase by {} seconds", seconds);
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in sendExtendPhaseMsg: {}", e.what());
    }
}

void ChainzoneDriver::sendCutOffPhaseMsg() {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            LOG(ERROR, "Failed to connect to chainzone device for phase cut-off");
            return;
        }

        auto request = std::make_shared<CutOffPhaseRequest>();
        auto response = std::make_shared<MonitorResponse>();

        auto result = m_client->request(request, response);
        if (result) {
            LOG(INFO, "Successfully cut off current phase");
        } else {
            LOG(ERROR, "Failed to cut off current phase");
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in sendCutOffPhaseMsg: {}", e.what());
    }
}

void ChainzoneDriver::sendNextPhaseMsg() {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            LOG(ERROR, "Failed to connect to chainzone device for next phase");
            return;
        }

        auto request = std::make_shared<ManualControlRequest>(ControlMode::NEXT);
        auto response = std::make_shared<MonitorResponse>();

        auto result = m_client->request(request, response);
        if (result) {
            LOG(INFO, "Successfully switched to next phase");
        } else {
            LOG(ERROR, "Failed to switch to next phase");
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in sendNextPhaseMsg: {}", e.what());
    }
}

void ChainzoneDriver::sendPreviousPhaseMsg() {
    try {
        if (!m_client->isConnected() && !m_client->connect()) {
            LOG(ERROR, "Failed to connect to chainzone device for previous phase");
            return;
        }

        auto request = std::make_shared<ManualControlRequest>(ControlMode::LAST);
        auto response = std::make_shared<MonitorResponse>();

        auto result = m_client->request(request, response);
        if (result) {
            LOG(INFO, "Successfully switched to previous phase");
        } else {
            LOG(ERROR, "Failed to switch to previous phase");
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in sendPreviousPhaseMsg: {}", e.what());
    }
}

bool ChainzoneDriver::isConnected() const {
    return m_connected;
}

// 修复 const 成员函数中的互斥锁问题
std::map<std::string, int> ChainzoneDriver::getPhaseInfo() const {
    std::lock_guard<std::mutex> lock(m_phaseInfoMutex);
    return m_phaseInfo;
}

} // namespace chainzone

// SignalDeviceChainzone 实现
SignalDeviceChainzone::SignalDeviceChainzone(const SignalCtl& signalCtl) 
    : m_reconnectThreshold(30) {
    
    // 配置驱动参数
    m_driverConfig.hostIP = signalCtl.hostIP;
    m_driverConfig.hostPort = signalCtl.hostPort;
    m_driverConfig.timeout = signalCtl.timeout;
    m_driverConfig.pollingPeriod = signalCtl.tscPollingPeriod_ms;
    
    // 从JSON中读取更多配置
    try {
        json j = json::parse(signalCtl.rawJson);
        const auto& signalCtl_json = j.value("signalCtl", json::object());
        
        if (signalCtl_json.contains("crossId")) {
            m_driverConfig.crossId = signalCtl_json["crossId"].get<std::string>();
        }
    } catch (const std::exception& e) {
        LOG(WARNING, "Failed to parse additional config from JSON: {}", e.what());
    }

    // 创建并启动驱动
    m_driver = std::make_shared<chainzone::ChainzoneDriver>(m_driverConfig);
    if (m_driver) {
        m_driver->start();
        LOG(INFO, "Chainzone signal device initialized");
    } else {
        LOG(ERROR, "Failed to create Chainzone driver");
    }
}

SignalDeviceChainzone::~SignalDeviceChainzone() {
    if (m_driver) {
        m_driver->stop();
    }
    LOG(INFO, "Chainzone signal device destroyed");
}

void SignalDeviceChainzone::controlSignal(int phase) {
    if (!m_driver) {
        LOG(ERROR, "Cannot control signal: driver is null");
        return;
    }

    DeviceState currentState = deviceState.getCurrentState();
    if (currentState != DeviceState::CONTROLABLE && currentState != DeviceState::TRANSITIONTOCONTROLABLE) {
        LOG(INFO, "Signal Machine does not need change mode.");
    }

    LOG(INFO, "Controlling chainzone signal to phase {}", phase);
    m_driver->setPhase(phase);
}

SignalEnvState SignalDeviceChainzone::getSignalState() {
    if (!m_driver) {
        LOG(ERROR, "Cannot get signal state: driver is null");
        return {0, 0, 0, false, "-1"};
    }

    auto phaseInfo = m_driver->getPhaseInfo();
    
    // 更新设备状态
    if (phaseInfo.find("currentPhase") != phaseInfo.end() && 
        phaseInfo["currentPhase"] != deviceState.currentPhase) {
        deviceState.currentPhase = phaseInfo["currentPhase"];
    }

    // 设置设备状态
    if (phaseInfo.count("CtrlMode") && phaseInfo["CtrlMode"] == static_cast<int>(chainzone::ControlMode::PAUSE)) {
        if (deviceState.getTargetState() != DeviceState::MONITORING) {
            deviceState.chageCurState(DeviceState::CONTROLABLE);
        }
    } else {
        if (deviceState.getTargetState() == DeviceState::CONTROLABLE) {
            deviceState.chageCurState(DeviceState::TRANSITIONTOCONTROLABLE);
        } else {
            deviceState.chageCurState(DeviceState::MONITORING);
        }
    }
    deviceState.currentPhase = phaseInfo["currentPhase"];
    deviceState.currentPhaseTime = phaseInfo["phaseTime"];
    // 构造返回值
    SignalEnvState state = {
        getCurrentTimestamp(),
        phaseInfo.count("currentPhase") ? phaseInfo["currentPhase"] : 0,
        phaseInfo.count("phaseTime") ? phaseInfo["phaseTime"] : 0,
        phaseInfo.count("IsInCtrl") ? static_cast<bool>(phaseInfo["IsInCtrl"]) : false,
        phaseInfo.count("currentPlan") ? std::to_string(phaseInfo["currentPlan"]) : "-1"
    };

    return state;
}

bool SignalDeviceChainzone::reset() {
    if (!m_driver) {
        LOG(ERROR, "Cannot reset: driver is null");
        return false;
    }

    LOG(INFO, "Resetting Chainzone signal machine");
    m_driver->setControlMode(0); // 0 表示监控模式
    deviceState.chageCurState(DeviceState::TRANSITIONTOMONITORING);
    return true;
}

bool SignalDeviceChainzone::setControlMode(int mode) {
    if (!m_driver) {
        LOG(ERROR, "Cannot set control mode: driver is null");
        return false;
    }
    // if u want to cancel manual control
    if (mode == 0) {
        auto phaseInfo = m_driver->getPhaseInfo();
        // 设置设备状态
        if (phaseInfo.count("CtrlMode") && phaseInfo["CtrlMode"] == static_cast<int>(chainzone::ControlMode::PAUSE)) {
            m_driver->setControlMode(0);
            LOG(INFO, "Chainzone set control mode to auto run");
        }
        deviceState.chageCurState(DeviceState::TRANSITIONTOMONITORING);
    } else {
        LOG(INFO, "Chainzone do not need to set MANUAL control mode.");
    }
    return true;

    LOG(INFO, "Setting Chainzone control mode to {}", mode);
    m_driver->setControlMode(mode);
    return true;
}

bool SignalDeviceChainzone::reconnect(int waitSeconds) {
    if (!m_driver) {
        LOG(ERROR, "Cannot reconnect: driver is null");
        return false;
    }

    LOG(INFO, "Reconnecting Chainzone signal machine, waiting {} seconds", waitSeconds);
    
    // 停止当前驱动
    m_driver->stop();
    
    // 等待指定时间
    std::this_thread::sleep_for(std::chrono::seconds(waitSeconds));
    
    // 重新启动驱动
    m_driver->start();
    
    return m_driver->isConnected();
}

void SignalDeviceChainzone::setReconnectThreshold(int seconds) {
    m_reconnectThreshold = seconds;
    LOG(INFO, "Set Chainzone reconnect threshold to {} seconds", seconds);
}
