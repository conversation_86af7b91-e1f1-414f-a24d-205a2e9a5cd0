//
// Created by lth on 2024/10/18.
//

#include "SignalDevice1049.hpp"
#include "utils/Logger.hpp"
#include "cpp-httplib/httplib.h"

SignalDevice1049::SignalDevice1049(const SignalCtl& signalCtl)
    : m_running(false)
    , m_connected(false)
    , m_errCount(0)
    , m_lastQueryTime(0)
    , m_lastPhase(-1) {

    try {
        // 从配置中解析服务器信息
        auto j = nlohmann::json::parse(signalCtl.rawJson);
        const auto& config = j["signalCtl"];

        m_config.serverHost = config.value("serverHost", "127.0.0.1");
        m_config.serverPort = config.value("serverPort", 80);
        m_config.crossId = config.value("crossId", "");
        m_config.timeout = signalCtl.timeout;
        m_config.pollingPeriod = signalCtl.tscPollingPeriod_ms;

        // 初始化HTTP客户端
        m_client = std::make_unique<httplib::Client>(
            m_config.serverHost, m_config.serverPort);
        m_client->set_connection_timeout(m_config.timeout);

        // 启动工作线程
        m_running = true;
        m_thread = std::thread(&SignalDevice1049::workerThread, this);

        LOG(INFO, "SignalDevice1049 constructed for cross {}", m_config.crossId);

    } catch (const std::exception& e) {
        LOG(ERROR, "Failed to create SignalDevice1049: {}", e.what());
        throw;
    }
}

SignalDevice1049::~SignalDevice1049() {
    try {
        // 安全地停止工作线程
        if (m_running) {
            m_running = false;

            // 尝试发送退出命令，但不要在此处阻塞
            try {
                m_cmdQueue.push({"exit", 0});
            } catch (...) {
                // 忽略异常
            }

            // 等待线程结束，设置超时
            if (m_thread.joinable()) {
                // 使用单独的线程来等待，防止阻塞
                std::thread joinThread([this]() {
                    try {
                        if (m_thread.joinable()) {
                            m_thread.join();
                        }
                    } catch (const std::exception& e) {
                        LOG(ERROR, "Exception joining worker thread: {}", e.what());
                    } catch (...) {
                        LOG(ERROR, "Unknown exception joining worker thread");
                    }
                });

                // 给它一个短暂的时间来完成
                if (joinThread.joinable()) {
                    joinThread.detach(); // 允许它在后台继续运行
                }
            }
        }

        // 释放其他资源
        m_client.reset();

        LOG(INFO, "SignalDevice1049 destroyed");
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in SignalDevice1049 destructor: {}", e.what());
    } catch (...) {
        LOG(ERROR, "Unknown exception in SignalDevice1049 destructor");
    }
}

void SignalDevice1049::controlSignal(int phase) {
    m_cmdQueue.push({"setPhase", phase});
}

SignalEnvState SignalDevice1049::getSignalState() {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_currentState;
}

bool SignalDevice1049::reset() {
    m_cmdQueue.push({"setControlMode", 0});  // 0 表示监控模式
    deviceState.chageCurState(DeviceState::TRANSITIONTOMONITORING);
    LOG(INFO, "Reset command sent");
    return true;
}

bool SignalDevice1049::setControlMode(int mode) {
    m_cmdQueue.push({"setControlMode", mode});
    if (mode == 0) {
        deviceState.chageCurState(DeviceState::TRANSITIONTOMONITORING);
    } else {
        deviceState.chageCurState(DeviceState::TRANSITIONTOCONTROLABLE);
    }
    return true;
}

bool SignalDevice1049::reconnect(int waitSeconds) {
    try {
        LOG(INFO, "Disconnecting from SignalDevice1049 for reconnection");

        // 停止当前工作线程
        if (m_running) {
            m_running = false;

            // 尝试发送退出命令
            try {
                m_cmdQueue.push({"exit", 0});
            } catch (...) {
                // 忽略异常
            }

            // 等待线程结束
            if (m_thread.joinable()) {
                m_thread.join();
            }
        }

        LOG(INFO, "Disconnected from SignalDevice1049, waiting {} seconds before reconnecting", waitSeconds);

        // 等待指定的秒数
        if (waitSeconds > 0) {
            std::this_thread::sleep_for(std::chrono::seconds(waitSeconds));
        }

        // 重新创建 HTTP 客户端
        m_client = std::make_unique<httplib::Client>(m_config.serverHost, m_config.serverPort);
        m_client->set_connection_timeout(m_config.timeout);

        // 重新启动工作线程
        m_running = true;
        m_thread = std::thread(&SignalDevice1049::workerThread, this);

        // 重置状态
        m_errCount = 0;
        m_connected = false;
        m_inControlMode = false;
        m_controlModeStartTime = 0;

        LOG(INFO, "Successfully reconnected to SignalDevice1049");
        return true;
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception during SignalDevice1049 reconnection: {}", e.what());
        return false;
    } catch (...) {
        LOG(ERROR, "Unknown exception during SignalDevice1049 reconnection");
        return false;
    }
}

void SignalDevice1049::setReconnectThreshold(int seconds) {
    if (seconds < 0) {
        LOG(WARNING, "Invalid reconnect threshold: {}, setting to 0 (disabled)", seconds);
        m_controlModeReconnectThreshold = 0;
    } else {
        m_controlModeReconnectThreshold = seconds;
        LOG(INFO, "Set control mode reconnect threshold to {} seconds", seconds);
        // 注意：当前没有实现自动重连功能
    }
}

std::string SignalDevice1049::sendToSignalMachine(
    const std::string& path,
    const nlohmann::json& params) const {

    try {
        // 构造查询参数
        httplib::Params queryParams;
        if (params != nullptr) {
            for (const auto& [key, value] : params.items()) {
                if (value.is_string()) {
                    queryParams.emplace(key, value.get<std::string>());
                } else {
                    queryParams.emplace(key, value.dump());
                }
            }
        }

        // 构造完整路径
        std::string url = path;
        if (!queryParams.empty()) {
            url += "?" + httplib::detail::params_to_query_str(queryParams);
        }

        // 发送请求
        auto res = m_client->Get(url.c_str());
        if (res && res->status == 200) {
            return res->body;
        }

        LOG(ERROR, "HTTP request failed: {} {}",
            res ? res->status : 0,
            res ? res->body : "No response");
        return "";

    } catch (const std::exception& e) {
        LOG(ERROR, "Failed to send request: {}", e.what());
        return "";
    }
}

void SignalDevice1049::sendSetPhaseMsg(int phase) {
    if (phase == m_lastPhase) {
        return;
    }

    nlohmann::json params = {
        {"crossId", m_config.crossId},
        {"phase", phase}
    };

    std::string response = sendToSignalMachine("/api/v1/cross/setPhase", params);
    if (!response.empty()) {
        try {
            auto j = nlohmann::json::parse(response);
            if (j["code"] == 200) {
                m_lastPhase = phase;
                LOG(INFO, "Phase {} set successfully", phase);
            }
        } catch (const std::exception& e) {
            LOG(ERROR, "Failed to parse setPhase response: {}", e.what());
        }
    }
}

void SignalDevice1049::sendSetCtrlModeMsg(int mode) {
    nlohmann::json params = {
        {"crossId", m_config.crossId},
        {"mode", mode}
    };

    std::string response = sendToSignalMachine("/api/v1/cross/setControlMode", params);
    if (!response.empty()) {
        try {
            auto j = nlohmann::json::parse(response);
            if (j["code"] == 200) {
                LOG(INFO, "Control mode {} set successfully", mode);
            }
        } catch (const std::exception& e) {
            LOG(ERROR, "Failed to parse setControlMode response: {}", e.what());
        }
    }
}

void SignalDevice1049::sendQueryStateMsg() {
    nlohmann::json params = {
        {"crossId", m_config.crossId}
    };

    std::string response = sendToSignalMachine("/api/v1/cross/getCrossStage", params);
    if (!response.empty()) {
        try {
            auto j = nlohmann::json::parse(response);
            if (j["code"] == 200) {
                m_errCount = 0;
                m_connected = true;
                updatePhaseInfo(j["data"]);
            } else {
                m_errCount++;
            }
        } catch (const std::exception& e) {
            LOG(ERROR, "Failed to parse state query response: {}", e.what());
            m_errCount++;
        }
    } else {
        m_errCount++;
    }

    if (m_errCount >= 3) {
        m_connected = false;
        deviceState.chageCurState(DeviceState::ERROR);
    }
}

void SignalDevice1049::updatePhaseInfo(const nlohmann::json& stateInfo) {
    std::lock_guard<std::mutex> lock(m_mutex);

    try {
        // 解析时间字符串为时间戳
        std::tm tm = {};
        std::istringstream ss(stateInfo["startTime"].get<std::string>());
        ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
        auto timePoint = std::chrono::system_clock::from_time_t(std::mktime(&tm));
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            timePoint.time_since_epoch()).count() / 1000.0;

        m_currentState.timestamp = timestamp;
        m_currentState.currentPhase = stateInfo["currentPhase"].get<int>();
        m_currentState.phaseTime = stateInfo["phaseTime"].get<int>();
        m_currentState.signalCtlStatus = true; // 根据实际情况设置
        m_currentState.currentPlan = stateInfo["currentPlan"].get<std::string>();

    } catch (const std::exception& e) {
        LOG(ERROR, "Failed to update phase info: {}", e.what());
    }
}

void SignalDevice1049::workerThread() {
    while (m_running) {
        // 处理命令队列
        while (!m_cmdQueue.empty()) {
            auto cmd = m_cmdQueue.front();
            m_cmdQueue.pop();

            if (cmd.first == "exit") {
                return;
            } else if (cmd.first == "setPhase") {
                sendSetPhaseMsg(cmd.second);
            } else if (cmd.first == "setControlMode") {
                sendSetCtrlModeMsg(cmd.second);
            }
        }

        // 定期查询状态
        int64_t now = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();

        if (now - m_lastQueryTime >= m_config.pollingPeriod) {
            sendQueryStateMsg();
            m_lastQueryTime = now;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

