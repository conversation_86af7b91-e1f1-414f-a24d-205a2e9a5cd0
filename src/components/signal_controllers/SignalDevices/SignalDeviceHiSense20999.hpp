#ifndef SIGNALDEVICEHISENSE20999_HPP
#define SIGNALDEVICEHISENSE20999_HPP

#include "ISignalDevice.hpp"
#include "utils/UDPClient.hpp"
#include "utils/Logger.hpp"
#include <memory>
#include <thread>
#include <queue>
#include <mutex>
#include <atomic>
#include <optional>
#include <chrono>
#include <map>

#include "GB20999Message.hpp"



/**
 * HiSense 20999信号机设备类 - 使用工作线程模式
 * 这个类是对原有SignalDeviceHiSense的重新实现，采用类似HKopenapi的工作线程模式
 */
class SignalDeviceHiSense20999 final : public ISignalDevice {
public:
    explicit SignalDeviceHiSense20999(const SignalCtl& signalCtl);
    ~SignalDeviceHiSense20999() override;

    // ISignalDevice接口实现
    void controlSignal(int phase) override;
    SignalEnvState getSignalState() override;
    bool reset() override;
    bool setControlMode(int mode) override;
    bool reconnect(int waitSeconds) override;
    void setReconnectThreshold(int seconds) override;

    // 设置信号机在timetable模式下触发重连的时间阈值
    void setTimetableModeReconnectThreshold(int seconds);

private:
    // HiSense20999驱动类 - 内部类
    class HiSense20999Driver {
    public:
        // 配置结构体
        struct Config {
            std::string hostIP;
            int hostPort;
            uint8_t hostId;
            uint32_t tscId;
            uint8_t intersectionId;
            int timeout;
            int pollingPeriod; // 轮询周期(毫秒)
        };

        // 控制模式枚举
        enum ControlMode {
            MONITORING = 0,  // 监控模式
            MANUAL = 1       // 手动控制模式
        };

        explicit HiSense20999Driver(const Config& config);
        ~HiSense20999Driver();

        // 启动和停止驱动
        void start();
        void stop();

        // 控制接口
        void setPhase(int phase);
        void setControlMode(ControlMode mode);

        // 重连接口
        bool reconnect(int waitSeconds);

        // 状态查询接口
        std::shared_ptr<gb20999::RunStatusInfo> getStatusInfo() const;
        bool isConnected() const;

        // 获取最后活动时间
        time_t getLastAliveTimestamp() const;

        // 获取相位开始时间
        time_t getPhaseStartTimestamp() const;

        // 设置和获取timetable模式状态
        void setInTimetableMode(bool inTimetable);
        bool getInTimetableMode() const;
        time_t getTimetableModeStartTime() const;

    private:
        // 内部工作线程函数
        void workerThread();

        // 发送查询和设置命令
        void sendQueryStateMsg();
        void sendSetPhaseMsg(int phase);
        void sendSetControlModeMsg(ControlMode mode);

        // 更新状态信息
        void updateStatusInfo();

        // 检查是否需要重连
        bool checkNeedReconnect();

        // 配置
        Config m_config;

        // 线程控制
        std::atomic<bool> m_running;
        std::thread m_thread;

        // 命令队列
        std::queue<std::pair<std::string, int>> m_cmdQueue;
        std::mutex m_cmdQueueMutex;

        // UDP客户端和执行器
        std::shared_ptr<UDPClient> m_udpClient;
        std::shared_ptr<gb20999::QueryExecutor> m_queryExecutor;
        std::shared_ptr<gb20999::SetterExecutor> m_setterExecutor;

        // 状态信息
        std::shared_ptr<gb20999::RunStatusInfo> m_statusInfo;
        mutable std::mutex m_statusMutex;

        // 状态跟踪
        std::atomic<bool> m_connected;
        std::atomic<time_t> m_lastAliveTimestamp;
        std::atomic<time_t> m_lastQueryTime;
        std::atomic<int> m_lastSetPhase;

        // timetable模式跟踪
        std::atomic<bool> m_inTimetableMode;
        std::atomic<time_t> m_timetableModeStartTime;

        // 相位开始时间跟踪
        std::atomic<time_t> m_phaseStartTimestamp;
        std::atomic<int> m_currentPhase;
    };

    // 驱动实例
    std::shared_ptr<HiSense20999Driver> m_driver;

    // 驱动配置
    HiSense20999Driver::Config m_driverConfig;

    // 重连阈值
    int m_reconnectThreshold;
    int m_timetableModeReconnectThreshold;

    // 上次检查时间
    time_t m_lastCheckTime;
};

#endif // SIGNALDEVICEHISENSE20999_HPP
