//
// Created by lth on 2024/11/14.
//

#include "SignalDeviceHKOPENAPI.hpp"
#include "utils/Logger.hpp"
#include "json.hpp"


SignalDeviceHKOPENAPI::SignalDeviceHKOPENAPI(const SignalCtl &signalCtl) {
    try {
        //XXX: test hkopenapi dry run.
        //char *ret = httpUtil::HTTPUTIL_Post("", "", "", "", 1, nullptr);
        //LOG(INFO, "receive response {}", ret);
        //httpUtil::HTTPUTIL_Free(ret);
        // reread hk signal machine config from signalCtl rawJson
        json j = json::parse(signalCtl.rawJson);
        const auto& signalCtl_json = j.value("signalCtl", json::object());
        hkOpenApiConfig.platformUrl = signalCtl_json.value("platformUrl", "127.0.0.1");
        hkOpenApiConfig.appkey = signalCtl_json.value("appkey", "");
        hkOpenApiConfig.secret = signalCtl_json.value("secret", "");
        hkOpenApiConfig.crossCode  = signalCtl_json.value("crossCode", "");
        hkOpenApiConfig.crossName = signalCtl_json.value("crossName", "");
        hkOpenApiConfig.userID = signalCtl_json.value("userID", "");
        hkOpenApiConfig.timeout = signalCtl_json.value("timeout", 10);
        hkOpenApiConfig.currentPlan = signalCtl_json.value("defaultPlan", 1);

        hkOpenApiConfig.setlockflow_APISubURL = signalCtl_json.value("setlockflow_APISubURL",
                                                                     "/artemis/api/itscms-scms/v1/signalcontrol/setLockFlow");
        hkOpenApiConfig.SetSignalControl_APISubURL = signalCtl_json.value("SetSignalControl_APISubURL",
                                                                     "/artemis/api/itscms-scms/v1/signalcontrol/SetSignalControl");
        hkOpenApiConfig.getRealtimeSchemeStateInfo_APISubURL = signalCtl_json.value("getRealtimeSchemeStateInfo_APISubURL",
                                                                     "/artemis/api/itscms-scms/v1/crossstate/getRealtimeSchemeStateInfo");

        hkOpenApiConfig.schemes = signalCtl_json.value("schemes", std::vector<std::vector<int> >());
        // 解析lockPhaseChannelMap
        if (signalCtl_json.contains("lockPhaseChannelMap")) {
            const auto& mapJson = signalCtl_json["lockPhaseChannelMap"];
            for (auto it = mapJson.begin(); it != mapJson.end(); ++it) {
                // 将字符串key转换为整数
                int key = std::stoi(it.key());
                // 直接获取值数组
                std::vector<int> value = it.value();
                hkOpenApiConfig.lockPhaseChannelMap[key] = value;
            }
        }

        // 解析协议字段名配置
        if (signalCtl_json.contains("fieldNames")) {
            const auto& fieldNamesJson = signalCtl_json["fieldNames"];

            // 解析lockFlow字段名
            if (fieldNamesJson.contains("lockFlow")) {
                const auto& lockFlowJson = fieldNamesJson["lockFlow"];
                hkOpenApiConfig.fieldNames.lockFlow.crossCode =
                    lockFlowJson.value("crossCode", "crossCode");
                hkOpenApiConfig.fieldNames.lockFlow.operaType =
                    lockFlowJson.value("operaType", "operaType");
                hkOpenApiConfig.fieldNames.lockFlow.channelType =
                    lockFlowJson.value("channelType", "channelType");
                hkOpenApiConfig.fieldNames.lockFlow.transitTime =
                    lockFlowJson.value("transitTime", "transitTime");
                hkOpenApiConfig.fieldNames.lockFlow.channelList2 =
                    lockFlowJson.value("channelList2", "channelList2");
                hkOpenApiConfig.fieldNames.lockFlow.channels =
                    lockFlowJson.value("channels", "channels");
                hkOpenApiConfig.fieldNames.lockFlow.lockTime =
                    lockFlowJson.value("lockTime", "lockTime");
                hkOpenApiConfig.fieldNames.lockFlow.userId =
                    lockFlowJson.value("userId", "userId");
            }

            // 解析signalControl字段名
            if (fieldNamesJson.contains("signalControl")) {
                const auto& signalControlJson = fieldNamesJson["signalControl"];
                hkOpenApiConfig.fieldNames.signalControl.userId =
                    signalControlJson.value("userId", "userId");
                hkOpenApiConfig.fieldNames.signalControl.crossCode =
                    signalControlJson.value("crossCode", "crossCode");
                hkOpenApiConfig.fieldNames.signalControl.controlType =
                    signalControlJson.value("controlType", "controlType");
                hkOpenApiConfig.fieldNames.signalControl.controlNo =
                    signalControlJson.value("controlNo", "controlNo");
                hkOpenApiConfig.fieldNames.signalControl.duration =
                    signalControlJson.value("duration", "duration");
            }

            // 解析queryState字段名
            if (fieldNamesJson.contains("queryState")) {
                const auto& queryStateJson = fieldNamesJson["queryState"];
                hkOpenApiConfig.fieldNames.queryState.pageSize =
                    queryStateJson.value("pageSize", "pageSize");
                hkOpenApiConfig.fieldNames.queryState.pageNo =
                    queryStateJson.value("pageNo", "pageNo");
                hkOpenApiConfig.fieldNames.queryState.searchObj =
                    queryStateJson.value("searchObj", "searchObj");
                hkOpenApiConfig.fieldNames.queryState.crossCodes =
                    queryStateJson.value("crossCodes", "crossCodes");
            }

            // 解析response字段名
            if (fieldNamesJson.contains("response")) {
                const auto& responseJson = fieldNamesJson["response"];
                hkOpenApiConfig.fieldNames.response.code =
                    responseJson.value("code", "code");
                hkOpenApiConfig.fieldNames.response.data =
                    responseJson.value("data", "data");
                hkOpenApiConfig.fieldNames.response.list =
                    responseJson.value("list", "list");
                hkOpenApiConfig.fieldNames.response.controlType =
                    responseJson.value("controlType", "controlType");
                hkOpenApiConfig.fieldNames.response.lockPhases =
                    responseJson.value("lockPhases", "lockPhases");
                hkOpenApiConfig.fieldNames.response.rings =
                    responseJson.value("rings", "rings");
                hkOpenApiConfig.fieldNames.response.phaseList =
                    responseJson.value("phaseList", "phaseList");
                hkOpenApiConfig.fieldNames.response.channelState =
                    responseJson.value("channelState", "channelState");
                hkOpenApiConfig.fieldNames.response.patternNo =
                    responseJson.value("patternNo", "patternNo");
                hkOpenApiConfig.fieldNames.response.runTime =
                    responseJson.value("runTime", "runTime");
                hkOpenApiConfig.fieldNames.response.phaseLength =
                    responseJson.value("phaseLength", "phaseLength");
                hkOpenApiConfig.fieldNames.response.phaseNo =
                    responseJson.value("phaseNo", "phaseNo");
                hkOpenApiConfig.fieldNames.response.state =
                    responseJson.value("state", "state");
                hkOpenApiConfig.fieldNames.response.channelNo =
                    responseJson.value("channelNo", "channelNo");
            }
        }
        hkOpenApiDriver = std::make_shared<HKOpenApi::HKOpenApiDriver>(hkOpenApiConfig);

        if (hkOpenApiDriver != nullptr) {
            hkOpenApiDriver->start();
        }
        LOG(INFO, "HKOPENAPI Contructed.");
    } catch (const std::exception &e) {
        LOG(ERROR, "Create SignalDevice HKOPENAPI exception {}", e.what());
    }
}

SignalDeviceHKOPENAPI::~SignalDeviceHKOPENAPI() {
    hkOpenApiDriver->stop();
    LOG(INFO, "HKOPENAPI destructor");
}

void SignalDeviceHKOPENAPI::controlSignal(int phase) {
    hkOpenApiDriver->setPhase(phase);
}

SignalEnvState SignalDeviceHKOPENAPI::getSignalState() {
    try {
        std::map<std::string, int> phaseInfo = hkOpenApiDriver->getPhaseInfo();
        // 构造符合 SignalEnvState 格式的 JSON
        json j = {
            {"timestamp", getCurrentTimestamp()}, // 当前时间戳
            {"currentPhase", phaseInfo["currentPhase"]},
            {"phaseTime", phaseInfo["phaseTime"]},
            {"signalCtlStatus", static_cast<bool>(phaseInfo["IsInCtrl"])},
            {"currentPlan", std::to_string(phaseInfo["currentPlan"])}
        };

        // 设置设备状态
        if (phaseInfo["CtrlMode"] == HKOpenApi::HKOpenApiDriver::ControlMode::STEP_CONTROL ||
            phaseInfo["CtrlMode"] == HKOpenApi::HKOpenApiDriver::ControlMode::LOCK_STATE) {
            if (deviceState.getTargetState() != DeviceState::MONITORING) {
                deviceState.chageCurState(DeviceState::CONTROLABLE);
            }
        } else {
            if (deviceState.getTargetState() == DeviceState::CONTROLABLE) {
                deviceState.chageCurState(DeviceState::TRANSITIONTOCONTROLABLE);
            } else {
                deviceState.chageCurState(DeviceState::MONITORING);
            }
        }
        deviceState.currentPhase = phaseInfo["currentPhase"];
        deviceState.currentPhaseTime = phaseInfo["phaseTime"];
        return SignalEnvState::fromJson(j); // 使用静态方法创建对象
    } catch (const std::exception &e) {
        LOG(ERROR, "get signal machine states exception {}", e.what());
    }
    return SignalEnvState();
}

bool SignalDeviceHKOPENAPI::reset() {
    if (hkOpenApiDriver != nullptr && deviceState.getCurrentState() == DeviceState::CONTROLABLE) {
        setControlMode(0);
        LOG(INFO, "HKOPENAPI reset command send.");
    }
    return true;
}

bool SignalDeviceHKOPENAPI::setControlMode(int mode) {
    // if u want to cancel manual control
    if (mode == 0) {
        if (hkOpenApiDriver->getPhaseCtrlMode() == HKOpenApi::HKOpenApiDriver::ControlMode::STEP_CONTROL) {
            hkOpenApiDriver->setControlMode(HKOpenApi::HKOpenApiDriver::ControlMode::CANCEL_STEP);
            LOG(INFO, "HKOPENAPI set control mode to CANCEL_STEP");
        } else if (hkOpenApiDriver->getPhaseCtrlMode() == HKOpenApi::HKOpenApiDriver::ControlMode::LOCK_STATE) {
            hkOpenApiDriver->setControlMode(HKOpenApi::HKOpenApiDriver::ControlMode::UNLOCK_STATE);
            LOG(INFO, "HKOPENAPI set control mode to UNLOCK_STATE");
        }
        deviceState.chageCurState(DeviceState::TRANSITIONTOMONITORING);
    } else {
        LOG(INFO, "HKOPENAPI do not need to set MANUAL control mode.");
    }
    return true;
}

bool SignalDeviceHKOPENAPI::reconnect(int waitSeconds) {
    try {
        LOG(INFO, "Disconnecting from HKOPENAPI signal machine for reconnection");

        // 停止当前驱动
        if (hkOpenApiDriver) {
            hkOpenApiDriver->stop();
        }

        LOG(INFO, "Disconnected from HKOPENAPI signal machine, waiting {} seconds before reconnecting", waitSeconds);

        // 等待指定的秒数
        if (waitSeconds > 0) {
            std::this_thread::sleep_for(std::chrono::seconds(waitSeconds));
        }

        // 重新创建驱动
        hkOpenApiDriver = std::make_shared<HKOpenApi::HKOpenApiDriver>(hkOpenApiConfig);

        if (hkOpenApiDriver != nullptr) {
            hkOpenApiDriver->start();
            LOG(INFO, "Successfully reconnected to HKOPENAPI signal machine");
            return true;
        } else {
            LOG(ERROR, "Failed to reconnect to HKOPENAPI signal machine");
            return false;
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception during HKOPENAPI reconnection: {}", e.what());
        return false;
    } catch (...) {
        LOG(ERROR, "Unknown exception during HKOPENAPI reconnection");
        return false;
    }
}

void SignalDeviceHKOPENAPI::setReconnectThreshold(int seconds) {
    if (seconds < 0) {
        LOG(WARNING, "Invalid reconnect threshold: {}, setting to 0 (disabled)", seconds);
        stepControlModeReconnectThreshold = 0;
    } else {
        stepControlModeReconnectThreshold = seconds;
        LOG(INFO, "Set step control mode reconnect threshold to {} seconds", seconds);
        // 注意：当前没有实现自动重连功能
    }
}