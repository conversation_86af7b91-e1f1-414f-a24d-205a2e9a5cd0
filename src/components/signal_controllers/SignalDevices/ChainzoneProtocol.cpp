#include "ChainzoneProtocol.hpp"
#include "utils/Logger.hpp"
#include "utils/UDPClient.hpp"
#include <cstring>
#include <ctime>
#include <errno.h>

namespace chainzone {

// Packet 实现 - 修正协议格式
std::vector<uint8_t> Packet::pack() const {
    std::vector<uint8_t> buffer;
    buffer.reserve(16 + body.size());
    
    // 添加数据长度 (2 bytes, little endian)
    uint16_t totalLen = 12 + body.size();  // 固定头部12字节 + 数据
    buffer.push_back(totalLen & 0xFF);
    buffer.push_back((totalLen >> 8) & 0xFF);
    
    // 添加源地址 (2 bytes)
    buffer.push_back(0x01);  // 源组
    buffer.push_back(0x01);  // 源单元
    
    // 添加目标地址 (2 bytes)
    buffer.push_back(0x01);  // 目标组
    buffer.push_back(0x01);  // 目标单元
    
    // 添加包序号 (2 bytes, little endian)
    buffer.push_back(0x01);
    buffer.push_back(0x00);
    
    // 添加命令 (2 bytes, big endian)
    buffer.push_back((command >> 8) & 0xFF);
    buffer.push_back(command & 0xFF);
    
    // 添加参数长度和标志 (2 bytes)
    buffer.push_back(body.size() & 0xFF);
    buffer.push_back(0x00);  // 标志
    
    // 添加数据
    buffer.insert(buffer.end(), body.begin(), body.end());
    
    return buffer;
}

std::shared_ptr<Packet> Packet::unpack(const std::vector<uint8_t>& buffer) {
    if (buffer.size() < 12) {
        return nullptr;
    }
    
    auto packet = std::make_shared<Packet>();
    
    // 解析数据长度 (2 bytes, little endian)
    uint16_t totalLen = buffer[0] | (buffer[1] << 8);
    
    // 跳过地址和序号 (6 bytes)
    
    // 解析命令 (2 bytes, big endian)
    packet->command = (buffer[8] << 8) | buffer[9];
    
    // 解析参数长度
    uint8_t argLen = buffer[10];
    
    // 解析数据
    if (buffer.size() > 12 && argLen > 0) {
        packet->body.assign(buffer.begin() + 12, buffer.begin() + 12 + argLen);
    }
    
    return packet;
}

// 静态工厂方法 - 对应TypeScript的JetPacket.request()
std::shared_ptr<Packet> Packet::createRequest(std::shared_ptr<Request> req) {
    auto packet = std::make_shared<Packet>();
    
    // 设置命令
    packet->command = req->getCommand();
    
    // 动态打包参数和数据
    std::vector<uint8_t> buffer;
    packet->argLen = packet->packArg(buffer, req);
    packet->dataLen = packet->packData(buffer, req);
    packet->body = buffer;
    
    // 设置默认值 - 可以通过req->prepare()方法自定义
    packet->packetSerial = 1; // 可以实现序号自增
    packet->sourceAddress = {0x00, 0x00};
    packet->destinationAddress = {0x00, 0x00};
    packet->flag = 0x00;
    
    return packet;
}

uint8_t Packet::packArg(std::vector<uint8_t>& buffer, std::shared_ptr<Request> req) {
    size_t startPos = buffer.size();
    
    // 调用请求的packArg方法
    req->packArg(buffer);
    
    size_t byteCount = buffer.size() - startPos;
    
    // 按4字节对齐 - 对应TypeScript的实现
    size_t preferByteCount = ((byteCount + 3) / 4) * 4;
    if (preferByteCount > byteCount) {
        // 填充0字节
        buffer.resize(buffer.size() + (preferByteCount - byteCount), 0);
    }
    
    return preferByteCount / 4; // 返回4字节单位的长度
}

uint16_t Packet::packData(std::vector<uint8_t>& buffer, std::shared_ptr<Request> req) {
    size_t startPos = buffer.size();
    
    // 调用请求的packData方法
    auto data = req->packData();
    buffer.insert(buffer.end(), data.begin(), data.end());
    
    return buffer.size() - startPos;
}

void Packet::unpackArg(std::shared_ptr<Response> response, size_t& offset) {
    if (argLen == 0 || offset >= body.size()) return;
    
    // 按4字节对齐计算参数数据长度 - 对应TypeScript实现
    size_t byteCount = argLen * 4;
    
    if (offset + byteCount > body.size()) {
        LOG(ERROR, "Invalid arg data size: offset={}, byteCount={}, body.size={}", 
            offset, byteCount, body.size());
        return;
    }
    
    // 提取参数数据
    std::vector<uint8_t> argData(body.begin() + offset, body.begin() + offset + byteCount);
    
    // 调用响应的unpackArg方法
    response->unpackArg(argData, argLen);
    
    offset += byteCount;
}

void Packet::unpackData(std::shared_ptr<Response> response, size_t& offset) {
    if (dataLen == 0 || offset >= body.size()) return;
    
    // 提取数据部分
    std::vector<uint8_t> data(body.begin() + offset, body.end());
    
    // 调用响应的unpackData方法
    response->unpackData(data);
    
    offset += data.size();
}

// TcpClient 实现
TcpClient::TcpClient(const std::string& host, int port, int timeout)
    : m_host(host), m_port(port), m_timeout(timeout), m_socket(-1), m_connected(false) {
}

TcpClient::~TcpClient() {
    disconnect();
}

bool TcpClient::connect() {
    if (m_connected) return true;
    
    m_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (m_socket < 0) {
        LOG(ERROR, "Failed to create TCP socket");
        return false;
    }
    
    // 设置超时
    struct timeval tv;
    tv.tv_sec = m_timeout / 1000;
    tv.tv_usec = (m_timeout % 1000) * 1000;
    setsockopt(m_socket, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv));
    setsockopt(m_socket, SOL_SOCKET, SO_SNDTIMEO, &tv, sizeof(tv));
    
    // 连接服务器
    struct sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_port = htons(m_port);
    inet_pton(AF_INET, m_host.c_str(), &addr.sin_addr);
    
    if (::connect(m_socket, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        LOG(ERROR, "Failed to connect to {}:{}", m_host, m_port);
        close(m_socket);
        m_socket = -1;
        return false;
    }
    
    m_connected = true;
    return true;
}

void TcpClient::disconnect() {
    if (m_socket >= 0) {
        close(m_socket);
        m_socket = -1;
    }
    m_connected = false;
}

bool TcpClient::isConnected() const {
    return m_connected;
}

std::shared_ptr<Response> TcpClient::request(std::shared_ptr<Request> req, std::shared_ptr<Response> resp) {
    if (!m_connected && !connect()) {
        return nullptr;
    }
    
    // 创建请求包
    Packet reqPacket;
    reqPacket.command = req->getCommand();
    reqPacket.body = req->packData();  // 使用 body 而不是 data
    
    // 发送请求
    if (!sendPacket(reqPacket)) {
        LOG(ERROR, "Failed to send TCP packet");
        return nullptr;
    }
    
    // 接收响应
    auto respPacket = receivePacket();
    if (!respPacket) {
        LOG(ERROR, "Failed to receive TCP packet");
        return nullptr;
    }
    
    // 解析响应
    resp->unpackData(respPacket->body);  // 使用 body 而不是 data
    return resp;
}

bool TcpClient::sendPacket(const Packet& packet) {
    auto buffer = packet.pack();
    ssize_t sent = send(m_socket, buffer.data(), buffer.size(), 0);
    return sent == static_cast<ssize_t>(buffer.size());
}

std::shared_ptr<Packet> TcpClient::receivePacket() {
    std::vector<uint8_t> buffer(1024);
    ssize_t received = recv(m_socket, buffer.data(), buffer.size(), 0);
    
    if (received <= 0) {
        return nullptr;
    }
    
    buffer.resize(received);
    return Packet::unpack(buffer);
}

// UdpClient 实现 - 使用utils/UDPClient
UdpClient::UdpClient(const std::string& host, int port, int timeout)
    : m_host(host), m_port(port), m_timeout(timeout), m_connected(false) {
    m_udpClient = std::make_unique<UDPClient>(host, port);
}

UdpClient::~UdpClient() {
    disconnect();
}

bool UdpClient::connect() {
    if (m_connected) return true;
    
    LOG(INFO, "UDP client connecting to {}:{}", m_host, m_port);
    m_connected = true;
    return true;
}

void UdpClient::disconnect() {
    m_connected = false;
}

bool UdpClient::isConnected() const {
    return m_connected;
}

std::shared_ptr<Response> UdpClient::request(std::shared_ptr<Request> req, std::shared_ptr<Response> resp) {
    if (!m_connected && !connect()) {
        LOG(ERROR, "UDP client failed to connect to {}:{}", m_host, m_port);
        return nullptr;
    }
    
    LOG(DEBUG, "Sending UDP request to {}:{}", m_host, m_port);
    
    // 使用动态协议处理
    auto reqPacket = Packet::createRequest(req);
    req->prepare(reqPacket);
    
    std::cout << "构造的数据包信息:" << std::endl;
    std::cout << "  命令: 0x" << std::hex << reqPacket->command << std::dec << std::endl;
    std::cout << "  数据长度: " << reqPacket->dataLen << std::endl;
    std::cout << "  参数长度: " << (int)reqPacket->argLen << std::endl;
    
    // 发送请求
    if (!sendPacket(*reqPacket)) {
        LOG(ERROR, "Failed to send UDP packet to {}:{}", m_host, m_port);
        return nullptr;
    }
    
    LOG(DEBUG, "Waiting for UDP response from {}:{}", m_host, m_port);
    
    // 接收响应
    auto respPacket = receivePacket();
    if (!respPacket) {
        LOG(ERROR, "Failed to receive UDP response from {}:{}", m_host, m_port);
        return nullptr;
    }
    
    LOG(DEBUG, "Received UDP response with command: 0x{:04X}, data size: {}", 
        respPacket->command, respPacket->body.size());
    
    // 使用动态解析 - 对应TypeScript的packet.response()
    try {
        return respPacket->response(resp);
    } catch (const std::exception& e) {
        LOG(ERROR, "Failed to parse UDP response: {}", e.what());
        return nullptr;
    }
}

bool UdpClient::sendPacket(const Packet& packet) {
    // 使用Jet协议封装
    auto buffer = JetPacker::pack(packet);
    
    LOG(DEBUG, "Sending Jet packet: command=0x{:04X}, size={}", packet.command, buffer.size());
    
    // 打印完整的发送报文
    std::string hexData;
    for (size_t i = 0; i < buffer.size(); i++) {
        char buf[4];
        snprintf(buf, sizeof(buf), "%02X", buffer[i]);
        hexData += buf;
    }
    LOG(INFO, "Sending packet data: {}", hexData);
    
    // 也在控制台直接输出，确保能看到
    std::cout << "发送报文: " << hexData << std::endl;
    
    return m_udpClient->sendData(buffer.data(), buffer.size());
}

std::shared_ptr<Packet> UdpClient::receivePacket() {
    auto response = m_udpClient->receiveMessage(m_timeout);
    if (!response)  {
        LOG(DEBUG, "No UDP response received within timeout");
        return nullptr;
    }
    
    LOG(DEBUG, "Received raw data: size={}", response->size());
    
    std::string hexData;
    for (size_t i = 0; i < response->size() && i < 32; i++) {
        char buf[4];
        snprintf(buf, sizeof(buf), "%02X ", (unsigned char)(*response)[i]);
        hexData += buf;
    }
    LOG(DEBUG, "Raw data: {}", hexData);
    
    std::vector<uint8_t> buffer(response->begin(), response->end());
    return JetPacker::unpack(buffer);
}

// Monitor命令实现 - 动态协议处理
std::vector<uint8_t> MonitorRequest::packData() const {
    // Monitor命令通常没有数据部分
    return std::vector<uint8_t>();
}

// 如果Monitor需要特殊的参数或地址设置，可以重写prepare方法
//void MonitorRequest::prepare(std::shared_ptr<Packet> packet) const {
    // 可以在这里设置特定的地址或其他参数
    // packet->sourceAddress = {0x01, 0x01};
    // packet->destinationAddress = {0x01, 0x01};
//}

void MonitorResponse::unpackData(const std::vector<uint8_t>& data) {
    // 根据TypeScript实现解析Monitor响应
    if (data.size() >= 53) { // 最小需要53字节
        stepSeconds = data[0];            // uint8
        dataSize = data[1];               // uint8
        stepCountdown = data[2];          // uint8
        phaseIndex = data[3];             // uint8
        phaseActiveCount = data[4];       // uint8
        pedLightOffset = data[5];         // uint8
        stepIndex = data[6];              // uint8
        syncStatus = data[7];             // uint8
        syncOffset = data[8] | (data[9] << 8);  // uint16 (little endian)
        // 从BCD解码日期
        uint32_t bcdDate = data[10] | (data[11] << 8) | (data[12] << 16) | (data[13] << 24);
        phaseCreateDate = bcdDate; // 简化处理，实际应进行BCD解码
        phaseFileSum = data[14] | (data[15] << 8); // uint16 (little endian)
        selfAdaption = data[16] != 0;     // uint8转bool
        selfAdaptionDynamicSeconds = data[17]; // uint8
        // 跳过4字节保留字段 (data[18]到data[21])
        // 从偏移量22开始读取25字节的文件名
        if (data.size() >= 47) {
            phaseFile.assign(reinterpret_cast<const char*>(data.data()) + 22, 25);
            // 移除字符串末尾的空字符
            phaseFile.erase(phaseFile.find_last_not_of('\0') + 1);
        }
        // 从偏移量47开始读取灯组状态
        if (data.size() > 47) {
            for (size_t i = 47; i < data.size(); i++) {
                lightGroupStatus.push_back(data[i]);
            }
        }
    }
}

// Jet协议封装实现
void JetPacker::writeUint16LE(std::vector<uint8_t>& buffer, uint16_t value) {
    buffer.push_back(value & 0xFF);
    buffer.push_back((value >> 8) & 0xFF);
}

uint16_t JetPacker::readUint16LE(const uint8_t* buffer) {
    return buffer[0] | (buffer[1] << 8);
}

uint16_t JetPacker::calculateChecksum(const std::vector<uint8_t>& data) {
    uint32_t sum = 0;
    for (uint8_t byte : data) {
        sum += byte;
    }
    return sum & CHECKSUM_MASK;
}

std::vector<uint8_t> JetPacker::pack(const Packet& packet) {
    std::vector<uint8_t> buffer;
    
    // 1. 添加请求标识 (小端序)
    writeUint16LE(buffer, REQ_CODE);
    
    // 2. 预留校验和位置
    size_t checksumPos = buffer.size();
    writeUint16LE(buffer, 0);
    
    // 3. 打包JetPacket数据
    size_t packetStart = buffer.size();
    
    // JetPacket结构 (完全按照TypeScript实现)
    writeUint16LE(buffer, packet.dataLen);           // 数据长度
    buffer.push_back(packet.sourceAddress.group);    // 源地址组
    buffer.push_back(packet.sourceAddress.unit);     // 源地址单元  
    buffer.push_back(packet.destinationAddress.group); // 目标地址组
    buffer.push_back(packet.destinationAddress.unit);  // 目标地址单元
    writeUint16LE(buffer, packet.packetSerial);      // 包序列号
    
    // 命令字段 - 按照TypeScript的字节序 (大端序)
    buffer.push_back((packet.command >> 8) & 0xFF);  // 高字节在前
    buffer.push_back(packet.command & 0xFF);         // 低字节在后
    
    buffer.push_back(packet.argLen);                 // 参数长度
    buffer.push_back(packet.flag);                   // 标志
    
    // 添加body数据
    buffer.insert(buffer.end(), packet.body.begin(), packet.body.end());
    
    // 4. 计算并填入校验和
    std::vector<uint8_t> packetData(buffer.begin() + packetStart, buffer.end());
    uint16_t checksum = calculateChecksum(packetData);
    buffer[checksumPos] = checksum & 0xFF;
    buffer[checksumPos + 1] = (checksum >> 8) & 0xFF;
    
    return buffer;
}

std::shared_ptr<Packet> JetPacker::unpack(const std::vector<uint8_t>& buffer) {
    if (buffer.size() < 4) return nullptr;
    
    // 检查响应标识
    uint16_t code = readUint16LE(&buffer[0]);
    if (code != RES_CODE) {
        LOG(ERROR, "Invalid response code: 0x{:04X}, expected: 0x{:04X}", code, RES_CODE);
        return nullptr;
    }
    
    // 读取校验和（暂时跳过校验）
    uint16_t checksum = readUint16LE(&buffer[2]);
    
    // 解析JetPacket结构
    if (buffer.size() < 16) {
        LOG(ERROR, "Buffer too small for JetPacket: {}", buffer.size());
        return nullptr;
    }
    
    auto packet = std::make_shared<Packet>();
    size_t offset = 4; // 跳过响应标识和校验和
    
    // 按照TypeScript的unpack顺序解析
    packet->dataLen = readUint16LE(&buffer[offset]);
    offset += 2;
    
    packet->sourceAddress.group = buffer[offset++];
    packet->sourceAddress.unit = buffer[offset++];
    packet->destinationAddress.group = buffer[offset++];
    packet->destinationAddress.unit = buffer[offset++];
    
    packet->packetSerial = readUint16LE(&buffer[offset]);
    offset += 2;
    
    // 命令字段 - 按照TypeScript的字节序（大端序）
    packet->command = (buffer[offset] << 8) | buffer[offset + 1];
    offset += 2;
    
    packet->argLen = buffer[offset++];
    packet->flag = buffer[offset++];
    
    // 提取body数据
    if (offset < buffer.size()) {
        packet->body.assign(buffer.begin() + offset, buffer.end());
    }
    
    LOG(DEBUG, "Unpacked packet: cmd=0x{:04X}, dataLen={}, argLen={}, bodySize={}", 
        packet->command, packet->dataLen, packet->argLen, packet->body.size());
    
    return packet;
}

} // namespace chainzone
