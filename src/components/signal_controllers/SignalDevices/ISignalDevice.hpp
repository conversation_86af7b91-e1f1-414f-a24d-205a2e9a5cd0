//
// Created by lth on 2024/10/18.
//

#ifndef ISIGNALDEVICE_HPP
#define ISIGNALDEVICE_HPP
#include <json.hpp>

#include "utils/Logger.hpp"
#include <unordered_set>


using json = nlohmann::json;
struct SignalCtl {
    int lockTime = 10;
    int timeout = 30;
    int tscPollingPeriod_ms = 1000;
    std::string hostIP = "127.0.0.1";
    int hostPort = 5161;
    int protocol = 1;
    int defaultPlan = 1;
    int isStartControl = 0;
    int durationInterval = 15; //相同相位指令发送间隔
    int isInControl = 0;
    std::string rawJson;
    // 添加阶段相位查找表
    std::unordered_map<int, std::string> stagePhaseMap;
    // 添加计划相位映射表
    std::unordered_map<int, std::vector<int>> planPhasesMap;
/*
    {
        "signalCtl": {
            "lockTime": 10,
            "timeout": 30,
            "tscPollingPeriod_ms": 1000,
            "hostIP": "**********",
            "hostPort": 5161,
            "protocol": 2,
            "defaultPlan": 1,
            "hostID": 1,
            "tscID": 1,
            "isInControl": 1,
            "intsctID": 1,
        "stagePhase": {
            "1": "EW_WE",
            "2": "NS_SN_EE",
            "3": "NS_SN_EE_WW",
            "4": "SN_EE_WW",
            "5": "EW_WE",
            "6": "NS_SN_EE_WW",
            "7": "NS_NE_EE_WW",
            "8": "SN_SW_EE_WW",
            "9": "SN_SW_WW",
            "10": "NS_NE_EE",
            "11": "EE_WW",
            "12": "EW"
            "13": "WE"
            "14": "EE"
            "15": "WW"
        }
        "planPhases": {
            "1": [3, 7, 15, 14, 13, 12, 11, 10, 9, 8,4, 2, 1],
            "2": [7, 15, 14, 13, 12, 11, 10, 9, 8,4, 2, 1],
            "3": [8, 7, 15, 14, 13, 12, 11, 10, 9, 4,3, 2, 1],
            "4": [8, 7, 15, 14, 13, 12, 11, 10, 9, 4, 3, 2, 1],
            "5": [8, 7, 15, 14, 13, 12, 11, 10, 9, 4,3, 2, 1],
            "6": [8, 7, 15, 14, 13, 12, 11, 10, 9, 4,3, 2, 1],
        }
        }
    }
 */
    // 从JSON字符串解析数据
    void fromJson(const std::string& jsonStr) {
        try {
            json j = json::parse(jsonStr);
            const auto& signalCtl = j.value("signalCtl", json::object());
            lockTime = signalCtl.value("lockTime", 10);
            timeout = signalCtl.value("timeout", 30);
            tscPollingPeriod_ms = signalCtl.value("tscPollingPeriod_ms", 1000);
            hostIP = signalCtl.value("hostIP", "127.0.0.1");
            hostPort = signalCtl.value("hostPort", 5161);
            protocol = signalCtl.value("protocol", 1);
            defaultPlan = signalCtl.value("defaultPlan", 1);
            isStartControl = signalCtl.value("isStartControl", 0);
            isInControl = signalCtl.value("isInControl", 0);
            durationInterval = signalCtl.value("durationInterval", 15);
            // 解析 stagePhase 部分 (现在在 signalCtl 内部)
            stagePhaseMap.clear();
            if (signalCtl.contains("stagePhase") && signalCtl["stagePhase"].is_object()) {
                const auto& stagePhase = signalCtl["stagePhase"];
                for (auto it = stagePhase.begin(); it != stagePhase.end(); ++it) {
                    try {
                        int phase = std::stoi(it.key());
                        std::string phaseValue = it.value();
                        stagePhaseMap[phase] = phaseValue;
                    } catch (const std::exception& e) {
                        LOG(WARNING, "Failed to parse stagePhase entry: {} -> {}. Error: {}",
                            it.key(), it.value().dump(), e.what());
                    }
                }
            }

            // 解析 planPhases 部分 (现在在 signalCtl 内部)
            planPhasesMap.clear();
            if (signalCtl.contains("planPhases") && signalCtl["planPhases"].is_object()) {
                const auto& planPhases = signalCtl["planPhases"];
                for (auto it = planPhases.begin(); it != planPhases.end(); ++it) {
                    try {
                        int planId = std::stoi(it.key());
                        if (it.value().is_array()) {
                            std::vector<int> phases;
                            for (const auto& phase : it.value()) {
                                phases.push_back(phase.get<int>());
                            }
                            planPhasesMap[planId] = phases;
                        }
                    } catch (const std::exception& e) {
                        LOG(WARNING, "Failed to parse planPhases entry: {} -> {}. Error: {}",
                            it.key(), it.value().dump(), e.what());
                    }
                }
            }

            rawJson = jsonStr;
        } catch (const std::exception& e) {
            LOG(ERROR, "SignalCtl from jsonstr convert error. {}", e.what());
        }
    }
    // 根据相位编号获取相位描述
    std::string getPhaseDescription(int phaseNumber) const {
        auto it = stagePhaseMap.find(phaseNumber);
        if (it != stagePhaseMap.end()) {
            return it->second;
        }
        return ""; // 如果找不到对应的相位描述，返回空字符串
    }

    // 根据计划序号获取相位描述数组
    std::vector<std::string> getPlanPhaseDescriptions(int planId) const {
        std::vector<std::string> descriptions;

        // 查找计划对应的相位数组
        auto planIt = planPhasesMap.find(planId);
        if (planIt == planPhasesMap.end()) {
            LOG(WARNING, "Plan ID {} not found in planPhasesMap", planId);
            return descriptions;
        }

        // 遍历相位数组，将每个相位编号转换为相位描述
        for (const auto& phaseNumber : planIt->second) {
            std::string description = getPhaseDescription(phaseNumber);
            if (!description.empty()) {
                descriptions.push_back(description);
            } else {
                LOG(WARNING, "Phase description for phase {} not found in stagePhaseMap", phaseNumber);
                descriptions.push_back("Unknown_" + std::to_string(phaseNumber));
            }
        }

        return descriptions;
    }
    //根据计划序号，获取相位数组
    std::vector<int> getPlanPhases(int planId) const {
        auto it = planPhasesMap.find(planId);
        if (it != planPhasesMap.end()) {
            return it->second;
        }
        return {}; // 如果找不到对应的计划，返回空数组
    }
};



struct SignalEnvState {
    double timestamp;          // 浮点型时间戳
    int currentPhase;          // 当前相位
    int phaseTime;             // 相位时间
    bool signalCtlStatus;      // 信号控制状态
    std::string currentPlan;   // 当前执行计划（字符串）

    // 新增字段
    std::vector<int> planPhase; // 当前计划的相位描述数组
    int phaseIndex;            // 当前相位在相位描述数组中的索引
    std::unordered_map<int, std::string> stagePhaseMap; // 阶段相位查找表


    // toJson 方法：将结构体转换为 JSON 对象
    json toJson() const {
        json j = {
                {"timestamp", timestamp},
                {"currentPhase", currentPhase},
                {"phaseTime", phaseTime},
                {"signalCtlStatus", signalCtlStatus},
                {"currentPlan", currentPlan}
        };
        // 添加新字段到JSON
        j["phases"] = planPhase;
        //j["phaseIndex"] = phaseIndex;

        // 添加阶段相位映射
        /*
        json stagePhaseJson = json::object();
        for (const auto& [phase, description] : stagePhaseMap) {
            stagePhaseJson[std::to_string(phase)] = description;
        }
        j["stagePhase"] = stagePhaseJson;
        */

        return j;
    }

    // fromJson 方法：从 JSON 对象中填充结构体
    static SignalEnvState fromJson(const json& j) {
        SignalEnvState signal_env_state;
        signal_env_state.timestamp = j.at("timestamp").get<double>();
        signal_env_state.currentPhase = j.at("currentPhase").get<int>();
        signal_env_state.phaseTime = j.at("phaseTime").get<int>();
        signal_env_state.signalCtlStatus = j.at("signalCtlStatus").get<bool>();
        signal_env_state.currentPlan = j.at("currentPlan").get<std::string>();
        // 解析新字段
        if (j.contains("planPhase") && j["planPhase"].is_array()) {
            signal_env_state.planPhase = j["planPhase"].get<std::vector<int>>();
        }

        if (j.contains("phaseIndex")) {
            signal_env_state.phaseIndex = j["phaseIndex"].get<int>();
        } else {
            signal_env_state.phaseIndex = -1; // 默认值，表示未找到索引
        }
        // 解析阶段相位映射
        if (j.contains("stagePhase") && j["stagePhase"].is_object()) {
            const auto& stagePhase = j["stagePhase"];
            for (auto it = stagePhase.begin(); it != stagePhase.end(); ++it) {
                try {
                    int phase = std::stoi(it.key());
                    std::string phaseValue = it.value();
                    signal_env_state.stagePhaseMap[phase] = phaseValue;
                } catch (const std::exception& e) {
                    LOG(WARNING, "Failed to parse stagePhase entry: {} -> {}. Error: {}",
                        it.key(), it.value().dump(), e.what());
                }
            }
        }
        return signal_env_state;
    }

    // 从字符串解析
    static SignalEnvState fromString(const std::string& str) {
        json j = json::parse(str);  // 将字符串解析为 json 对象
        return fromJson(j);         // 调用 fromJson 进行处理
    }

    // 使用SignalCtl更新相位描述和索引
    void updatePhaseInfo(const SignalCtl& signalCtl) {
        // 复制阶段相位映射表
        //stagePhaseMap = signalCtl.stagePhaseMap;

        // 尝试将currentPlan转换为整数
        int planId;
        try {
            planId = std::stoi(currentPlan);
        } catch (const std::exception& e) {
            LOG(WARNING, "Failed to convert currentPlan '{}' to integer: {}", currentPlan, e.what());
            phaseIndex = -1;
            return;
        }

        // 获取计划对应的相位数组
        planPhase = signalCtl.getPlanPhases(planId);

        // 查找当前相位在相位数组中的索引
        /*
        phaseIndex = -1;
        auto planIt = signalCtl.planPhasesMap.find(planId);
        if (planIt != signalCtl.planPhasesMap.end()) {
            const auto& phases = planIt->second;
            for (size_t i = 0; i < phases.size(); ++i) {
                if (phases[i] == currentPhase) {
                    phaseIndex = static_cast<int>(i);
                    break;
                }
            }
        }

        if (phaseIndex == -1) {
            LOG(WARNING, "Current phase {} not found in plan {}", currentPhase, planId);
        }
        */
    }
};

class ISignalDevice {
  public:
    // 设备状态枚举
    enum class DeviceState {
        UNINITIALIZED,
        CONTROLABLE, //controlable
        TRANSITIONTOCONTROLABLE, //transitiontocontrolable
        MONITORING,
        TRANSITIONTOMONITORING,
        ERROR,
        TIMEOUT
    };
    static std::string toString(DeviceState state) {
        switch(state) {
            case DeviceState::UNINITIALIZED:
                return "Uninitialized";
            case DeviceState::CONTROLABLE:
                return "Controlable";
            case DeviceState::TRANSITIONTOCONTROLABLE:
                return "TransitionToControlable";
            case DeviceState::MONITORING:
                return "Monitoring";
            case DeviceState::TRANSITIONTOMONITORING:
                return "TransitionToMonitoring";
            case DeviceState::ERROR:
                return "Error";
            case DeviceState::TIMEOUT:
                return "Timeout";
            default:
                return "Unknown";
        }
    }
    struct StateData {
        std::mutex mutex4State;
        DeviceState currentState{DeviceState::UNINITIALIZED};
        DeviceState targetState{DeviceState::UNINITIALIZED};
        [[nodiscard]] std::string getStateChangeMsg(DeviceState newState) const {
            return fmt::format("SignalDevice Current State Change: {} -> {}", toString(currentState), toString
            (newState));
        }
        DeviceState getCurrentState() {
            std::lock_guard lock(mutex4State);
            return currentState;
        }
        DeviceState getTargetState() {
            std::lock_guard lock(mutex4State);
            return targetState;
        }
        void changeTargetState(const DeviceState newState) {
            std::lock_guard lock(mutex4State);
            LOG(INFO, "SignalDevice target mode set {} -> {}.", toString(targetState), toString(newState));
            targetState = newState;
        }
        void chageCurState(const DeviceState newState) {
            std::lock_guard lock(mutex4State);
            if (currentState != newState) {
                    LOG(INFO, getStateChangeMsg(newState));
                    currentState = newState;
            } else {
                LOG(DEBUG, "SignalDevice newState the same as currentState: {}", toString(currentState));
            }
        }
        int currentPhase{0};
        int targetPhase{0};
        int lastPhase{0};
        int currentPhaseTime{0};
    };
    virtual ~ISignalDevice() = default;
    virtual void controlSignal(int phase) = 0;
    virtual SignalEnvState getSignalState() = 0;
    // 获取带有相位描述的信号状态
    SignalEnvState getSignalStateWithPhaseInfo(const SignalCtl& signalCtl) {
        SignalEnvState state = getSignalState();
        state.updatePhaseInfo(signalCtl);
        return state;
    }
    static double getCurrentTimestamp() {
        //const auto now = std::chrono::system_clock::now();
        //return std::chrono::duration<double>(now.time_since_epoch()).count();
        // 1. 获取当前时间点
        const auto now = std::chrono::system_clock::now();

        // 2. 计算从 epoch 到当前时间点的时长，并将其转换为毫秒
        //    std::chrono::milliseconds 是一个表示毫秒的 duration 类型
        auto duration_in_milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch());

        // 3. 获取毫秒数的计数值 (long long 类型)
        long long milliseconds_count = duration_in_milliseconds.count();

        // 4. 将毫秒数转换为以秒为单位的 double 类型
        //    除以 1000.0 (注意是 .0，以确保是浮点数除法)
        return static_cast<double>(milliseconds_count) / 1000.0;
    }
    virtual bool reset() = 0;
    virtual bool setControlMode(int mode) = 0;

    // 重连接口
    virtual bool reconnect(int waitSeconds) = 0;

    // 设置重连阈值
    virtual void setReconnectThreshold(int seconds) = 0;

    DeviceState getCurDeviceState() { return deviceState.getCurrentState(); }
    DeviceState getTarDeviceState() { return deviceState.getTargetState(); }
    StateData deviceState;
};


#endif //ISIGNALDEVICE_HPP
