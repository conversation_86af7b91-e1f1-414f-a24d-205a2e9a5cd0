//
// Created by lth on 2024/10/31.
//

#include "SignalDeviceHiSense.hpp"
#include "utils/Logger.hpp"
#include <thread>
#include <chrono>

SignalDeviceHiSense::SignalDeviceHiSense(const SignalCtl &signalCtl): lastPhaseStartTimestamp(0),
                                                                      lastAliveTimestamp(0) {
    LOG(INFO, "HiSense 20999 signal machine created {}, {}", signalCtl.hostIP, signalCtl.hostPort);

    // 存储连接参数，以便重连时使用
    hostIP = signalCtl.hostIP;
    hostPort = signalCtl.hostPort;
    hostId = 0x01;
    tscId = 0x01;
    intersectionId = 0x01;

    // 创建UDP客户端和执行器
    udpClient = std::make_shared<UDPClient>(hostIP, hostPort);
    queryExecutor = std::make_shared<gb20999::QueryExecutor>(udpClient, hostId, tscId, intersectionId);
    setterExecutor = std::make_shared<gb20999::SetterExecutor>(udpClient, hostId, tscId, intersectionId);
    statusInfo = std::make_shared<gb20999::RunStatusInfo>();
    timeout = signalCtl.timeout;
    isAlive = true;
    ctlModeChanged = false;

    // 初始化timetable模式跟踪变量
    timetableModeStartTime = 0;
    inTimetableMode = false;
    timetableModeReconnectThreshold = 10; // 默认60秒，可以根据需要调整
}

SignalDeviceHiSense::~SignalDeviceHiSense() {
    try {
        // 尝试重置信号机状态，但不抛出异常
        try {
            if (deviceState.getCurrentState() != DeviceState::UNINITIALIZED) {
                LOG(INFO, "Resetting HiSense signal machine during destruction");
                setControlMode(0);
            }
        } catch (const std::exception& e) {
            LOG(WARNING, "Exception during HiSense reset: {}", e.what());
        }

        // 释放资源
        setterExecutor.reset();
        queryExecutor.reset();
        udpClient.reset();

        LOG(INFO, "HiSense 20999 signal machine destroy called.");
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception in HiSense destructor: {}", e.what());
    } catch (...) {
        LOG(ERROR, "Unknown exception in HiSense destructor");
    }
}

void SignalDeviceHiSense::controlSignal(int phase) {
    // 检查执行器是否存在
    if (!setterExecutor || !udpClient) {
        LOG(ERROR, "Cannot control signal: setter executor or UDP client is null. Reconnection may be in progress.");
        return;
    }

    DeviceState currentState = deviceState.getCurrentState();
    if (currentState != DeviceState::CONTROLABLE && currentState != DeviceState::TRANSITIONTOCONTROLABLE) {
        LOG(ERROR, "Signal Machine is not controlable, cannot control signal.");
        return;
    }
    if (phase >= 0) {
        LOG(INFO, "control Phase to {}", phase);
        try {
            auto result = setterExecutor->execute<gb20999::PhaseSetter>(0x01, phase);
            if (result.has_value()) {
                LOG(INFO, "Signal Machine set phase result: " + std::visit([](auto&& arg) {
                        return gb20999::SetResultVisitor::toString(arg);
                        }, *result));
            } else {
                LOG(ERROR, "Set phase {} Failed.", phase);
            }
        } catch (const std::exception& e) {
            LOG(ERROR, "Exception during controlling signal: {}", e.what());
        } catch (...) {
            LOG(ERROR, "Unknown exception during controlling signal");
        }
    } else {
        LOG(ERROR, "Phase {} not valid.", phase);
    }
}

SignalEnvState SignalDeviceHiSense::getSignalState() {
    // 检查执行器是否存在
    if (!queryExecutor || !udpClient) {
        LOG(ERROR, "Cannot get signal state: query executor or UDP client is null. Reconnection may be in progress.");
        return {0, 0, 0, false, "-1"};
    }

    auto result = queryExecutor->execute<gb20999::RunStatusQuery>();
    if (result.has_value()) {
        lastAliveTimestamp = std::time(nullptr);

        // 存储是否需要重连的标志和当前状态
        bool needReconnect = false;
        time_t timeInTimetableMode = 0;

        // 使用一个局部作用域来限制锁的范围
        {
            std::lock_guard<std::mutex> lock(mutex4StateInfo);
            statusInfo = std::make_shared<
                gb20999::RunStatusInfo>(*std::get_if<gb20999::RunStatusInfo>(&result.value()));

            if (deviceState.currentPhase != statusInfo->currentPhaseStage) {
                deviceState.currentPhase = statusInfo->currentPhaseStage;
                HiSenseStateData.lastPhaseStartTimestamp = std::time(nullptr);
            }
            LOG(INFO, "Signal Machine current State is {}.", gb20999::RunModeData::modeToString(statusInfo->runMode));

            switch (statusInfo->runMode) {
                case gb20999::RunModeData::Mode::CENTER_MANUAL:
                    deviceState.chageCurState(DeviceState::CONTROLABLE);
                    // 重置timetable模式跟踪
                    inTimetableMode = false;
                    break;
                case gb20999::RunModeData::Mode::LOCAL_FIXCYCLE:
                    deviceState.chageCurState(DeviceState::MONITORING);
                    // 重置timetable模式跟踪
                    inTimetableMode = false;
                    break;
                case gb20999::RunModeData::Mode::CENTER_TIMETABLE:
                    deviceState.chageCurState(DeviceState::TRANSITIONTOCONTROLABLE);

                    // 跟踪信号机在timetable模式下的时间
                    if (timetableModeReconnectThreshold > 0) { // 只在阈值大于0时进行跟踪
                        if (!inTimetableMode) {
                            // 首次进入timetable模式，记录开始时间
                            inTimetableMode = true;
                            timetableModeStartTime = std::time(nullptr);
                            LOG(INFO, "Signal machine entered timetable mode, starting timer");
                        } else {
                            // 检查是否超过阈值时间
                            time_t currentTime = std::time(nullptr);
                            timeInTimetableMode = currentTime - timetableModeStartTime;

                            if (timeInTimetableMode >= timetableModeReconnectThreshold) {
                                // 标记需要重连，但在释放锁后再执行
                                needReconnect = true;
                            }
                        }
                    }
                    break;
                default:
                    deviceState.chageCurState(DeviceState::UNINITIALIZED);
                    // 重置timetable模式跟踪
                    inTimetableMode = false;
                    break;
            }

            auto delayTime = static_cast<int>(std::time(nullptr) - lastAliveTimestamp);
            if (delayTime > timeout && deviceState.getCurrentState() != DeviceState::TIMEOUT) {
                LOG(ERROR, "signal machine timeout delaytime is {}, will change to local fix control", delayTime);
                deviceState.chageCurState(DeviceState::TIMEOUT);
            }
        } // 锁的作用域结束，释放锁

        // 在锁释放后执行重连操作，避免死锁
        if (needReconnect && deviceState.getTargetState() != DeviceState::CONTROLABLE) {
            LOG(WARNING, "Signal machine has been in timetable mode for {} seconds, triggering reconnection",
                timeInTimetableMode);

            // 触发重连操作，等待3秒
            if (reconnect(60)) {
                LOG(INFO, "Reconnection successful after timetable mode timeout");
            } else {
                LOG(ERROR, "Reconnection failed after timetable mode timeout");
            }

            // 重置跟踪状态
            inTimetableMode = false;

            // 重连后重新获取状态
            return getSignalState();
        }

        // 使用已获取的状态信息构造返回值
        SignalEnvState state = {
            getCurrentTimestamp(), statusInfo->currentPhaseStage,
            static_cast<int>(std::time(nullptr) - HiSenseStateData.lastPhaseStartTimestamp),
            statusInfo->runMode == gb20999::RunModeData::Mode::CENTER_MANUAL,
            std::to_string(statusInfo->currentPlan)
        };
        return state;
    }
    return {0, 0, 0, false, "-1"};
}

bool SignalDeviceHiSense::reset() {
    return setControlMode(0);
}

void SignalDeviceHiSense::setTimetableModeReconnectThreshold(int seconds) {
    if (seconds < 0) {
        LOG(WARNING, "Invalid timetable mode reconnect threshold: {}, setting to 0 (disabled)", seconds);
        timetableModeReconnectThreshold = 0;
    } else {
        timetableModeReconnectThreshold = seconds;
        LOG(INFO, "Set timetable mode reconnect threshold to {} seconds", seconds);

        // 如果设置为0，表示禁用自动重连
        if (seconds == 0) {
            LOG(INFO, "Automatic reconnection for timetable mode is now disabled");
        }
    }
}

void SignalDeviceHiSense::setReconnectThreshold(int seconds) {
    // 对于海信信号机，重连阈值就是timetable模式的重连阈值
    setTimetableModeReconnectThreshold(seconds);
}

bool SignalDeviceHiSense::reconnect(int waitSeconds) {
    try {
        LOG(INFO, "Disconnecting from HiSense signal machine for reconnection");

        // 直接重置资源，而不调用setControlMode以避免死锁
        // 我们将在重新连接后通过查询状态来确认连接成功
        LOG(INFO, "Skipping control mode reset to avoid potential deadlock");

        // 释放资源
        setterExecutor.reset();
        queryExecutor.reset();
        udpClient.reset();

        LOG(INFO, "Disconnected from HiSense signal machine, waiting {} seconds before reconnecting", waitSeconds);

        // 等待指定的秒数
        if (waitSeconds > 0) {
            std::this_thread::sleep_for(std::chrono::seconds(waitSeconds));
        }

        // 重新创建UDP客户端和执行器
        LOG(INFO, "Reconnecting to HiSense signal machine at {}:{}", hostIP, hostPort);
        udpClient = std::make_shared<UDPClient>(hostIP, hostPort);
        queryExecutor = std::make_shared<gb20999::QueryExecutor>(udpClient, hostId, tscId, intersectionId);
        setterExecutor = std::make_shared<gb20999::SetterExecutor>(udpClient, hostId, tscId, intersectionId);

        // 重置状态信息
        statusInfo = std::make_shared<gb20999::RunStatusInfo>();
        lastAliveTimestamp = std::time(nullptr);
        isAlive = true;

        // 重置timetable模式跟踪
        inTimetableMode = false;
        timetableModeStartTime = 0;

        // 测试连接是否成功
        try {
            auto result = queryExecutor->execute<gb20999::RunStatusQuery>();
            if (result.has_value()) {
                LOG(INFO, "Successfully reconnected to HiSense signal machine");
                return true;
            } else {
                LOG(ERROR, "Failed to reconnect to HiSense signal machine");
                return false;
            }
        } catch (const std::exception& e) {
            LOG(ERROR, "Exception during testing reconnection: {}", e.what());
            return false;
        } catch (...) {
            LOG(ERROR, "Unknown exception during testing reconnection");
            return false;
        }
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception during HiSense reconnection: {}", e.what());
        return false;
    } catch (...) {
        LOG(ERROR, "Unknown exception during HiSense reconnection");
        return false;
    }
}

bool SignalDeviceHiSense::setControlMode(int mode) {
    // Check for valid mode values
    if (mode != 0 && mode != 1) {
        LOG(ERROR, "Mode {} not supported. Only modes 0 (monitoring) and 1 (controlable) are valid.", mode);
        return false;
    }

    // 首先检查执行器是否存在
    if (!setterExecutor || !queryExecutor || !udpClient) {
        LOG(ERROR, "Cannot set control mode: executors or UDP client are null. Reconnection may be in progress.");
        return false;
    }

    DeviceState currentState = deviceState.getCurrentState();

    if (mode == 1) { // Set to CONTROLABLE mode
        // Already in the desired state
        if (currentState == DeviceState::CONTROLABLE) {
            LOG(WARNING, "HiSense current mode already in controlable. No need to set.");
            return true;
        }

        // Cannot transition from uninitialized state
        if (currentState == DeviceState::UNINITIALIZED) {
            LOG(WARNING, "HiSense current mode is uninitialized. Cannot set to controlable.");
            return false;
        }

        // Set target state and execute command
        LOG(INFO, "Setting HiSense target mode to controlable.");
        deviceState.changeTargetState(DeviceState::CONTROLABLE);

        try {
            std::lock_guard<std::mutex> lock(mutex4StateInfo);
            auto result = setterExecutor->execute<gb20999::ModeSetter>(0x01, gb20999::ModeSetter::Mode::CENTER_MANUAL);

            if (result.has_value()) {
                LOG(INFO, "Signal Machine Run Mode Switch To CENTER_MANUAL Command send Result: " +
                    std::visit([](auto&& arg) {
                        return gb20999::SetResultVisitor::toString(arg);
                    }, *result));
                // need follow a phase set command to make mode change effective
                //XXX need this?
                if (statusInfo) {
                    controlSignal(statusInfo->currentPhaseStage);
                }
                return true;
            } else {
                LOG(ERROR, "Failed to execute CENTER_MANUAL mode command.");
                return false;
            }
        } catch (const std::exception& e) {
            LOG(ERROR, "Exception during setting control mode to CONTROLABLE: {}", e.what());
            return false;
        } catch (...) {
            LOG(ERROR, "Unknown exception during setting control mode to CONTROLABLE");
            return false;
        }
    } else { // mode == 0, Set to MONITORING mode
        // Already in the desired state
        if (currentState == DeviceState::MONITORING) {
            LOG(WARNING, "HiSense current mode already in monitoring. No need to set.");
            return true;
        }

        // Cannot transition from uninitialized state
        if (currentState == DeviceState::UNINITIALIZED) {
            LOG(WARNING, "HiSense current mode is uninitialized. Cannot set to monitoring.");
            return false;
        }

        // Set target state and execute command
        LOG(INFO, "Setting HiSense target mode to monitoring.");
        deviceState.changeTargetState(DeviceState::MONITORING);
        //add to unlock
        controlSignal(0);



        try {
            std::lock_guard<std::mutex> lock(mutex4StateInfo);
            auto result = setterExecutor->execute<gb20999::ModeSetter>(0x01, gb20999::ModeSetter::Mode::LOCAL_FIXCYCLE);

            if (result.has_value()) {
                LOG(INFO, "Signal Machine Run Mode switch To LOCAL_FIXCYCLE Command send Result: " +
                    std::visit([](auto&& arg) {
                        return gb20999::SetResultVisitor::toString(arg);
                    }, *result));
                return true;
            } else {
                LOG(ERROR, "Failed to execute LOCAL_FIXCYCLE mode command.");
                return false;
            }
        } catch (const std::exception& e) {
            LOG(ERROR, "Exception during setting control mode to MONITORING: {}", e.what());
            return false;
        } catch (...) {
            LOG(ERROR, "Unknown exception during setting control mode to MONITORING");
            return false;
        }
    }
}