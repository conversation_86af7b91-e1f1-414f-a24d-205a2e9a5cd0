//
// Created by lth on 2024/10/18.
//

#ifndef SIGNALDEVICE1049_HPP
#define SIGNALDEVICE1049_HPP
#include "ISignalDevice.hpp"
#include "utils/RedisClient.hpp"
#include "cpp-httplib/httplib.h"
#include <queue>



class SignalDevice1049 : public ISignalDevice{
public:
    SignalDevice1049(const SignalCtl& signalCtl);

    ~SignalDevice1049();

    void controlSignal(int phase) override;
    SignalEnvState getSignalState() override;
    bool reset() override;
    bool setControlMode(int mode) override;

    // 重连接口
    bool reconnect(int waitSeconds) override;

    // 设置重连阈值
    void setReconnectThreshold(int seconds) override;
private:
    std::unique_ptr<RedisClient> redisClient_;
    // 内部工作线程函数
    void workerThread();

    // 发送请求到信号机
    std::string sendToSignalMachine(const std::string& path, const nlohmann::json& params = nullptr) const;

    // 控制命令相关函数
    void sendSetPhaseMsg(int phase);
    void sendSetCtrlModeMsg(int mode);
    void sendQueryStateMsg();

    // 更新相位信息
    void updatePhaseInfo(const nlohmann::json& stateInfo);

    // 配置信息
    struct Config {
        std::string serverHost;
        int serverPort;
        std::string crossId;
        int timeout;
        int pollingPeriod;
    } m_config;

    // 运行状态
    bool m_running;
    bool m_connected;
    std::thread m_thread;
    std::mutex m_mutex;
    std::queue<std::pair<std::string, int>> m_cmdQueue;

    // 通信相关
    std::unique_ptr<httplib::Client> m_client;

    // 状态信息
    int m_errCount;
    int64_t m_lastQueryTime;
    int m_lastPhase;
    SignalEnvState m_currentState;

    // 状态监控相关
    bool m_inControlMode{false};
    int64_t m_controlModeStartTime{0};
    int m_controlModeReconnectThreshold{60}; // 在控制模式下多少秒后触发重连
};



#endif //SIGNALDEVICE1049_HPP