//
// Created by lth on 2024/11/14.
//

#ifndef SIGNALDEVICEHKOPENAPI_HPP
#define SIGNALDEVICEHKOPENAPI_HPP
#include <mutex>
#include <queue>
#include <thread>
#include <HttpUtil/HttpUtil.h>
#include <stdexcept>
#include <typeinfo>

#include "ISignalDevice.hpp"
#include "json.hpp"

using json = nlohmann::json;

namespace  HKOpenApi {
    // 获取当前时间戳(毫秒)
    static int64_t getCurrentTime_int64() {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
    }

    class HKOpenApiDriver {
    public:
        // 控制模式枚举
        enum ControlMode {
            UNINITIALIZED = -1,
            SYSTEM_CONTROL = 0,
            LIGHTS_OFF = 1,
            FLASH_YELLOW = 2,
            ALL_RED = 3,
            FIXED_TIME = 4,
            COORDINATED_GREEN = 5,
            COORDINATED_RED = 6,
            FULL_ACTUATED = 7,
            SEMI_ACTUATED = 8,
            COORDINATED_ACTUATED = 9,
            STEP_CONTROL = 10,
            CANCEL_STEP = 11,
            LOCK_STATE = 12,
            UNLOCK_STATE = 13
        };
        struct Config {
            std::string platformUrl;
            std::string appkey;
            std::string secret;
            std::string crossCode;
            std::string crossName;
            std::string userID;
            int timeout;
            int tscPollingPeriod;
            int currentPlan;
            std::string setlockflow_APISubURL;
            std::string SetSignalControl_APISubURL;
            std::string getRealtimeSchemeStateInfo_APISubURL;
            std::vector<std::vector<int>> schemes;
            // 新增lockPhaseChannelMap配置
            std::map<int, std::vector<int>> lockPhaseChannelMap;

            // 新增：协议报文字段名配置
            struct FieldNames {
                // setLockFlow接口字段名
                struct LockFlowFieldNames {
                    std::string crossCode = "crossCode";
                    std::string operaType = "operaType";
                    std::string channelType = "channelType";
                    std::string transitTime = "transitTime";
                    std::string channelList2 = "channelList2";
                    std::string channels = "channels";
                    std::string lockTime = "lockTime";
                    std::string userId = "userId";
                } lockFlow;

                // SetSignalControl接口字段名
                struct SignalControlFieldNames {
                    std::string userId = "userId";
                    std::string crossCode = "crossCode";
                    std::string controlType = "controlType";
                    std::string controlNo = "controlNo";
                    std::string duration = "duration";
                } signalControl;

                // 查询状态接口字段名
                struct QueryStateFieldNames {
                    std::string pageSize = "pageSize";
                    std::string pageNo = "pageNo";
                    std::string searchObj = "searchObj";
                    std::string crossCodes = "crossCodes";
                } queryState;

                // 响应解析字段名
                struct ResponseFieldNames {
                    std::string code = "code";
                    std::string data = "data";
                    std::string list = "list";
                    std::string controlType = "controlType";
                    std::string lockPhases = "lockPhases";
                    std::string rings = "rings";
                    std::string phaseList = "phaseList";
                    std::string channelState = "channelState";
                    std::string patternNo = "patternNo";
                    std::string runTime = "runTime";
                    std::string phaseLength = "phaseLength";
                    std::string phaseNo = "phaseNo";
                    std::string state = "state";
                    std::string channelNo = "channelNo";
                } response;

            } fieldNames;
        };
        HKOpenApiDriver(const Config& config);
        ~HKOpenApiDriver();

        ControlMode getPhaseCtrlMode();

        void start();
        void stop();
        bool getConnState() const;
        void setPhase(int phase);
        void setControlMode(ControlMode mode);
        std::map<std::string, int> getPhaseInfo();
    private:
        // 内部工作线程函数
        void workerThread();

        // 发送消息到信号机
        std::string sendToTsc(const json& msgBody, const std::string& subUrl) const;
        void sendSetPhaseMsg(int phase);
        void sendSetCtlModeMsg(ControlMode mode);
        void sendQueryStateMsg();
        void reset();

        // 相位转换函数
        int phaseToTsc(int phaseFromAlg) const;
        int phaseFromTsc(int phaseFromTsc);

        // 更新相位信息
        void updatePhaseInfo(int currentStage, ControlMode phaseCtrlMode, int currentPlanNo, int latency);
        int calculatePeriodicalModeStage(const json& phaseList);
        int lookupLockPhase(const json& channelStateList);

        Config m_config;
        bool m_running;
        bool m_connected;
        std::thread m_thread;
        std::mutex m_mutex;
        std::queue<std::pair<std::string, int>> m_cmdQueue;

        int m_errCount;
        std::map<std::string, int> m_phaseInfo;
        int64_t m_currPhaseStartTs;
        int m_lastSetPhaseCmd;
        int64_t m_lastSetPhaseTs;
        int m_phaseDuration;
        int64_t m_lastQueryPakTs;

        std::map<int,int> m_tscPhaseLut;
        ControlMode m_phaseCtlMode;

        // 重试机制相关成员变量
        int m_currentPhaseRetryCount{0};
        int m_lastRetryPhase{-1};

    };
    inline HKOpenApiDriver::HKOpenApiDriver(const Config& config) :
    m_config(config),
    m_running(false),
    m_connected(false),
    m_errCount(0),
    m_currPhaseStartTs(0),
    m_lastSetPhaseCmd(-1),
    m_lastSetPhaseTs(0),
    //m_phaseDuration(15),
    m_phaseDuration(60),
    m_lastQueryPakTs(0),
    m_phaseCtlMode(HKOpenApiDriver::ControlMode::UNINITIALIZED) {

        // 初始化相位查找表
        if (!m_config.schemes.empty()) {
            for (const auto& plan : m_config.schemes) {
                for (size_t i = 0; i < plan.size(); i++) {
                    m_tscPhaseLut[plan[i]] = i;
                }
            }
        }
    }
    inline HKOpenApiDriver::~HKOpenApiDriver() {
        stop();
    }
    inline HKOpenApiDriver::ControlMode HKOpenApiDriver::getPhaseCtrlMode() {
        return m_phaseCtlMode;
    }
    inline void HKOpenApiDriver::start() {
        m_running = true;
        m_thread = std::thread(&HKOpenApiDriver::workerThread, this);
    }
    inline void HKOpenApiDriver::stop() {
        if (m_running) {
            m_running = false;
            m_cmdQueue.push({"exit", 0});
            if (m_thread.joinable()) {
                m_thread.join();
            }
            LOG(INFO, "Working thread Stoped.");
        }
    }
    inline std::string HKOpenApiDriver::sendToTsc(const json& msgBody, const std::string& subUrl) const {
        std::string url = "https://" + m_config.platformUrl + subUrl;
        std::string body = msgBody.dump();
        int dataLen = 0;

        char* result = httpUtil::HTTPUTIL_Post(
            url.c_str(),
            body.c_str(),
            m_config.appkey.c_str(),
            m_config.secret.c_str(),
            m_config.timeout,
            &dataLen
        );

        std::string response;
        if (result) {
            response = std::string(result, dataLen);
            httpUtil::HTTPUTIL_Free(result);
        } else {
            int errCode = httpUtil::HTTPUTIL_GetLastStatus();
            LOG(ERROR, "sendToTsc response is NULL, errorCode is {}", errCode);
        }
        return response;
    }
    inline void HKOpenApiDriver::sendSetPhaseMsg(int phase) {
        int64_t now = getCurrentTime_int64();
        if (phase == m_lastSetPhaseCmd &&
            (now - m_lastSetPhaseTs < m_phaseDuration * 1000 - 2000)) {
            LOG(INFO, "跳过设置相位，因为相位{}与上次设置相同且时间间隔过短", phase);
            return;
        }

        // 检查上次设置的相位是否生效
        if (m_lastSetPhaseCmd != -1 && !m_phaseInfo.empty() &&
            m_phaseInfo["currentPhase"] != m_lastSetPhaseCmd) {
            LOG(WARNING, "当前相位{}与上次设置相位{}不一致，跳过设置相位",
                m_phaseInfo["currentPhase"], m_lastSetPhaseCmd);
            return;
        }

        std::string response;
        // 检查phase是否在lockPhaseChannelMap中
        auto it = m_config.lockPhaseChannelMap.find(phase);
        if (it != m_config.lockPhaseChannelMap.end()) {
            // 如果当前是步进控制模式，先取消步进控制
            if (!m_phaseInfo.empty() && m_phaseInfo["CtrlMode"] == STEP_CONTROL) {
                sendSetCtlModeMsg(CANCEL_STEP);
                LOG(INFO, "通道锁定前，先取消步进控制模式");
            }

            // 使用setLockFlow接口
            json body;
            body[m_config.fieldNames.lockFlow.crossCode] = m_config.crossCode;
            body[m_config.fieldNames.lockFlow.operaType] = 0;          // 锁定
            body[m_config.fieldNames.lockFlow.channelType] = 0;        // 固定
            body[m_config.fieldNames.lockFlow.transitTime] = 5;        // 过度时长
            body[m_config.fieldNames.lockFlow.channelList2] = json::array(); // 空数组
            body[m_config.fieldNames.lockFlow.channels] = it->second;  // 使用映射中的通道号数组
            body[m_config.fieldNames.lockFlow.lockTime] = 90;          // 锁定时长
            body[m_config.fieldNames.lockFlow.userId] = "admin";       // 固定admin
            response = sendToTsc(body,
                m_config.setlockflow_APISubURL + "?userId=" + m_config.userID);
        } else {
            // 先设置步进控制
            json body;
            body[m_config.fieldNames.signalControl.userId] = m_config.userID;
            body[m_config.fieldNames.signalControl.crossCode] = m_config.crossCode;
            body[m_config.fieldNames.signalControl.controlType] = 10; // 步进控制
            body[m_config.fieldNames.signalControl.controlNo] = phase;
            body[m_config.fieldNames.signalControl.duration] = m_phaseDuration;

            response = sendToTsc(body,
                m_config.SetSignalControl_APISubURL + "?userId=" +
                m_config.userID);

            // 如果当前是通道锁定模式，需要解除锁定
            if (!m_phaseInfo.empty() && m_phaseInfo["CtrlMode"] == LOCK_STATE) {
                sendSetCtlModeMsg(UNLOCK_STATE);
                LOG(INFO, "步进控制后，再解除通道锁定");
            }
        }

        if (!response.empty()) {
            m_lastSetPhaseCmd = phase;
            m_lastSetPhaseTs = now;
            // 成功时重置重试计数
            if (m_lastRetryPhase == phase) {
                m_currentPhaseRetryCount = 0;
                m_lastRetryPhase = -1;
            }
        } else {
            // 响应为空，实现重试机制
            // 检查是否是同一个相位的重试
            if (m_lastRetryPhase == phase) {
                m_currentPhaseRetryCount++;
            } else {
                m_lastRetryPhase = phase;
                m_currentPhaseRetryCount = 1;
            }

            if (m_currentPhaseRetryCount <= 3) { // 最多重试3次
                std::lock_guard<std::mutex> lock(m_mutex);
                m_cmdQueue.push({"setPhase", phase});
                LOG(WARNING, "设置相位{}失败，重试第{}次", phase, m_currentPhaseRetryCount);
            } else {
                LOG(ERROR, "设置相位{}失败，已达到最大重试次数, 切回监控模式", phase);
                m_currentPhaseRetryCount = 0;
                m_lastRetryPhase = -1;
                reset();
            }
        }
    }
    inline void HKOpenApiDriver::sendQueryStateMsg() {
        json body;
        body[m_config.fieldNames.queryState.pageSize] = 1;
        body[m_config.fieldNames.queryState.pageNo] = 1;
        json searchObj;
        searchObj[m_config.fieldNames.queryState.crossCodes] = json::array({m_config.crossCode});
        body[m_config.fieldNames.queryState.searchObj] = searchObj;

        int64_t now = getCurrentTime_int64();
        std::string response = sendToTsc(body,
            m_config.getRealtimeSchemeStateInfo_APISubURL);

        if (response.empty()) {
            m_errCount++;
            return;
        }

        try {
            json stateInfo = json::parse(response);
            if (stateInfo[m_config.fieldNames.response.code] != "0") {
                m_errCount++;
                return;
            }

            m_errCount = 0;
            m_connected = true;

            auto curCrossState = stateInfo[m_config.fieldNames.response.data]
                                         [m_config.fieldNames.response.list][0];
            int currentStage = 0;
            ControlMode phaseCtrlMode;

            if (curCrossState[m_config.fieldNames.response.controlType] == 10) { // 步进模式
                if (!curCrossState[m_config.fieldNames.response.lockPhases].empty()) {
                    currentStage = curCrossState[m_config.fieldNames.response.lockPhases][0];
                } else {
                    currentStage = m_phaseInfo["currentPhase"];
                }
                phaseCtrlMode = STEP_CONTROL;
            }
            else if (curCrossState[m_config.fieldNames.response.controlType] == 5) { // 协调绿波模式
                currentStage = calculatePeriodicalModeStage(
                    curCrossState[m_config.fieldNames.response.rings][0]
                                [m_config.fieldNames.response.phaseList]);
                phaseCtrlMode = COORDINATED_GREEN;
            }
            else if (curCrossState[m_config.fieldNames.response.controlType] == 4) { // 定周期模式
                currentStage = calculatePeriodicalModeStage(
                    curCrossState[m_config.fieldNames.response.rings][0]
                                [m_config.fieldNames.response.phaseList]);
                phaseCtrlMode = FIXED_TIME;
            }
            else if (curCrossState[m_config.fieldNames.response.controlType] == 12) { // 通道锁定模式
                currentStage = lookupLockPhase(curCrossState[m_config.fieldNames.response.channelState]);
                LOG(INFO, "通道锁定模式，反查出当前相位：{}", currentStage);
                if (currentStage == 0 && !m_phaseInfo.empty()) {
                    currentStage = m_phaseInfo["currentPhase"];
                    LOG(INFO, "通道锁定模式，当前相位为0，保持前次查询到的相位：{}", currentStage);
                }
                phaseCtrlMode = LOCK_STATE;
            }
            else {
                currentStage = 0;
                phaseCtrlMode = SYSTEM_CONTROL;
            }
            int currnetPlanNo = curCrossState.value(m_config.fieldNames.response.patternNo, 0);
            updatePhaseInfo(currentStage, phaseCtrlMode, currnetPlanNo, getCurrentTime_int64() - now);

        } catch (const std::exception& e) {
            m_errCount++;
            LOG(ERROR, "Send query status message error in sendQueryStateMsg() at {}:{}: {}, Exception type: {}, Response JSON: {}",
                __FILE__, __LINE__, e.what(), typeid(e).name(), response);
        }

        if (m_errCount >= 3) {
            m_connected = false;
        }
    }
    inline void HKOpenApiDriver::workerThread() {
        LOG(INFO, "Working thread Started.");
        while (m_running) {
            // 处理命令队列
            while (!m_cmdQueue.empty()) {
                auto cmd = m_cmdQueue.front();
                m_cmdQueue.pop();

                if (cmd.first == "exit") {
                    return;
                }
                else if (cmd.first == "setPhase") {
                    sendSetPhaseMsg(cmd.second);
                }
                else if (cmd.first == "setControlMode") {
                    sendSetCtlModeMsg(static_cast<ControlMode>(cmd.second));
                }
            }

            // 定期查询状态
            int64_t now = getCurrentTime_int64();
            if (now - m_lastQueryPakTs >= m_config.tscPollingPeriod) {
                sendQueryStateMsg();
                m_lastQueryPakTs = now;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
    inline void HKOpenApiDriver::sendSetCtlModeMsg(ControlMode ctrlMode) {
        if (ctrlMode == CANCEL_STEP) {
            json body;
            body[m_config.fieldNames.signalControl.userId] = m_config.userID;
            body[m_config.fieldNames.signalControl.crossCode] = m_config.crossCode;
            body[m_config.fieldNames.signalControl.controlType] = CANCEL_STEP;
            body[m_config.fieldNames.signalControl.controlNo] = 0;
            body[m_config.fieldNames.signalControl.duration] = 0;
            std::string response = sendToTsc(body,
            m_config.SetSignalControl_APISubURL + "?userId=" + m_config.userID);
        } else if (ctrlMode == UNLOCK_STATE) {
            // 解除通道锁定
            json body;
            body[m_config.fieldNames.lockFlow.crossCode] = m_config.crossCode;
            body[m_config.fieldNames.lockFlow.operaType] = 1;  // 解除锁定
            body[m_config.fieldNames.lockFlow.channelType] = 0;  // 固定
            body[m_config.fieldNames.lockFlow.transitTime] = 5;  // 过度时长
            body[m_config.fieldNames.lockFlow.channelList2] = json::array();  // 空列表
            body[m_config.fieldNames.lockFlow.channels] = json::array();  // 空列表
            body[m_config.fieldNames.lockFlow.lockTime] = 90;  // 锁定时长
            body[m_config.fieldNames.lockFlow.userId] = "admin";  // 固定admin
            std::string response = sendToTsc(body,
            m_config.setlockflow_APISubURL + "?userId=" + m_config.userID);
        }
        m_lastSetPhaseCmd = -1;
    }

    inline bool HKOpenApiDriver::getConnState() const {
        return m_connected;
    }
    inline void HKOpenApiDriver::setPhase(int phase) {
        int phaseToTsc = -1;
        if (!m_config.schemes.empty()) {
            phaseToTsc = this->phaseToTsc(phase);
        } else {
            phaseToTsc = phase;
        }
        if (phaseToTsc >= 0) {
            m_cmdQueue.push({"setPhase", phaseToTsc});
        }
    }
    inline void HKOpenApiDriver::setControlMode(ControlMode mode) {
        m_cmdQueue.push({"setControlMode", static_cast<int>(mode)});
    }
    inline std::map<std::string, int> HKOpenApiDriver::getPhaseInfo() {
        std::lock_guard<std::mutex> lock(m_mutex);
        auto info = m_phaseInfo;
        if (!info.empty()) {
            info["currentPhase"] = phaseFromTsc(info["currentPhase"]);
            //info["phaseTime"]++;
        }
        return info;
    }
    // 从算法相位转换到信号机相位
    inline int HKOpenApiDriver::phaseToTsc(int phaseFromAlg) const {
        // 检查当前plan序号是否合法
        if (m_config.currentPlan <= 0 ||
            m_config.currentPlan > m_config.schemes.size()) {
            LOG(ERROR,"config schemes error.");
            return -1;
            }

        // 检查相位值是否在plan范围内
        const auto& currentPlan = m_config.schemes[m_config.currentPlan - 1];
        if (phaseFromAlg < 0 || phaseFromAlg >= currentPlan.size()) {
            LOG(ERROR,"phaseFromAlg error.");
            return -1;
        }
        return currentPlan[phaseFromAlg];
    }
    // 从信号机相位转换到算法相位
    inline int HKOpenApiDriver::phaseFromTsc(int phaseFromTsc) {
        if (!m_config.schemes.empty()) {
            auto it = m_tscPhaseLut.find(phaseFromTsc);
            if (it == m_tscPhaseLut.end()) {
                return -1;
            }
            return it->second;
        } else {
            return phaseFromTsc;
        }
    }
    // 更新相位信息
    inline void HKOpenApiDriver::updatePhaseInfo(int currentStage,
                                                 ControlMode phaseCtrlMode,
                                                 int currentPlanNo,
                                                 int latency) {
        int64_t now = getCurrentTime_int64();

        // 相位发生变化或首次更新时,更新起始时间戳
        std::lock_guard<std::mutex> lock(m_mutex);
        if (m_currPhaseStartTs == 0 ||
            m_phaseInfo["currentPhase"] != currentStage) {
            m_currPhaseStartTs = now;
            }

        // 无效相位时保持前一相位
        if (currentStage == 0 && !m_phaseInfo.empty()) {
            currentStage = m_phaseInfo["currentPhase"];
        }

        m_phaseInfo = {
            {"timestamp", static_cast<int>(now)},
            {"currentPhase", currentStage},
            {"currentPlan", currentPlanNo},
            {"phaseTime", static_cast<int>((now - m_currPhaseStartTs)/1000)},
            {"CtrlMode", static_cast<int>(phaseCtrlMode)},
            {"IsInCtrl", (phaseCtrlMode == STEP_CONTROL || phaseCtrlMode == LOCK_STATE) ? 1 : 0},
            {"Lag_ms", latency}
        };
        m_phaseCtlMode = phaseCtrlMode;
    }

    // 计算定周期模式下的当前相位
    inline int HKOpenApiDriver::calculatePeriodicalModeStage(const json &phaseList) {
        int stage = 0;

        // 遍历相位列表
        for (const auto &phase: phaseList) {
            // 跳过运行时间为0或等于相位长度的相位
            int runTime = phase[m_config.fieldNames.response.runTime].get<int>();
            int phaseLength = phase[m_config.fieldNames.response.phaseLength].get<int>();

            if (runTime == 0 || runTime == phaseLength) {
                continue;
            }

            stage = phase[m_config.fieldNames.response.phaseNo].get<int>();
            break;
        }

        // 如果没找到运行中的相位(相位即将切换)
        if (stage == 0) {
            std::lock_guard<std::mutex> lock(m_mutex);
            if (!m_phaseInfo.empty()) {
                // 保持上次查询到的相位
                stage = m_phaseInfo["currentPhase"];
            } else {
                // 首次查询时返回默认值1
                stage = 1;
            }
        }

        return stage;
    }

    // 根据锁定的通道查找对应的相位
    inline int HKOpenApiDriver::lookupLockPhase(const json &channelStateList) {
        int stage = 0;
        std::vector<int> lockedChannels;

        // 收集所有锁定的通道号
        for (const auto &channel : channelStateList) {
            if (channel[m_config.fieldNames.response.state] == 1) {
                lockedChannels.push_back(channel[m_config.fieldNames.response.channelNo].get<int>());
            }
        }

        if (!lockedChannels.empty()) {
            // 在lockPhaseChannelMap中查找匹配的相位
            for (const auto &entry : m_config.lockPhaseChannelMap) {
                // 检查通道集合是否完全匹配
                if (std::is_permutation(entry.second.begin(), entry.second.end(),
                                       lockedChannels.begin(), lockedChannels.end())) {
                    stage = entry.first;
                    break;
                }
            }

            // 如果没找到匹配的相位，记录日志
            if (stage == 0) {
                LOG(WARNING, "未找到与锁定通道匹配的相位");
            }
        }

        return stage;
    }
    inline void HKOpenApiDriver::reset() {
        if (getPhaseCtrlMode() == STEP_CONTROL) {
            setControlMode(CANCEL_STEP);
            LOG(INFO, "HKOPENAPIDriver set control mode to CANCEL_STEP");
        } else if (getPhaseCtrlMode() == LOCK_STATE) {
            setControlMode(UNLOCK_STATE);
            LOG(INFO, "HKOPENAPIDriver set control mode to UNLOCK_STATE");
        }
    }
}































class SignalDeviceHKOPENAPI final : public ISignalDevice{
public:
    explicit SignalDeviceHKOPENAPI(const SignalCtl &signalCtl);
    ~SignalDeviceHKOPENAPI() override;
    void controlSignal(int phase) override;
    SignalEnvState getSignalState() override;
    bool reset() override;
    bool setControlMode(int mode) override;

    // 重连接口
    bool reconnect(int waitSeconds) override;

    // 设置重连阈值
    void setReconnectThreshold(int seconds) override;

private:
    std::shared_ptr<HKOpenApi::HKOpenApiDriver> hkOpenApiDriver;
    HKOpenApi::HKOpenApiDriver::Config hkOpenApiConfig;

    // 状态监控相关
    bool inStepControlMode{false};
    int64_t stepControlModeStartTime{0};
    int stepControlModeReconnectThreshold{60}; // 在步进控制模式下多少秒后触发重连
};




#endif //SIGNALDEVICEHKOPENAPI_HPP
