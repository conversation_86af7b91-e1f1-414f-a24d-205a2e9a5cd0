#ifndef GB20999MESSAGE_HPP
#define GB20999MESSAGE_HPP

#include <vector>
#include <string>
#include <optional>
#include <variant>
#include <unordered_map>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <chrono>
#include <ctime>
#include <arpa/inet.h>
#include "utils/UDPClient.hpp"

namespace gb20999 {
class GB20999Message {
public:
    // Frame Types
    enum class FrameType : uint8_t {
        TYPE_QUERY = 0x10,
        TYPE_QUERY_REPLY = 0x20,
        TYPE_QUERY_ERROR_REPLY = 0x21,
        TYPE_SET = 0x30,
        TYPE_SET_REPLY = 0x40,
        TYPE_SET_ERROR_REPLY = 0x41,
        TYPE_BROADCAST = 0x50,
        TYPE_TRAP = 0x60,
        TYPE_HEART_SEARCH = 0x70,
        TYPE_HEART_REPLY = 0x80
    };

    // Error Status
    enum class ErrorStatus : uint8_t {
        STATUS_BAD_VALUE = 0x10,
        STATUS_WRONG_LENGTH = 0x11,
        STATUS_OVERFLOW = 0x12,
        STATUS_READ_ONLY = 0x20,
        STATUS_NULL = 0x30,
        STATUS_ERROR = 0x40,
        STATUS_CONTROL_FAIL = 0x50
    };

    // 添加打印功能
    static std::string toHexString(const std::vector<uint8_t> &data);

    static std::string toHexString(const uint8_t *data, size_t length);

    // 添加帧类型名称映射
    static std::unordered_map<uint8_t, std::string> FrameTypeNames;

    // 解析整个消息
    std::string parseMessageToString() const;

    // 解析消息头
    std::string parseHeaderToString() const;

    // 解析数据部分
    std::string parseDataToString() const;

    //
    std::string parseDataFieldToString() const;

    // 数据标识符结构
    struct DataId {
        uint8_t dataClassId;   // 数据类ID
        uint8_t objectId;      // 对象ID
        uint8_t propertyId;    // 属性ID
        uint8_t elementId;     // 元素ID

        DataId(uint8_t dClass, uint8_t obj, uint8_t prop = 0, uint8_t elem = 0)
            : dataClassId(dClass), objectId(obj), propertyId(prop), elementId(elem) {}

        // 相等比较运算符
        bool operator==(const DataId& other) const {
            return dataClassId == other.dataClassId
                && objectId == other.objectId
                && propertyId == other.propertyId
                && elementId == other.elementId;
        }
        // 不等比较运算符
        bool operator!=(const DataId& other) const {
            return !(*this == other);
        }
        // 用于容器排序等操作的小于比较运算符
        bool operator<(const DataId& other) const {
            if(dataClassId != other.dataClassId)
                return dataClassId < other.dataClassId;
            if(objectId != other.objectId)
                return objectId < other.objectId;
            if(propertyId != other.propertyId)
                return propertyId < other.propertyId;
            return elementId < other.elementId;
        }

        // 转换为数据字节流
        std::vector<uint8_t> toBytes() const {
            return {dataClassId, objectId, propertyId, elementId};
        }
    };

    // 数据域的结构定义
    struct DataField {
        uint8_t index;           // 数据值索引
        uint8_t length;          // 数据值长度
        uint8_t dataClassId;     // 数据类ID
        uint8_t objectId;        // 对象ID
        uint8_t propertyId;      // 属性ID
        uint8_t elementId;       // 元素ID
        std::vector<uint8_t> value;  // 实际数据值

        // 获取完整的数据标识
        [[nodiscard]] const DataId getDataId() const {
            return DataId{dataClassId, objectId, propertyId, elementId};
        }
    };

    // 获取解析后的数据域
    const std::vector<DataField>& getDataFields() const { return dataFields_; }



private:
    struct MessageHeader {
        uint16_t version;      // Protocol version
        uint8_t hostId;        // Host ID
        uint32_t tscId;        // Traffic Signal Controller ID
        uint8_t intersectionId;// Intersection ID
        uint8_t sequence;      // Frame sequence number
        FrameType frameType;   // Frame type
    } __attribute__((packed));

    MessageHeader header_;
    std::vector<std::vector<uint8_t>> data_; // Multiple data values

    static uint16_t calculateCRC16(const uint8_t* data, size_t length);

    // 将帧类型转换为字符串
    static std::string frameTypeToString(FrameType type);

    std::vector<DataField> dataFields_;  // 解析后的数据域
    // 解析数据域
    bool parseDataFields(const std::vector<uint8_t>& buffer, size_t& offset);

public:
    GB20999Message() : header_{} {}

    // Set message header
    void setHeader(uint8_t hostId, uint32_t tscId, uint8_t intersectionId,
                  uint8_t sequence, FrameType frameType) {
        header_.version = htons(0x0100);  // Protocol version 1.0
        header_.hostId = hostId;
        header_.tscId = htonl(tscId);
        header_.intersectionId = intersectionId;
        header_.sequence = sequence;
        header_.frameType = frameType;
    }

    // Add data value to message
    void appendData(const std::vector<uint8_t>& data) {
        data_.push_back(data);
    }

    // Pack message into bytes buffer
    [[nodiscard]] std::vector<uint8_t> packMessage() const {
        std::vector<uint8_t> buffer;

        // Calculate total length
        size_t totalLength = sizeof(MessageHeader);
        totalLength += 1; // Data count

        for(const auto& d : data_) {
            totalLength += 2; // Data index and length
            totalLength += d.size();
        }
        totalLength += 2; // CRC16

        buffer.reserve(totalLength + 2); // Add start and end bytes

        // Start byte
        buffer.push_back(0x7E);

        // Pack header
        uint16_t length = htons(totalLength);
        buffer.insert(buffer.end(), (uint8_t*)&length, ((uint8_t*)&length) + 2);
        buffer.insert(buffer.end(), (uint8_t*)&header_, ((uint8_t*)&header_) + sizeof(MessageHeader));

        // Pack data count
        buffer.push_back(static_cast<uint8_t>(data_.size()));

        // Pack data values
        for(size_t i = 0; i < data_.size(); i++) {
            buffer.push_back(static_cast<uint8_t>(i + 1)); // Data index
            buffer.push_back(static_cast<uint8_t>(data_[i].size())); // Data length
            buffer.insert(buffer.end(), data_[i].begin(), data_[i].end());
        }

        // Calculate and add CRC16
        uint16_t crc = calculateCRC16(buffer.data() + 1, buffer.size() - 1);
        buffer.push_back((crc >> 8) & 0xFF);
        buffer.push_back(crc & 0xFF);

        // End byte
        buffer.push_back(0x7D);
        LOG(DEBUG, "Packed Message: " + toHexString(buffer));
        LOG(DEBUG, "Packed Message Parsed: " + parseMessageToString());
        return buffer;
    }

    // Parse message from bytes buffer
    bool parseMessage(const std::vector<uint8_t>& buffer) {
        LOG(DEBUG, "Received Message: " + toHexString(buffer));

        if(buffer.size() < 17 || buffer[0] != 0x7E || buffer.back() != 0x7D) {
            return false;
        }

        // Check CRC16
        size_t dataLength = buffer.size() - 4; // Exclude start, CRC16 and end bytes
        uint16_t receivedCrc = (buffer[buffer.size()-3] << 8) | buffer[buffer.size()-2];
        uint16_t calculatedCrc = calculateCRC16(buffer.data() + 1, dataLength);

        if(receivedCrc != calculatedCrc) {
            return false;
        }

        // Parse header
        memcpy(&header_, buffer.data() + 3, sizeof(MessageHeader));
        //header_.version = ntohs(header_.version);
        //header_.tscId = ntohl(header_.tscId);
        //header_.intersectionId = ntohl(header_.intersectionId);

        // Parse data values
        data_.clear();
        size_t offset = 3 + sizeof(MessageHeader);
        if(!parseDataFields(buffer, offset)) {
            return false;
        }
        LOG(DEBUG, "Received Message Parsed: " + parseMessageToString());
        return true;
    }

    // Create response message
    std::unique_ptr<GB20999Message> createResponse(bool hasError = false) const {
        auto response = std::make_unique<GB20999Message>();

        FrameType responseType;
        switch(header_.frameType) {
            case FrameType::TYPE_QUERY:
                responseType = hasError ? FrameType::TYPE_QUERY_ERROR_REPLY
                                      : FrameType::TYPE_QUERY_REPLY;
                break;
            case FrameType::TYPE_SET:
                responseType = hasError ? FrameType::TYPE_SET_ERROR_REPLY
                                      : FrameType::TYPE_SET_REPLY;
                break;
            default:
                return nullptr;
        }

        response->setHeader(header_.hostId, ntohl(header_.tscId),
                          header_.intersectionId, header_.sequence, responseType);
        return response;
    }

    const MessageHeader& getHeader() const { return header_; }
    const std::vector<std::vector<uint8_t>>& getData() const { return data_; }
};

// CRC16 calculation implementation
inline uint16_t GB20999Message::calculateCRC16(const uint8_t* data, size_t length) {
    // CRC-16 polynomial: x^16 + x^12 + x^2 + 1 (0x1005)
    uint16_t crc = 0;
    while(length--) {
        crc ^= (*data++) << 8;
        for(int i = 0; i < 8; i++) {
            if(crc & 0x8000) {
                crc = (crc << 1) ^ 0x1005;
            } else {
                crc = crc << 1;
            }
        }
    }
    return crc;
}

inline std::string GB20999Message::toHexString(const std::vector<uint8_t>& data) {
    return toHexString(data.data(), data.size());
}

inline std::string GB20999Message::toHexString(const uint8_t* data, size_t length) {
    std::ostringstream oss;
    for(size_t i = 0; i < length; ++i) {
        if(i > 0) oss << " ";
        oss << "0x" << std::hex << std::setw(2) << std::setfill('0')
            << static_cast<int>(data[i]);
    }
    return oss.str();
}


// 初始化帧类型名称映射
inline std::unordered_map<uint8_t, std::string> GB20999Message::FrameTypeNames = {
    {static_cast<uint8_t>(FrameType::TYPE_QUERY), "查询"},
    {static_cast<uint8_t>(FrameType::TYPE_QUERY_REPLY), "查询应答"},
    {static_cast<uint8_t>(FrameType::TYPE_QUERY_ERROR_REPLY), "查询错误应答"},
    {static_cast<uint8_t>(FrameType::TYPE_SET), "设置"},
    {static_cast<uint8_t>(FrameType::TYPE_SET_REPLY), "设置应答"},
    {static_cast<uint8_t>(FrameType::TYPE_SET_ERROR_REPLY), "设置错误应答"},
    {static_cast<uint8_t>(FrameType::TYPE_BROADCAST), "广播"},
    {static_cast<uint8_t>(FrameType::TYPE_TRAP), "主动上报"},
    {static_cast<uint8_t>(FrameType::TYPE_HEART_SEARCH), "心跳查询"},
    {static_cast<uint8_t>(FrameType::TYPE_HEART_REPLY), "心跳应答"}
};


inline std::string GB20999Message::frameTypeToString(FrameType type) {
    auto it = FrameTypeNames.find(static_cast<uint8_t>(type));
    if(it != FrameTypeNames.end()) {
        return it->second;
    }
    return "未知类型(" + std::to_string(static_cast<uint8_t>(type)) + ")";
}

inline std::string GB20999Message::parseHeaderToString() const {
    std::ostringstream oss;

    // 解析协议版本
    uint16_t version = ntohs(header_.version);
    uint8_t majorVersion = (version >> 8) & 0xFF;
    uint8_t minorVersion = version & 0xFF;

    oss << "协议版本 " << static_cast<int>(majorVersion) << "."
        << static_cast<int>(minorVersion);

    // 解析其他字段
    oss << " 上位机ID " << static_cast<int>(header_.hostId)
        << " 信号机ID " << ntohl(header_.tscId)
        << " 路口ID " << static_cast<int>(header_.intersectionId)
        << " 帧流水号 " << static_cast<int>(header_.sequence)
        << " 帧类型 " << frameTypeToString(header_.frameType);

    return oss.str();
}

inline std::string GB20999Message::parseDataToString() const {
    std::ostringstream oss;

    oss << "数据域数量: " << data_.size() << "\n";

    for(size_t i = 0; i < data_.size(); i++) {
        const auto& data = data_[i];
        oss << "  数据[" << (i + 1) << "]:\n";

        if(data.size() >= 4) {
            // 解析数据标识符
            oss << "    数据类: " << static_cast<int>(data[0])
                << " 对象: " << static_cast<int>(data[1])
                << " 属性: " << static_cast<int>(data[2])
                << " 元素: " << static_cast<int>(data[3]) << "\n";

            // 如果有数据值，打印数据值的16进制表示
            if(data.size() > 4) {
                oss << "    数据值: ";
                for(size_t j = 4; j < data.size(); j++) {
                    if(j > 4) oss << " ";
                    oss << "0x" << std::hex << std::setw(2) << std::setfill('0')
                        << static_cast<int>(data[j]);
                }
                oss << std::dec;  // 恢复十进制输出
            }
        } else {
            // 如果数据不完整，直接打印原始数据
            oss << "    原始数据: " << toHexString(data);
        }
        oss << "\n";
    }

    return oss.str();
}

inline std::string GB20999Message::parseMessageToString() const {
    std::ostringstream oss;

    // 报文头部解析
    oss << "[消息头] " << parseHeaderToString() << "\n";

    // 数据部分解析
    oss << "[数据部分]\n" << parseDataToString();

    return oss.str();
}

inline bool GB20999Message::parseDataFields(const std::vector<uint8_t>& buffer, size_t& offset) {
    // 清空已有数据
    data_.clear();
    dataFields_.clear();

    if(offset >= buffer.size()) return false;

    // 读取数据值数量
    uint8_t dataCount = buffer[offset++];

    // 解析每个数据域
    for(uint8_t i = 0; i < dataCount; i++) {
        if(offset + 2 > buffer.size()) return false;  // 至少需要索引和长度字节

        // 读取索引和长度
        uint8_t index = buffer[offset++];
        uint8_t length = buffer[offset++];

        // 验证索引
        if(index != i + 1) {
            return false;
        }

        // 验证长度
        if(offset + length > buffer.size()) {
            return false;
        }

        // 保存原始数据
        std::vector<uint8_t> rawData(buffer.begin() + offset,
                                   buffer.begin() + offset + length);
        data_.push_back(std::move(rawData));

        // 解析数据域结构
        if(length >= 4) {  // 至少要有数据标识的4个字节
            DataField field;
            field.index = index;
            field.length = length;
            field.dataClassId = buffer[offset];
            field.objectId = buffer[offset + 1];
            field.propertyId = buffer[offset + 2];
            field.elementId = buffer[offset + 3];

            // 提取数据值
            if(length > 4) {
                field.value.assign(buffer.begin() + offset + 4,
                                 buffer.begin() + offset + length);
            }

            dataFields_.push_back(std::move(field));
        }

        offset += length;
    }
    return true;
}

inline std::string GB20999Message::parseDataFieldToString() const {
    std::ostringstream oss;
    oss << "数据域数量: " << dataFields_.size() << "\n";
    for(const auto& field : dataFields_) {
        // 打印数据域基本信息
        oss << "数据域[" << static_cast<int>(field.index) << "]:\n"
            << "  数据长度: " << static_cast<int>(field.length) << " 字节\n"
            << "  数据标识: "
            << static_cast<int>(field.dataClassId) << "."
            << static_cast<int>(field.objectId) << "."
            << static_cast<int>(field.propertyId) << "."
            << static_cast<int>(field.elementId) << "\n";

        // 打印数据值（十六进制格式）
        if(!field.value.empty()) {
            oss << "  数据值: ";
            for(uint8_t byte : field.value) {
                oss << "0x" << std::hex << std::setw(2) << std::setfill('0')
                    << static_cast<int>(byte) << " ";
            }
            oss << std::dec << "\n";  // 恢复十进制输出
        } else {
            oss << "  数据值: <空>\n";
        }
        oss << "\n";
    }
    return oss.str();
}




    // 数据标识符结构
    /*
struct DataIdentifier {
    uint8_t dataClassId;   // 数据类ID
    uint8_t objectId;      // 对象ID
    uint8_t propertyId;    // 属性ID
    uint8_t elementId;     // 元素ID

    DataIdentifier(uint8_t dClass, uint8_t obj, uint8_t prop = 0, uint8_t elem = 0)
        : dataClassId(dClass), objectId(obj), propertyId(prop), elementId(elem) {}

    // 转换为数据字节流
    std::vector<uint8_t> toBytes() const {
        return {dataClassId, objectId, propertyId, elementId};
    }
};
*/

// 时间日期结构
struct DateTime {
    uint16_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;

    static DateTime fromBytes(const std::vector<uint8_t>& data) {
        if(data.size() < 7) throw std::runtime_error("Invalid DateTime data");
        DateTime dt;
        dt.year = (data[0] << 8) | data[1];
        dt.month = data[2];
        dt.day = data[3];
        dt.hour = data[4];
        dt.minute = data[5];
        dt.second = data[6];
        return dt;
    }

    std::vector<uint8_t> toBytes() const {
        std::vector<uint8_t> result;
        result.push_back((year >> 8) & 0xFF);
        result.push_back(year & 0xFF);
        result.push_back(month);
        result.push_back(day);
        result.push_back(hour);
        result.push_back(minute);
        result.push_back(second);
        return result;
    }

    std::string toString() const {
        char buffer[32];
        snprintf(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d",
                year, month, day, hour, minute, second);
        return std::string(buffer);
    }
};

    // 运行模式结果数据
    struct RunModeData {
        enum class Mode : uint8_t {
            CENTER_CONTROL = 0x10,
            CENTER_TIMETABLE = 0x11,
            CENTER_OPTIMIZATION = 0x12,
            CENTER_COORDINATION = 0x13,
            CENTER_ADAPTIVE = 0x14,
            CENTER_MANUAL = 0x15,
            LOCAL_CONTROL = 0x20,
            LOCAL_FIXCYCLE = 0x21,
            LOCAL_VA = 0x22,
            LOCAL_COORDINATION = 0x23,
            LOCAL_ADAPTIVE = 0x24,
            LOCAL_MANUAL = 0x25,
            SPECIAL_CONTROL = 0x30,
            SPECIAL_FLASH = 0x31,
            SPECIAL_ALLRED = 0x32,
            SPECIAL_ALLOFF = 0x33
        };

        Mode mode;

        static std::string modeToString(Mode mode) {
            switch(mode) {
                case Mode::CENTER_CONTROL: return "Center Control";
                case Mode::CENTER_TIMETABLE: return "Center Timetable";
                case Mode::CENTER_OPTIMIZATION: return "Center Optimization";
                case Mode::CENTER_COORDINATION: return "Center Coordination";
                case Mode::CENTER_ADAPTIVE: return "Center Adaptive";
                case Mode::CENTER_MANUAL: return "Center Manual";
                case Mode::LOCAL_CONTROL: return "Local Control";
                case Mode::LOCAL_FIXCYCLE: return "Local Fixed Cycle";
                case Mode::LOCAL_VA: return "Local VA";
                case Mode::LOCAL_COORDINATION: return "Local Coordination";
                case Mode::LOCAL_ADAPTIVE: return "Local Adaptive";
                case Mode::LOCAL_MANUAL: return "Local Manual";
                case Mode::SPECIAL_CONTROL: return "Special Control";
                case Mode::SPECIAL_FLASH: return "Special Flash";
                case Mode::SPECIAL_ALLRED: return "Special All Red";
                case Mode::SPECIAL_ALLOFF: return "Special All Off";
                default: return "Unknown";
            }
        }

        std::string toString() const {
            return modeToString(mode);
        }
    };


    // 当前方案信息 (13.2.3.x)
    struct PlanInfo {
        uint8_t planId;           // 当前方案号

        std::string toString() const {
            std::ostringstream oss;
            oss << "当前方案号: " << static_cast<int>(planId);
            return oss.str();
        }
    };

    // 当前相位信息 (13.2.4.x)
    struct PhaseInfo {
        uint8_t currentStage;     // 当前相位阶段

        std::string toString() const {
            std::ostringstream oss;
            oss << "当前相位阶段: " << static_cast<int>(currentStage);
            return oss.str();
        }
    };
    // 运行状态表数据结构
    struct RunStatusInfo {
        uint8_t intersectionId;       // 路口序号
        RunModeData::Mode runMode;        // 运行模式
        uint8_t currentPlan;          // 当前方案
        uint8_t currentPhaseStage;    // 当前相位阶段

        std::string toString() const {
            std::ostringstream oss;
            oss << "路口序号: " << static_cast<int>(intersectionId) << "\n"
                << "运行模式: " << RunModeData::modeToString(runMode) << "\n"
                << "当前方案号: " << static_cast<int>(currentPlan) << "\n"
                << "当前相位阶段: " << static_cast<int>(currentPhaseStage);
            return oss.str();
        }
    };

    // 相位阶段状态数据结构
    struct PhaseStageStatus {
        enum class Status : uint8_t {
            NOT_OF_WAY = 0x10,    // 相位阶段未放行
            ON_THE_WAY = 0x20,    // 相位阶段正在放行
            TRANSITION = 0x30     // 相位阶段过渡
        };

        uint8_t phaseStageNumber;     // 相位阶段编号
        Status status;                 // 相位阶段状态
        uint16_t elapsedTime;         // 已运行时间(秒)
        uint16_t remainingTime;       // 剩余时间(秒)
        bool hasRemainingTime;        // 是否有剩余时间信息

        static std::string statusToString(Status status) {
            switch(status) {
                case Status::NOT_OF_WAY: return "相位阶段未放行";
                case Status::ON_THE_WAY: return "相位阶段正在放行";
                case Status::TRANSITION: return "相位阶段过渡";
                default: return "未知状态";
            }
        }

        std::string toString() const {
            std::ostringstream oss;
            oss << "相位阶段编号: " << static_cast<int>(phaseStageNumber)
                << " 相位阶段状态: " << statusToString(status)
                << " 已运行时间: " << elapsedTime << "秒";
            if(hasRemainingTime) {
                oss << " 剩余时间: " << remainingTime << "秒";
            }
            return oss.str();
        }
    };

// 查询结果类型
using QueryResult = std::variant<
    std::monostate,       // 空结果
    DateTime,             // 日期时间
    std::string,          // 字符串
    uint8_t,              // 单字节整数
    uint16_t,             // 双字节整数
    uint32_t,             // 四字节整数
    std::vector<uint8_t>,  // 原始字节数据
    RunModeData,
    PlanInfo,
    PhaseInfo,
    RunStatusInfo,
    PhaseStageStatus
>;

// 查询基类
class QueryBase {
protected:
    GB20999Message::DataId identifier_;

public:
    explicit QueryBase(const GB20999Message::DataId& id) : identifier_(id) {}
    virtual ~QueryBase() = default;

    // 获取查询用的数据标识符
    const GB20999Message::DataId& getIdentifier() const { return identifier_; }

    // 解析响应数据
    virtual QueryResult parseResponse(const std::vector<uint8_t> &data) const {
        return std::monostate{};
    };
    // 只解析数据值部分
    virtual QueryResult parseValue(const std::vector<uint8_t>& value) const = 0;
};

    // 相位阶段状态查询类
    class PhaseStageStatusQuery : public QueryBase {
    public:
        explicit PhaseStageStatusQuery(uint8_t intersectionId = 1)
            : QueryBase({6, 3, 0, intersectionId})  // 数据类6.对象3.属性0.元素ID=交叉口ID
        {}

        QueryResult parseValue(const std::vector<uint8_t>& value) const override {
            if(value.size() < 4) {  // 至少需要相位阶段号、状态、已运行时间(2字节)
                return std::monostate{};
            }

            PhaseStageStatus status;
            status.phaseStageNumber = value[0];
            status.status = static_cast<PhaseStageStatus::Status>(value[1]);
            status.elapsedTime = (value[2] << 8) | value[3];

            // 检查是否有剩余时间数据
            status.hasRemainingTime = value.size() >= 6;
            if(status.hasRemainingTime) {
                status.remainingTime = (value[4] << 8) | value[5];
            }

            return status;
        }
    };





// 运行模式查询类
class RunModeQuery : public QueryBase {
private:
    uint8_t intersection_id_;

public:
    explicit RunModeQuery(uint8_t intersectionId = 1)
        : QueryBase({13, 2, 2, intersectionId})  // 数据类13.对象2.属性2.元素ID=交叉口ID
        , intersection_id_(intersectionId)
    {}

    QueryResult parseResponse(const std::vector<uint8_t> &data) const override {
        if(data.empty()) {
            throw std::runtime_error("Empty response data");
        }

        RunModeData result;
        result.mode = static_cast<RunModeData::Mode>(data[4]);
        return result;
    }

    QueryResult parseValue(const std::vector<uint8_t>& value) const override {
        if (value.empty()) {
            return std::monostate{};
        }
        RunModeData curMode;
        curMode.mode = static_cast<RunModeData::Mode>(value[0]);
        return curMode;
    }

    uint8_t getIntersectionId() const { return intersection_id_; }
};

    // 当前方案查询 (13.2.3.x)
    class CurrentPlanQuery : public QueryBase {
    public:
        explicit CurrentPlanQuery(uint8_t intersectionId = 1)
            : QueryBase({13, 2, 3, intersectionId})  // 数据类13.对象2.属性3.元素ID=交叉口ID
        {}

        QueryResult parseResponse(const std::vector<uint8_t>& data) const override {
            if(data.empty()) {
                return std::monostate{};
            }

            PlanInfo plan;
            plan.planId = data[0];  // 只返回方案号
            return plan;
        }
        QueryResult parseValue(const std::vector<uint8_t>& value) const override {
            if(value.empty()) {
                return std::monostate{};
            }
            PlanInfo plan;
            plan.planId = value[0];
            return plan;
        }
    };




    // 当前相位查询 (13.2.4.x)
    class CurrentPhaseQuery : public QueryBase {
    public:
        explicit CurrentPhaseQuery(uint8_t intersectionId = 1)
            : QueryBase({13, 2, 4, intersectionId})  // 数据类13.对象2.属性4.元素ID=交叉口ID
        {}

        QueryResult parseResponse(const std::vector<uint8_t>& data) const override {
            if(data.empty()) {
                return std::monostate{};
            }

            PhaseInfo phase;
            phase.currentStage = data[0];  // 只返回当前相位阶段
            return phase;
        }
        QueryResult parseValue(const std::vector<uint8_t>& value) const override {
            if(value.empty()) {
                return std::monostate{};
            }
            PhaseInfo phase;
            phase.currentStage = value[0];
            return phase;
        }
    };


    // 运行状态查询 - 13.2.0.x
class RunStatusQuery : public QueryBase {
public:
    explicit RunStatusQuery(uint8_t intersectionId = 1)
        : QueryBase({13, 2, 0, intersectionId})  // 数据类13.对象2.属性0.元素ID=交叉口ID
    {}

    QueryResult parseValue(const std::vector<uint8_t>& value) const override {
        if(value.size() < 4) {  // 至少需要4个字节
            return std::monostate{};
        }

        RunStatusInfo status;
        status.intersectionId = value[0];        // 路口序号
        status.runMode = static_cast<RunModeData::Mode>(value[1]);  // 运行模式
        status.currentPlan = value[2];           // 当前方案
        status.currentPhaseStage = value[3];     // 当前相位阶段
        return status;
    }
};





// 查询执行器类
class QueryExecutor {
private:
    std::shared_ptr<UDPClient> client_;
    uint8_t hostId_;
    uint32_t tscId_;
    uint8_t intersectionId_;
    uint8_t sequence_;

public:
    QueryExecutor(std::shared_ptr<UDPClient> client,
                 uint8_t hostId, uint32_t tscId, uint8_t intersectionId = 0)
        : client_(client)
        , hostId_(hostId)
        , tscId_(tscId)
        , intersectionId_(intersectionId)
        , sequence_(0)
    {}

    template<typename QueryType, typename... Args>
    std::optional<QueryResult> execute(Args&&... args) {
        QueryType query(std::forward<Args>(args)...);
        GB20999Message queryMsg;

        // 设置消息头
        queryMsg.setHeader(
            hostId_,
            tscId_,
            intersectionId_,
            ++sequence_,
            GB20999Message::FrameType::TYPE_QUERY
        );

        // 添加查询数据
        queryMsg.appendData(query.getIdentifier().toBytes());

        // 发送查询
        auto packedMsg = queryMsg.packMessage();
        if(!client_->send(packedMsg.data(), packedMsg.size())) {
            return std::nullopt;
        }

        // 接收响应
        std::vector<uint8_t> recvBuffer(1024);
        size_t recvSize = client_->receive(recvBuffer.data(), recvBuffer.size(), 5000);
        if(recvSize == 0) {
            return std::nullopt;
        }
        recvBuffer.resize(recvSize);

        // 解析响应消息
        GB20999Message responseMsg;
        if(!responseMsg.parseMessage(recvBuffer)) {
            return std::nullopt;
        }

        // 检查响应类型
        const auto& header = responseMsg.getHeader();
        if(header.frameType == GB20999Message::FrameType::TYPE_QUERY_ERROR_REPLY) {
            return std::nullopt;
        }

        if(header.frameType != GB20999Message::FrameType::TYPE_QUERY_REPLY) {
            return std::nullopt;
        }

        // 解析响应数据
        /*
        const auto& data = responseMsg.getData();
        if(data.empty()) {
            return std::nullopt;
        }
        */
        const auto& dataFields = responseMsg.getDataFields();
        if (dataFields.empty()) {
            return std::nullopt;
        }

        // 验证数据标识是否匹配
        const auto& field = dataFields[0];  // 通常我们只处理第一个数据域
        if(field.getDataId() != query.getIdentifier()) {
            return std::nullopt;
        }

        // 解析数据值


        try {
            //return query.parseResponse(data[0]);
            return query.parseValue(field.value);
        } catch(const std::exception&) {
            return std::nullopt;
        }
    }
};

    // 设置结果
    using SetResult = std::variant<
        std::monostate,     // 空结果
        bool,               // 设置是否成功
        std::string         // 错误信息
    >;

    // 设置操作的基类
    class SetterBase {
    protected:
        GB20999Message::DataId id_;
        std::vector<uint8_t> value_;

    public:
        SetterBase(GB20999Message::DataId id) : id_(id) {}
        virtual ~SetterBase() = default;

        // 获取设置用的数据标识符
        const GB20999Message::DataId& getId() const { return id_; }

        // 获取设置的值
        const std::vector<uint8_t>& getValue() const { return value_; }

        // 解析设置响应
        virtual SetResult parseResponse(const std::vector<uint8_t>& data) const {
            if(data.empty()) return std::monostate{};
            return data[4] == 0;  // 通常0表示成功
        }
        virtual SetResult parseValue(const std::vector<uint8_t>& value) const {
            if(value.empty()) return std::monostate{};
            return value[0] == 0;
        };
    };

    // 运行模式设置
    class ModeSetter : public SetterBase {
    public:
        enum class Mode : uint8_t {
            CENTER_CONTROL = 0x10,
            CENTER_TIMETABLE = 0x11,
            CENTER_OPTIMIZATION = 0x12,
            CENTER_COORDINATION = 0x13,
            CENTER_ADAPTIVE = 0x14,
            CENTER_MANUAL = 0x15,
            LOCAL_CONTROL = 0x20,
            LOCAL_FIXCYCLE = 0x21,
            LOCAL_VA = 0x22,
            LOCAL_COORDINATION = 0x23,
            LOCAL_ADAPTIVE = 0x24,
            LOCAL_MANUAL = 0x25,
            SPECIAL_CONTROL = 0x30,
            SPECIAL_FLASH = 0x31,
            SPECIAL_ALLRED = 0x32,
            SPECIAL_ALLOFF = 0x33
        };

        ModeSetter(uint8_t intersectionId, Mode mode)
            : SetterBase({17, 1, 4, intersectionId})  // 数据类17.对象1.属性4.元素ID=交叉口ID
        {
            value_ = {static_cast<uint8_t>(mode)};
        }
    };

    // 相位设置
    class PhaseSetter : public SetterBase {
    public:
        PhaseSetter(uint8_t intersectionId, uint8_t phaseId)
            : SetterBase({17, 1, 2, intersectionId})  // 数据类17.对象1.属性2.元素ID=交叉口ID
        {
            value_ = {phaseId};
        }
    };

// 设置执行器
class SetterExecutor {
private:
    std::shared_ptr<UDPClient> client_;
    uint8_t hostId_;
    uint32_t tscId_;
    uint8_t intersectionId_;
    uint8_t sequence_;

public:
    SetterExecutor(std::shared_ptr<UDPClient> client,
                   uint8_t hostId,
                   uint32_t tscId,
                   uint8_t intersectionId = 0)
        : client_(client)
        , hostId_(hostId)
        , tscId_(tscId)
        , intersectionId_(intersectionId)
        , sequence_(0)
    {}

    // 执行设置操作
    template<typename SetterType, typename... Args>
    std::optional<SetResult> execute(Args&&... args) {
        SetterType setter(std::forward<Args>(args)...);
        GB20999Message setMsg;

        // 设置消息头
        setMsg.setHeader(
            hostId_,
            tscId_,
            intersectionId_,
            ++sequence_,
            GB20999Message::FrameType::TYPE_SET
        );

        // 添加设置数据
        std::vector<uint8_t> setData;
        // 添加数据标识符
        auto id = setter.getId();
        setData.insert(setData.end(), {id.dataClassId, id.objectId, id.propertyId, id.elementId});
        // 添加设置值
        const auto& value = setter.getValue();
        setData.insert(setData.end(), value.begin(), value.end());
        setMsg.appendData(setData);

        // 发送设置消息
        auto packed = setMsg.packMessage();
        if(!client_->send(packed.data(), packed.size())) {
            return std::nullopt;
        }

        // 接收响应
        std::vector<uint8_t> recvBuffer(1024);
        size_t recvSize = client_->receive(recvBuffer.data(), recvBuffer.size());
        if(recvSize == 0) {
            return std::nullopt;
        }
        recvBuffer.resize(recvSize);

        // 解析响应
        GB20999Message responseMsg;
        if(!responseMsg.parseMessage(recvBuffer)) {
            return std::nullopt;
        }

        // 检查响应类型
        const auto& header = responseMsg.getHeader();
        if(header.frameType == GB20999Message::FrameType::TYPE_SET_ERROR_REPLY) {
            return std::string("设置失败");
        }

        if(header.frameType != GB20999Message::FrameType::TYPE_SET_REPLY) {
            return std::nullopt;
        }

        // 解析响应数据
        const auto& data = responseMsg.getData();
        if(data.empty()) {
            return std::nullopt;
        }

        return setter.parseResponse(data[0]);
    }
};

// 格式化工具
struct SetResultVisitor {
    template<typename T>
    static std::string toString(const T& value) {
        if constexpr (std::is_same_v<T, std::monostate>) {
            return "空结果";
        }
        else if constexpr (std::is_same_v<T, bool>) {
            return value ? "设置成功" : "设置失败";
        }
        else if constexpr (std::is_same_v<T, std::string>) {
            return value;
        }
        else {
            return "未知类型";
        }
    }
};

} // namespace gb20999

#endif // GB20999MESSAGE_HPP
