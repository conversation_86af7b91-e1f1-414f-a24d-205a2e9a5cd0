#include "SignalMachine.hpp"
#include <csignal>

#include "components/InfoNatsMsg.hpp"
#include "core/ConfigManager.hpp"
#include "SignalDevices/SignalDeviceFactory.hpp"
#include "utils/IpAddress.hpp"


extern volatile sig_atomic_t gStopFlag;
extern NatsClient &gNatsClient;
extern std::string gIntersectionCode;

void SignalMachine::sendAlgErrorNotification(const int algErrCount) {
    NatsDataMessage natsMsg;
    natsMsg.dataType = 0;
    natsMsg.dataDesc = "Alg return phase error more than threshold times";
    natsMsg.dataNode = "STN";
    std::vector<NatsDataItem> natsDataItems;
    NatsDataItem errItem = {
        gIntersectionCode + "_Alg_return_phase_error",
        std::to_string(algErrCount),
        "alg return phase error count."
    };
    natsDataItems.push_back(errItem);
    natsMsg.dataItems = natsDataItems;
    NotifyStatsService::sendStateMessage(gNatsClient, natsMsg);
}


void SignalMachine::controlSignals(int phase) {
    if (signalDevice == nullptr) {
        LOG(ERROR, "Signal Device NOT FOUND.");
    }
    std::lock_guard<std::mutex> lock(signalDeviceMutex);
    auto curDeviceState = signalDevice->getCurDeviceState();
    auto tarDeviceState = signalDevice->getTarDeviceState();
    //if phase not -1, reset algErrCount to 0;
    if (phase != -1) {
        algErrCount = 0;
    } else {
        ++algErrCount;
        // 提前检查错误次数
        if (curDeviceState != ISignalDevice::DeviceState::MONITORING &&
            algErrCount >= std::stoi(configData.algPhaseErrorThreshold)) {
            signalDevice->deviceState.changeTargetState(ISignalDevice::DeviceState::MONITORING);
            signalDevice->reset();
            LOG(ERROR, "SignalMachine: alg phase error {} >= {}, reset signal Device.", algErrCount,
                std::stoi(configData.algPhaseErrorThreshold));
            sendAlgErrorNotification(algErrCount);
            return; // 直接返回，不执行后续的相位控制
        }
        return; // phase 为 -1 时直接返回，不执行相位控制
    }

    if (signalDeviceInitialized) {
        LOG(INFO, "SignalMachine: Controlling traffic signals from alg {}, currentSignalMachineMode {}, localip {}",
            phase,
            ISignalDevice::toString(curDeviceState), NetworkInterface::getDefaultInterfaceIP());

        if (tarDeviceState == ISignalDevice::DeviceState::MONITORING) {
            LOG(INFO, "SignalMachine: Skip control - targetSignalMachineMode is set to monitoring");
            return;
        }

        if (curDeviceState != ISignalDevice::DeviceState::CONTROLABLE &&
            curDeviceState != ISignalDevice::DeviceState::TRANSITIONTOCONTROLABLE) {
            LOG(INFO, "SignalMachine: Skip control - currentSignalMachineMode is not controlable");
            return;
        }

        // 如果相位没有变化，记录日志并返回
        if (curDeviceState == ISignalDevice::DeviceState::CONTROLABLE &&
            (phase == lastAlgPhase || phase == signalDevice->deviceState.currentPhase) &&
            signalDevice->deviceState.currentPhaseTime < durationInterval) {
            LOG(WARNING,
                "SignalMachine: Skip control - algphase:{} lastPhase:{} currentPhase:{} targetPhase:{}",
                phase, lastAlgPhase, signalDevice->deviceState.currentPhase,
                signalDevice->deviceState.targetPhase);
            return;
        }

        // 检查上一次相位切换是否完成
        if (curDeviceState == ISignalDevice::DeviceState::CONTROLABLE &&
            signalDevice->deviceState.targetPhase != 0 &&
            signalDevice->deviceState.targetPhase != signalDevice->deviceState.currentPhase) {
            LOG(WARNING, "Last phase transition not complete - target:{} current:{}",
                signalDevice->deviceState.targetPhase, signalDevice->deviceState.currentPhase);
            return;
        }

        // 执行相位控制
        signalDevice->controlSignal(phase);
        lastAlgPhase = phase;
        signalDevice->deviceState.targetPhase = phase;
        LOG(INFO, "SignalMachine: Control Signal to phase: {}", phase);

        if (algErrCount >= std::stoi(configData.algPhaseErrorThreshold)) {
            signalDevice->deviceState.changeTargetState(ISignalDevice::DeviceState::MONITORING);
            signalDevice->reset();
            LOG(ERROR, "SignalMachine: alg phase error, reset signal Device.");
            sendAlgErrorNotification(algErrCount);
        }
    } else {
        LOG(ERROR, "controlSignals: signalDevice not Initiialized!");
    }
}

void SignalMachine::update(ISubject *subject, const std::string &message) {
    std::lock_guard<std::mutex> lock(signalDeviceMutex);
    if (subject->getType() == static_cast<int>(ComponentsType::RedisDataProvider)) {
        LOG(DEBUG, "SignalMachine received update from RedisDataProvider" + message);
        signalCtl->fromJson(message);
        durationInterval = signalCtl->durationInterval;
        isInControl = signalCtl->isInControl;
        if (!signalDeviceInitialized && signalDevice == nullptr) {
            signalDevice = SignalDeviceFactory::createSignalDevice(*signalCtl);
            isStartControl = signalCtl->isStartControl;
            // 根据isStartControl标志决定初始控制状态
            if (!isStartControl) {
                // 如果启动时配置为不控制，强制设置为监控模式并更新Redis配置
                isInControl = false;
                updateRedisSignalConfig(false);
                resetSignalStates();
                LOG(INFO,
                    "SignalMachine initialized with isStartControl=false, forced isInControl to false and updated Redis.");
            } else {
                LOG(INFO, "SignalMachine initialized with isStartControl=true, isInControl={} from Redis.",
                    isInControl);
            }
            signalDeviceInitialized = true;
        }
    }

    if (signalDeviceInitialized && signalDevice != nullptr && 0 == algErrCount) {
        switch (signalDevice->getCurDeviceState()) {
            case ISignalDevice::DeviceState::MONITORING:
            case ISignalDevice::DeviceState::TRANSITIONTOMONITORING:
                if (isInControl) {
                    if (signalDevice->getTarDeviceState() != ISignalDevice::DeviceState::CONTROLABLE) {
                        signalDevice->setControlMode(1);
                        signalDevice->deviceState.changeTargetState(ISignalDevice::DeviceState::CONTROLABLE);
                        LOG(INFO, "SignalMachine Set Control Mode to controling.");
                    }
                    else {
                        LOG(INFO, "SignalMachine Target Control Mode already set to controling.");
                    }
                }
                break;
            case ISignalDevice::DeviceState::CONTROLABLE:
                if (!isInControl) {
                    resetSignalStates();
                    LOG(INFO, "IsInControl Not Setted, transfer to Monintor.");
                }
            default:
                break;
        }
    }
    sendControlModeStatusMessage();
}


void SignalMachine::sendControlModeStatusMessage() const {
    NatsDataMessage natsDataMsg;
    natsDataMsg.dataDesc = "Signal Device Config State";
    natsDataMsg.dataNode = "STN";
    std::vector<NatsDataItem> natsDataItems;
    NatsDataItem currentModeItem = {
        gIntersectionCode + "_STNControlMode", std::to_string(isInControl),
        "Current Control Mode Setting"
    };
    natsDataItems.push_back(currentModeItem);
    natsDataMsg.dataItems = natsDataItems;
    NotifyStatsService::sendStateMessage(gNatsClient, natsDataMsg);
}


std::string SignalMachine::handleUpstreamControlMessage(const std::string &subject, const std::string &msg) {
    LOG(INFO, "received msg on callback: " + msg);
    if (!gIntersectionCode.empty()) {
        ControlMessage controlmsg = ControlMessage::fromString(msg);
        if (controlmsg.getCode() == gIntersectionCode || controlmsg.getCode() ==
            NetworkInterface::getDefaultInterfaceIP()) {
            if (controlmsg.getControl() == 0) {
                LOG(INFO, "received release control message from upstream, will reset signal machine.");
                updateRedisSignalConfig(false); // 同步更新Redis配置
                if (signalDevice) {
                    resetSignalStates();
                }
                return "release control " + gIntersectionCode;
            }
            if (controlmsg.getControl() == 1) {
                LOG(INFO, "received begin control message from upstream.");
                updateRedisSignalConfig(true); // 同步更新Redis配置
                return "begin control " + gIntersectionCode;
            }
        } else {
            return "received msg not belong to me: " + gIntersectionCode;
        }
    }
    return "msg received, no action taken. intersection code is empty.";
}


void SignalMachine::querySignalState() {
    while (!gStopFlag) {
        try {
            if (signalDeviceInitialized && signalDevice != nullptr) {
                SignalEnvState ses;
                ses = signalDevice->getSignalState();
                ses.updatePhaseInfo(*signalCtl);
                std::string ses_str = ses.toJson().dump();
                LOG(INFO, "SignalMachine query signal state " + ses_str);
                notifyObservers(ses_str, static_cast<int>(ComponentsType::FlowControlModule));
                currentSignalCtlState = ses.signalCtlStatus;
                sendSignalStateStatusMessage(ses);
            }
        } catch (const std::exception &e) {
            LOG(ERROR, "SignalMachine query signal state error: " + std::string(e.what()));
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(signalCtl->tscPollingPeriod_ms));
    }
}


// 新增辅助方法
void SignalMachine::sendSignalStateStatusMessage(const SignalEnvState &ses) const {
    NatsDataMessage natsDataMsg;
    natsDataMsg.dataDesc = "Signal Device State";
    natsDataMsg.dataNode = "STN";
    std::vector<NatsDataItem> natsDataItems;

    NatsDataItem signalDeviceStateItem = {
        gIntersectionCode + "_SignalDeviceState",
        ISignalDevice::toString(signalDevice->getCurDeviceState()),
        "Signal Device State"
    };
    natsDataItems.push_back(signalDeviceStateItem);

    NatsDataItem ModeItem = {
        gIntersectionCode + "_SignalDeviceIsInControl",
        std::to_string(ses.signalCtlStatus),
        "Signal Device Is InControl."
    };
    natsDataItems.push_back(ModeItem);

    NatsDataItem PhaseItem = {
        gIntersectionCode + "_SignalDeviceCurrentPhase",
        std::to_string(ses.currentPhase),
        "Signal Device currentPhase"
    };
    natsDataItems.push_back(PhaseItem);

    NatsDataItem PhaseTimeItem = {
        gIntersectionCode + "_SignalDeviceCurrentPhaseTime",
        std::to_string(ses.phaseTime),
        "Signal Device current Phase Time"
    };
    natsDataItems.push_back(PhaseTimeItem);

    NatsDataItem PlanItem = {
        gIntersectionCode + "_SignalDeviceCurrentPlan",
        ses.currentPlan,
        "Signal Device current Plan"
    };
    natsDataItems.push_back(PlanItem);

    natsDataMsg.dataItems = natsDataItems;
    NotifyStatsService::sendStateMessage(gNatsClient, natsDataMsg);
}


void SignalMachine::resetSignalStates() {
    //if signalDevice is not null, call the reset method.
    if (signalDevice != nullptr && signalDevice->getCurDeviceState() != ISignalDevice::DeviceState::UNINITIALIZED) {
        signalDevice->deviceState.changeTargetState(ISignalDevice::DeviceState::MONITORING);
        signalDevice->reset();
    }
}

bool SignalMachine::reconnectSignalDevice(int waitSeconds) {
    std::lock_guard<std::mutex> lock(signalDeviceMutex);
    if (signalDevice != nullptr) {
        LOG(INFO, "SignalMachine: Reconnecting signal device with wait time {} seconds", waitSeconds);
        return signalDevice->reconnect(waitSeconds);
    }
    LOG(ERROR, "SignalMachine: Cannot reconnect - signal device is null");
    return false;
}

void SignalMachine::setSignalDeviceReconnectThreshold(int seconds) {
    std::lock_guard<std::mutex> lock(signalDeviceMutex);
    if (signalDevice != nullptr) {
        LOG(INFO, "SignalMachine: Setting signal device reconnect threshold to {} seconds", seconds);
        signalDevice->setReconnectThreshold(seconds);
    } else {
        LOG(ERROR, "SignalMachine: Cannot set reconnect threshold - signal device is null");
    }
}

SignalMachine::SignalMachine() {
    signalCtl = std::make_shared<SignalCtl>();

    // 初始化Redis客户端
    try {
        redisClient_ = std::make_shared<RedisClient>(
            configData.localRedisIp,
            std::stoi(configData.localRedisPort),
            configData.localRedisPwd
        );
        LOG(INFO, "SignalMachine Redis client initialized successfully");
    } catch (const std::exception& e) {
        LOG(ERROR, "Failed to initialize Redis client in SignalMachine: {}", e.what());
        redisClient_ = nullptr;
    }

    gNatsClient.subscribeWithReply("manual_control_topic." + gIntersectionCode,
                                   [&](const std::string &subject, const std::string &msg) {
                                       return handleUpstreamControlMessage(subject, msg);
                                   });
    gNatsClient.subscribeWithReply("manual_control_topic", [&](const std::string &subject, \
        const std::string &msg) {
        return handleUpstreamControlMessage(subject, msg);
    });
}

SignalMachine::~SignalMachine() {
    resetSignalStates();
    LOG(INFO, "SignalMachine destroyed.");
}

std::string SignalMachine::getSignalConfigKey() const {
    return configData.signalConfigKey + ":" + gIntersectionCode;
}

void SignalMachine::updateRedisSignalConfig(bool isInControlValue) {
    if (!redisClient_) {
        LOG(ERROR, "Redis client not initialized, cannot update signal config");
        return;
    }

    if (gIntersectionCode.empty()) {
        LOG(ERROR, "Intersection code is empty, cannot update signal config");
        return;
    }

    try {
        std::string signalConfigKey = getSignalConfigKey();

        // 读取当前Redis中的配置
        auto currentConfig = redisClient_->get(signalConfigKey);
        if (!currentConfig.has_value()) {
            LOG(ERROR, "Failed to read current signal config from Redis, key: {}", signalConfigKey);
            return;
        }

        // 解析JSON配置
        json configJson = json::parse(currentConfig.value());

        // 更新isInControl字段
        if (configJson.contains("signalCtl")) {
            configJson["signalCtl"]["isInControl"] = isInControlValue ? 1 : 0;

            // 写回Redis
            redisClient_->set(signalConfigKey, configJson.dump());

            LOG(INFO, "Successfully updated Redis signal config, isInControl: {}, key: {}",
                isInControlValue, signalConfigKey);
        } else {
            LOG(ERROR, "signalCtl section not found in Redis config");
        }
    } catch (const json::parse_error& e) {
        LOG(ERROR, "JSON parse error when updating Redis signal config: {}", e.what());
    } catch (const std::exception& e) {
        LOG(ERROR, "Exception when updating Redis signal config: {}", e.what());
    }
}
