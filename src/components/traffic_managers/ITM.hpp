#ifndef STN_ITM_HPP
#define STN_ITM_HPP
#include "core/interfaces/ITrafficManager.hpp"
#include "utils/Logger.hpp"

class ITM : public ITrafficManager {
public:
    void manageTraffic() override {
        LOG(INFO, "ITM: Managing traffic");
    }

    void update(ISubject* subject, const std::string& message) override {
        LOG(INFO, "ITM received update: {}, from {}", message.c_str(), subject->getType());
        manageTraffic();
    }

    int getType() const override {
        return  -1;
    };
};
#endif