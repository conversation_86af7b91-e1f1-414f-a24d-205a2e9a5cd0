#ifndef STN_DATASERVICE_HPP
#define STN_DATASERVICE_HPP
#include "core/interfaces/IDataProcessor.hpp"
#include "utils/Logger.hpp"
class DataService : public IDataProcessor {
public:
    void processData() override {
        LOG(INFO, "DataService: Processing data");
    }

    void update(ISubject* subject, const std::string& message) override {
        LOG(INFO, "DataService received update: " + message + "from " + std::to_string(subject->getType()));
        processData();
    }

    int getType() const override {
        return  -1;
    };
};
#endif