//
// Created by lth on 2024/10/5.
//

#ifndef REDISPROVIDER_H
#define REDISPROVIDER_H

#include <components/ComponentsType.hpp>

#include "core/interfaces/IDataProvider.hpp"
#include "utils/Logger.hpp"
#include "utils/RedisClient.hpp"

#include "core/ConfigManager.hpp"


class RedisDataProvider : public IDataProvider{
public:
    void captureData() override;
    void stopCapture() override; // Implement stopCapture method

    void fetchDataFromRedis(std::string &message);

    RedisDataProvider(const std::string& redisHost, int redisPort, const std::string& redisPwd,
        const std::string& remoteRedisHost, int remoteRedisPort, const std::string& remoteRedisPwd,
        const std::string& localHostIp, const std::string& intersectionKey, const std::string& originStateKey,
        const std::string& algConfigKey);

    explicit RedisDataProvider();

    void addObserver(IObserver* observer) override {
        observers_.push_back(observer);
    }

    void removeObserver(IObserver* observer) override {
        observers_.erase(std::remove(observers_.begin(), observers_.end(), observer), observers_.end());
    }

    void notifyAllObservers(const std::string& message) override {
        for (auto observer : observers_) {
            observer->update(this, message);
        }
    }

    void notifyObservers(const std::string& message, int component) override {
        for (auto observer : observers_) {
           if (observer->getType() == component) {
              observer->update(this, message);
           }
        }
    }

    [[nodiscard]] int getType() const override {
        return static_cast<int>(ComponentsType::RedisDataProvider);
    }
private:
    std::shared_ptr<RedisClient> local_redis_client_;
    std::shared_ptr<RedisClient> remote_redis_client_;
    std::vector<IObserver*> observers_;
    std::string deviceConfigString_;
    std::string intersection_code_;
    std::string local_host_ip_;

    std::string intersection_key_;
    std::string origin_state_key_;
    std::string algConfig_key_;
    std::string sensorConfigKey_;
    std::string signalConfigKey_;
};




#endif //REDISPROVIDER_H
