//
// Created by lth on 2024/10/5.
//

#include <csignal>
#include "RedisDataProvider.hpp"
#include "core/ConfigManager.hpp"
#include "json.hpp"
#include "utils/IpAddress.hpp"

extern volatile sig_atomic_t gStopFlag;
extern std::string gIntersectionCode;
using json = nlohmann::json;

RedisDataProvider::RedisDataProvider(const std::string& redisHost, int redisPort, const std::string& redisPwd,
        const std::string& remoteRedisHost, int remoteRedisPort, const std::string& remoteRedisPwd,
        const std::string& localHostIp, const std::string& intersectionKey, const std::string& originStateKey,
        const std::string& algConfigKey) {

    local_redis_client_ = std::make_shared<RedisClient>(redisHost, redisPort, redisPwd);
    remote_redis_client_ = std::make_shared<RedisClient>(remoteRedisHost, remoteRedisPort, remoteRedisPwd);

    local_host_ip_ = localHostIp;
    intersection_key_ = intersectionKey;
    origin_state_key_ = originStateKey;
    algConfig_key_ = algConfigKey;

    const std::string interSectionDeviceKey = intersection_key_ + ":" + localHostIp;
    auto configData = local_redis_client_->get(interSectionDeviceKey);
    if (configData.has_value()) {
       deviceConfigString_ = configData.value();
    }
    LOG(DEBUG, "device Config " + deviceConfigString_);

    // 解析 JSON
    nlohmann::json j = nlohmann::json::parse(deviceConfigString_);

    // 访问 JSON 数据
    std::string code = j["code"];
    std::cout << "Code: " << code << std::endl;
    intersection_code_ = code;
}

RedisDataProvider::RedisDataProvider() {
    std::string redisHost = configData.localRedisIp;
    int redisPort = std::stoi(configData.localRedisPort);
    std::string redisPwd = configData.localRedisPwd;
    local_redis_client_ = std::make_shared<RedisClient>(redisHost, redisPort, redisPwd);
#if 0
    std::string remoteRedisHost = configData.redisServiceIp;
    int remoteRedisPort = std::stoi(configData.redisServicePort);
    std::string remoteRedisPwd = configData.redisServicePwd;
    remote_redis_client_ = std::make_shared<RedisClient>(remoteRedisHost, remoteRedisPort, remoteRedisPwd);
#endif

    //local_host_ip_ = configData.localHostIp;
    local_host_ip_ = NetworkInterface::getDefaultInterfaceIP();
    intersection_key_ = configData.intersectionDevicesKey;
    origin_state_key_ = configData.originStateKey;
    algConfig_key_ = configData.algConfigKey;
    sensorConfigKey_ = configData.sensorConfigKey;
    signalConfigKey_ = configData.signalConfigKey;

    const std::string interSectionDeviceKey = intersection_key_ + ":" + local_host_ip_;
    auto intersectionDeviceconfigData = local_redis_client_->get(interSectionDeviceKey);
    if (intersectionDeviceconfigData.has_value()) {
        deviceConfigString_ = intersectionDeviceconfigData.value();
    }

    if (!deviceConfigString_.empty()) {
        LOG(DEBUG, "deviceConfigString_ " + deviceConfigString_);
        json device_config_json = json::parse(deviceConfigString_);
        intersection_code_ = device_config_json["code"];
        gIntersectionCode = intersection_code_;
        LOG(INFO, "Code: " + intersection_code_);
    }


}

// Add a private member to control the capture loop
static std::atomic<bool> stopCaptureFlag(false);

void RedisDataProvider::captureData() {
    LOG(DEBUG, "RedisDataProvider: Capturing and transmitting data");
    stopCaptureFlag = false;
    while (!gStopFlag && !stopCaptureFlag) {
        std::string message;
        fetchDataFromRedis(message);
        sleep(1);
    }
    LOG(DEBUG, "RedisDataProvider: Capture loop stopped");
}

void RedisDataProvider::stopCapture() {
    LOG(DEBUG, "RedisDataProvider: Stopping data capture");
    stopCaptureFlag = true;
}

void RedisDataProvider::fetchDataFromRedis(std::string& message) {
    //notify to FlowControl
    json combined_json;
    //do not need to notify below msg after alg split.
#if 0
    const std::string fulloriginStateKey = origin_state_key_ + ":" + intersection_code_;
    auto origin_state_data = local_redis_client_->getLatestFromStream(fulloriginStateKey, 2);
    if (origin_state_data.has_value()) {
        LOG(DEBUG, "origin_state_data first: " + origin_state_data.value().first + " second " + origin_state_data
        .value().second);
        LOG(DEBUG, "origin_state_data " + origin_state_data.value().second);
        json origin_state_json = json::parse(origin_state_data.value().second);
        combined_json[origin_state_key_] = origin_state_json;
    }

    const std::string fullalgConfigkey = algConfig_key_ + ":" + intersection_code_;
    auto algConfig_data = local_redis_client_->get(fullalgConfigkey);
    if (algConfig_data.has_value()) {
        LOG(DEBUG, "algConfig_data " + algConfig_data.value());
        json algConfig_json = json::parse(algConfig_data.value());
        combined_json[algConfig_key_] = algConfig_json;
    }
    const std::string fullSensorConfigkey = sensorConfigKey_ + ":" + intersection_code_;
    auto sensorConfig_data = local_redis_client_->get(fullSensorConfigkey);
    if (sensorConfig_data.has_value()) {
        LOG(DEBUG, "sensorConfig_data " + sensorConfig_data.value());
        json sensorConfig_json = json::parse(sensorConfig_data.value());
        combined_json[sensorConfigKey_] = sensorConfig_json;
    }
    message = combined_json.dump();
    LOG(DEBUG, "RedisDataProvider: Fetching data from RedisClient " + message);
    notifyObservers(message, static_cast<int>(ComponentsType::FlowControlModule));
#endif
    //notify to signal machine
    if (!intersection_code_.empty()) {
        const std::string signalConfigKey = signalConfigKey_ + ":" + intersection_code_;
        auto signalConfig_data = local_redis_client_->get(signalConfigKey);
        if (signalConfig_data.has_value()) {
            LOG(DEBUG, "signalConfig_data " + signalConfig_data.value());
            notifyObservers(signalConfig_data.value(), static_cast<int>(ComponentsType::SignalMachine));
        }
        const std::string algOutputKey = "algorithm_control:" + intersection_code_;
        auto algOutput_data = local_redis_client_->getLatestFromStream(algOutputKey, 2);
        if (algOutput_data.has_value()) {
            LOG(DEBUG, "algOutput_data first: " + algOutput_data.value().first + "|second " + algOutput_data
            .value().second);
            LOG(DEBUG, "algOutput_data " + algOutput_data.value().second);
            json algOutput_json = json::parse(algOutput_data.value().second);
            notifyObservers(algOutput_json.dump(), static_cast<int>(ComponentsType::FlowControlModule));
        }
    }
}
