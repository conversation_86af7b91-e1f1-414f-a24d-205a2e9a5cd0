#ifndef STN_CAMERARADAR_HPP
#define STN_CAMERARADAR_HPP

#include <iostream>
#include <vector>
#include <algorithm>
#include <components/ComponentsType.hpp>


#include "core/interfaces/IDataProvider.hpp"
#include  "utils/Logger.hpp"

class CameraRadar : public IDataProvider {
public:
    void captureData() override {
        LOG(INFO, "CameraRadar: Capturing and transmitting data");
        notifyAllObservers("New data captured");
    }

    void stopCapture() override {
        LOG(INFO, "CameraRadar: Stopping data capture");
        // No background thread to stop in this implementation
    }

    void addObserver(IObserver* observer) override {
        observers.push_back(observer);
    }

    void removeObserver(IObserver* observer) override {
        observers.erase(std::remove(observers.begin(), observers.end(), observer), observers.end());
    }

    void notifyAllObservers(const std::string& message) override {
        for (auto observer : observers) {
            observer->update(this, message);
        }
    }

    void notifyObservers(const std::string& message, int component) override {
        for (auto observer : observers) {
            if (observer->getType() == component) {
                observer->update(this, message);
            }
        }
    }

    [[nodiscard]] int getType() const override {
        return static_cast<int>(ComponentsType::SignalMachine);
    }

private:
    std::vector<IObserver*> observers;
};
#endif