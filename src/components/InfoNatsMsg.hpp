//
// Created by lth on 2024/10/24.
//

#ifndef NATSMSG_HPP
#define NATSMSG_HPP

#include <string>
#include <vector>
#include <json.hpp>
#include <chrono>
#include <iomanip>
#include <sstream>

#include "utils/IpAddress.hpp"
#include "utils/NatsClient.hpp"

extern std::string gMainName;
extern std::string gIntersectionCode;

using json = nlohmann::json;

// 数据项结构
struct NatsDataItem {
    std::string obj;   // 对象
    std::string value; // 值
    std::string desc;  // 描述

    // JSON序列化方法
    json to_json() const {
        return {
            {"obj", obj},
            {"value", value},
            {"desc", desc}
        };
    }

    // JSON反序列化方法
    static NatsDataItem from_json(const json& j) {
        NatsDataItem item;
        item.obj = j["obj"].get<std::string>();
        item.value = j["value"].get<std::string>();
        item.desc = j["desc"].get<std::string>();
        return item;
    }
};

// 主消息类
class NatsDataMessage {
public:
    std::string ip;
    int dataType;
    std::string sysCode;
    std::string process;
    std::string intersectionCode;
    std::string dataNode;
    std::string dataDesc;
    std::vector<NatsDataItem> dataItems;
    std::string datetime;

    // 默认构造函数
    NatsDataMessage() : dataType(1) {
        //ip = NetworkInterface::getInterfaceIP("eth0");
        ip = NetworkInterface::getDefaultInterfaceIP();
        sysCode = "02";
        process = gMainName;
        intersectionCode = gIntersectionCode;
        update_datetime();
    }

    // 更新时间戳为当前时间
    void update_datetime() {
        auto now = std::chrono::system_clock::now();
        auto time_t_now = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t_now), "%Y-%m-%d %H:%M:%S");
        datetime = ss.str();
    }

    // 转换为JSON字符串
    std::string to_string() const {
        return to_json().dump();
    }

    // 从JSON字符串解析
    static NatsDataMessage from_string(const std::string& json_str) {
        return from_json(json::parse(json_str));
    }

    // 转换为JSON对象
    json to_json() const {
        json j;
        j["ip"] = ip;
        j["dataType"] = dataType;
        j["sysCode"] = sysCode;
        j["process"] = process;
        j["intersectionCode"] = intersectionCode;
        j["dataNode"] = dataNode;
        j["dataDesc"] = dataDesc;

        // 转换dataItems数组
        json items_array = json::array();
        for (const auto& item : dataItems) {
            items_array.push_back(item.to_json());
        }
        j["dataItem"] = items_array;
        j["datetime"] = datetime;

        return j;
    }

    // 从JSON对象解析
    static NatsDataMessage from_json(const json& j) {
        NatsDataMessage msg;
        msg.ip = j["ip"].get<std::string>();
        msg.dataType = j["dataType"].get<int>();
        msg.sysCode = j["sysCode"].get<std::string>();
        msg.process = j["process"].get<std::string>();
        msg.dataNode = j["dataNode"].get<std::string>();
        msg.dataDesc = j["dataDesc"].get<std::string>();
        msg.datetime = j["datetime"].get<std::string>();
        msg.intersectionCode = j["intersectionCode"].get<std::string>();

        // 解析dataItems数组
        for (const auto& item : j["dataItem"]) {
            msg.dataItems.push_back(NatsDataItem::from_json(item));
        }

        return msg;
    }
};


class NotifyStatsService {
public:
    static NotifyStatsService& getInstance() {
       static NotifyStatsService instance;
        return instance;
    }
    static void sendStateMessage(NatsClient& natsClient, const NatsDataMessage& msg) {
        if (natsClient.init() && natsClient.isConnected() && natsClient.publish("topic_pro_data", msg.to_string())) {
            LOG(DEBUG, "Send Notify Stats msg success.");
        } else {
           LOG(ERROR, "Send Notify Stats msg to nats: {}", natsClient.getLastError());
        }
    };
private:
    NotifyStatsService() = default;
    ~NotifyStatsService() = default;
};









#include <stdexcept>

class ControlMessage {
public:
    // 构造函数
    ControlMessage() : code_(""), control_(0), from_(0) {}
    ControlMessage(std::string code, int control, int from)
        : code_(std::move(code)), control_(control), from_(from) {}

    // Getters
    const std::string& getCode() const { return code_; }
    int getControl() const { return control_; }
    int getFrom() const { return from_; }

    // Setters
    void setCode(const std::string& code) { code_ = code; }
    void setControl(int control) { control_ = control; }
    void setFrom(int from) { from_ = from; }

    // 将对象转换为JSON字符串
    std::string toString() const {
        nlohmann::json j;
        j["code"] = code_;
        j["control"] = control_;
        j["from"] = from_;
        return j.dump();
    }

    // 从JSON字符串解析对象
    static ControlMessage fromString(const std::string& jsonStr) {
        try {
            auto j = nlohmann::json::parse(jsonStr);

            // 检查必要字段是否存在
            if (!j.contains("code") || !j.contains("control") || !j.contains("from")) {
                throw std::invalid_argument("Missing required fields in JSON");
            }

            // 检查字段类型
            if (!j["code"].is_string()) {
                throw std::invalid_argument("Field 'code' must be a string");
            }
            if (!j["control"].is_number()) {
                throw std::invalid_argument("Field 'control' must be a number");
            }
            if (!j["from"].is_number()) {
                throw std::invalid_argument("Field 'from' must be a number");
            }

            return ControlMessage(
                j["code"].get<std::string>(),
                j["control"].get<int>(),
                j["from"].get<int>()
            );
        } catch (const nlohmann::json::parse_error& e) {
            throw std::invalid_argument("Invalid JSON format: " + std::string(e.what()));
        }
    }

    // 运算符重载，用于比较两个对象是否相等
    bool operator==(const ControlMessage& other) const {
        return code_ == other.code_ &&
               control_ == other.control_ &&
               from_ == other.from_;
    }

    bool operator!=(const ControlMessage& other) const {
        return !(*this == other);
    }

private:
    std::string code_;
    int control_;
    int from_;
};



#endif //NATSMSG_HPP