//
// Created by lth on 2024/10/5.
//

#include "PythonInterface.hpp"
#include "utils/Logger.hpp"
#include <iostream>


PythonInterface::PythonInterface() : pModule(nullptr), pClass_(nullptr), pInstance(nullptr) {
}

PythonInterface::~PythonInterface() {
    PyThreadStateLock PyThreadLock;
    if (pInstance)
        Py_CLEAR(pInstance);
    if (pModule)
        Py_CLEAR(pModule);
}

bool PythonInterface::ensureModuleLoaded(const std::string& moduleName) {
    if (!pModule) {
        return loadModule(moduleName);
    }
    return true;
}

bool PythonInterface::loadModule(const std::string& moduleName) {
    PyThreadStateLock PyThreadLock;

    PyObject* pName = PyUnicode_DecodeFSDefault(moduleName.c_str());
    pModule = PyImport_Import(pName);
    Py_DECREF(pName);

    if (!pModule) {
        handleError();
        return false;
    }

    return true;
}

bool PythonInterface::createInstance(const std::string& className, const std::string& configPath,
    const std::string& sensor_config) {
    PyThreadStateLock PyThreadLock;

    PyObject* pClass = PyObject_GetAttrString(pModule, className.c_str());
    if (!pClass || !PyCallable_Check(pClass)) {
        handleError();
        return false;
    }

    PyObject *pArgs = PyTuple_New(1);
    PyObject *pConfig = PyUnicode_FromString(configPath.c_str());
    PyTuple_SetItem(pArgs, 0, pConfig); // 添加构造函数的参数

    PyObject *pSensorConfig = jsonStringToPyDict(sensor_config);
    if (!pSensorConfig) {
        Py_DECREF(pArgs);
        Py_DECREF(pClass);
        return false;
    }

    // 关键字参数
    PyObject *pKwArgs = PyDict_New();
    PyDict_SetItemString(pKwArgs, "sensor_cnf", pSensorConfig);

    pInstance = PyObject_Call(pClass, pArgs, pKwArgs);

    Py_DECREF(pArgs);
    Py_DECREF(pKwArgs);
    Py_DECREF(pSensorConfig);
    Py_DECREF(pClass);

    if (!pInstance) {
        handleError();
        return false;
    }
    return true;
}


void PythonInterface::deleteInstance() {
    PyThreadStateLock PyThreadLock;
    if (pInstance) Py_CLEAR(pInstance);
}

PyObject* PythonInterface::callMethod(const std::string& methodName, PyObject* args) {
    PyThreadStateLock PyThreadLock;
    PyObject* pResult = nullptr;
    if (pInstance) {
        PyObject* pFunc = PyObject_GetAttrString(pInstance, methodName.c_str());
        if (pFunc && PyCallable_Check(pFunc)) {
            pResult = PyObject_CallObject(pFunc, args);
            Py_DECREF(pFunc);
        } else {
            handleError();
        }
    }
    return pResult;
}

PyObject* PythonInterface::callMethodWithDict(const std::string& methodName, const std::string& dict1,
    const std::string& dict2) {
    PyThreadStateLock PyThreadLock;
    // 准备参数
    PyObject* pArgs = PyTuple_New(2);  // 创建一个包含两个元素的元组
    PyObject* pDict1 = jsonStringToPyDict(dict1);
    PyObject* pDict2 = jsonStringToPyDict(dict2); // 第二个字典为 None 时传递 Py_None

    // 将字典添加到参数元组
    PyTuple_SetItem(pArgs, 0, pDict1);
    PyTuple_SetItem(pArgs, 1, pDict2);

    PyObject* pResult = callMethod(methodName, pArgs);
    Py_DECREF(pArgs);
    return pResult;
}

PyObject * PythonInterface::callMethodWithDict(const std::string &methodName, const std::string &dict) {
    PyThreadStateLock PyThreadLock;
    // 将 JSON 转换为 Python 字典
    PyObject* pDict = jsonStringToPyDict(dict);
    // 调用带字典参数的 Python 函数
    PyObject* pArgs = PyTuple_New(1);
    PyTuple_SetItem(pArgs, 0, pDict);
    PyObject* pResult = callMethod(methodName, pArgs);
    Py_DECREF(pArgs);
    return pResult;
}

void PythonInterface::printPyObject(PyObject* obj) {
    PyThreadStateLock PyThreadLock;
    if (PyUnicode_Check(obj)) {
        const char* str = PyUnicode_AsUTF8(obj);
        std::cout << "返回值: " << str << std::endl;
    } else if (PyLong_Check(obj)) {
        long val = PyLong_AsLong(obj);
        std::cout << "返回值: " << val << std::endl;
    } else if (PyFloat_Check(obj)) {
        double val = PyFloat_AsDouble(obj);
        std::cout << "返回值: " << val << std::endl;
    } else if (PyBool_Check(obj)) {
        bool val = (obj == Py_True);
        std::cout << "返回值: " << (val ? "True" : "False") << std::endl;
    } else if (PyDict_Check(obj)) {
        PyObject* json_module = PyImport_ImportModule("json");
        PyObject* json_dumps = PyObject_GetAttrString(json_module, "dumps");
        PyObject* json_str = PyObject_CallFunctionObjArgs(json_dumps, obj, NULL);
        const char* json_output = PyUnicode_AsUTF8(json_str);
        std::cout << "返回值是字典 (JSON 表示): " << json_output << std::endl;
        Py_DECREF(json_str);
        Py_DECREF(json_dumps);
        Py_DECREF(json_module);
    }
}

void PythonInterface::handleError() {
    PyThreadStateLock PyThreadLock;
    if (PyErr_Occurred()) {
        PyErr_Print();
    }
}

PyObject * PythonInterface::json_to_pydict(const nlohmann::json &j) {
    PyThreadStateLock PyThreadLock;
    PyObject* py_dict = PyDict_New();
    for (auto& [key, value] : j.items()) {
        PyObject* py_key = PyUnicode_FromString(key.c_str());
        PyObject* py_value = nullptr;

        if (value.is_string()) {
            py_value = PyUnicode_FromString(value.get<std::string>().c_str());
        } else if (value.is_number_integer()) {
            py_value = PyLong_FromLong(value.get<int>());
        } else if (value.is_number_float()) {
            py_value = PyFloat_FromDouble(value.get<double>());
        } else if (value.is_boolean()) {
            py_value = PyBool_FromLong(value.get<bool>());
        } else if (value.is_object()) {
            py_value = json_to_pydict(value);  // 递归转换嵌套对象
        } else if (value.is_array()) {
            PyObject* py_list = PyList_New(value.size());
            for (size_t i = 0; i < value.size(); ++i) {
                PyObject* item = nullptr;
                if (value[i].is_string()) {
                    item = PyUnicode_FromString(value[i].get<std::string>().c_str());
                } else if (value[i].is_number_integer()) {
                    item = PyLong_FromLong(value[i].get<int>());
                } else if (value[i].is_number_float()) {
                    item = PyFloat_FromDouble(value[i].get<double>());
                } else if (value[i].is_boolean()) {
                    item = PyBool_FromLong(value[i].get<bool>());
                }
                PyList_SetItem(py_list, i, item);  // 设置列表项
            }
            py_value = py_list;
        }

        if (py_value) {
            PyDict_SetItem(py_dict, py_key, py_value);  // 将 key-value 对插入 Python 字典
            Py_DECREF(py_value);  // 减少引用计数
        }
        Py_DECREF(py_key);  // 减少引用计数
    }
    return py_dict;
}


// 公共函数：将 JSON 字符串转换为 Python 字典
PyObject*  PythonInterface::jsonStringToPyDict(const std::string& jsonString) {
    PyThreadStateLock PyThreadLock;
    // Import the Python json module
    PyObject* jsonModule = PyImport_ImportModule("json");
    if (!jsonModule) {
        std::cerr << "Error: Failed to import json module." << std::endl;
        return nullptr;
    }

    // Get the json.loads function
    PyObject* loadFunc = PyObject_GetAttrString(jsonModule, "loads");
    if (!loadFunc) {
        std::cerr << "Error: Failed to get json.loads function." << std::endl;
        return nullptr;
    }

    // Call json.loads(jsonString) to convert the string to a dictionary
    PyObject* pyDict = PyObject_CallFunction(loadFunc, "s", jsonString.c_str());
    if (!pyDict) {
        std::cerr << "Error: Failed to parse JSON string." << std::endl;
        return nullptr;
    }

    // Clean up
    Py_DECREF(loadFunc);
    Py_DECREF(jsonModule);
    return pyDict; // 返回创建的字典
}