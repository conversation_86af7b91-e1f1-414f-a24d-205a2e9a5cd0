//
// Created by lth on 2024/10/5.
//

#ifndef PYTHONINTERFACE_HPP
#define PYTHONINTERFACE_HPP

#include <Python.h>
#include <string>
#include <iostream>
#include <json.hpp>


class PyThreadStateLock
{
public:
    PyThreadStateLock()
    {
        state = PyGILState_Ensure( );
    }

    ~PyThreadStateLock()
    {
        PyGILState_Release( state );
    }
private:
    PyGILState_STATE state;
};



class PythonInterface {
public:
    PythonInterface();
    ~PythonInterface();

    bool ensureModuleLoaded(const std::string &moduleName);

    // 加载 Python 模块
    bool loadModule(const std::string& moduleName);

    // 实例化 Python 类
    bool createInstance(const std::string& className, const std::string& configPath, const std::string& sensorconfig);

    void deleteInstance();

    // 调用 Python 类的成员函数
    PyObject* callMethod(const std::string& methodName, PyObject* args);

    // 调用带字典参数的函数
    PyObject* callMethodWithDict(const std::string& methodName, const std::string& dict1,
        const std::string& dict2);

    // 调用带字典参数的函数
    PyObject* callMethodWithDict(const std::string& methodName, const std::string& dict);


    // 打印 Python 对象
    void printPyObject(PyObject* obj);

    PyObject* json_to_pydict(const nlohmann::json& j);
    PyObject* jsonStringToPyDict(const std::string& jsonString);
private:
    PyObject* pModule;     // Python 模块对象
    PyObject* pClass_;      // Python 类对象
    PyObject* pInstance;   // Python 类的实例
    void handleError();
};



#endif //PYTHONINTERFACE_HPP
