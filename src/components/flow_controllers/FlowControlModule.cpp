#include "FlowControlModule.hpp"
#include "utils/NatsClient.hpp"
#include "components/InfoNatsMsg.hpp"
#include "utils/IpAddress.hpp"
#include <fstream>


extern NatsClient &gNatsClient;
extern std::string gMainName;
extern std::string gIntersectionCode;
using json  = nlohmann::json;

void FlowControlModule::controlFlow(const std::string& message) {
    try {
        nlohmann::json all_message = nlohmann::json::parse(message);
        //nlohmann::json deviceConfig = all_message[configData.intersectionDevicesKey];
        nlohmann::json origin_state = all_message[configData.originStateKey];
        nlohmann::json algConfig = all_message[configData.algConfigKey];
        nlohmann::json sensorConfig = all_message[configData.sensorConfigKey];
        sensorConfigStr_ = sensorConfig.dump();
        std::string algConfigVer = algConfig["version"];
        code_ = origin_state["code"];
        LOG(INFO, "code: " + code_ + ", algConfigVersionFromRedis: " + algConfigVer);
        writeJsonToFile(algConfig);

        if (algNeedReLoad_) {
            pythonInterface_->deleteInstance();
            if (!initPythonInterface(*pythonInterface_, "advanced_control", "AdvancedControl")) {
                LOG(ERROR, "FlowControlModule: Failed to initialize Python");
                return;
            }
            algNeedReLoad_ = false;
        }

#if 0
    // 读取 origin_state JSON 文件
    std::ifstream file("/home/<USER>/stn/alg/origin_state.json");
    if (!file) {
        std::cerr << "无法打开 JSON 文件" << std::endl;
        return;
    }
    // 使用stringstream来读取文件内容
    std::stringstream buffer;
    buffer << file.rdbuf(); // 将文件内容读取到缓冲区
    std::string jsonString = buffer.str(); // 将缓冲区内容转换为字符串
    //LOG(INFO, "convert origin_state: " + jsonString);
    std::string retstr = convert_cur_state(jsonString);
    nlohmann::json vehicle_map_json = nlohmann::json::parse(retstr);
    //LOG(INFO, "convert return state: \n" + retstr);
#endif

#if 1
        // 调用 Python 类的成员函数 convert_cur_state
        std::string retstr = convert_cur_state(*pythonInterface_, origin_state.dump());
        nlohmann::json vehicle_map_json = nlohmann::json::parse(retstr);
        std::string envjsonString;
        {
            std::unique_lock<std::mutex> lock(signalEnvStateMutex_);
            envjsonString = signalEnvState_.toJson().dump();
            if (signalEnvState_.timestamp == 0) {
                LOG(WARNING, "signal machine env state is invalid.");
            }
        }
        LOG(DEBUG, "envjsonString " + envjsonString);
        // 创建新的 JSON 对象
        nlohmann::json state;
        state[code_] = vehicle_map_json;
        LOG(DEBUG, "outcome state :" + vehicle_map_json.dump());

        NatsDataMessage natsDataMessage;
        natsDataMessage.dataDesc = "ALG args";
        natsDataMessage.dataNode = "ALG";
        std::vector<NatsDataItem> natsDataItems;


        NatsDataItem AlgConfigVerItem = {
        code_ + "_alg_config_version",
            algConfigVer,
            "alg config version"
        };
        natsDataItems.push_back(AlgConfigVerItem);

        NatsDataItem algVehicleMapItem = {
            code_ + "_vehicle_map",
            state.dump(),
            "alg input vehicle_map"
        };
        natsDataItems.push_back(algVehicleMapItem);

        NatsDataItem envStateItem = {
            code_ + "_env_state",
            envjsonString,
            "alg input env state"
        };
        natsDataItems.push_back(envStateItem);

        // 调用 take_action
        int phase = take_action(*pythonInterface_, state.dump(), envjsonString);

        NatsDataItem algPhaseItem = {
        code_ + "_AlgPhase",
            std::to_string(phase),
            "alg phase result"
        };
        natsDataItems.push_back(algPhaseItem);

        natsDataMessage.dataItems = natsDataItems;
        NotifyStatsService::getInstance().sendStateMessage(gNatsClient, natsDataMessage);
#endif

#if 0
        NatsDataMessage msg;
        msg.ip = NetworkInterface::getInterfaceIP("eth0");
        msg.dataType = 1;
        msg.dataNode = "TIR";
        msg.dataDesc = "vehicle_map";
        msg.process = gMainName;
        NatsDataItem item{"vehicle_map", state.dump(), "vehicle_map"};
        msg.dataItems.push_back(item);
        gNatsClient.publish("topic_pro_data", msg.to_string());
#endif
#if 0
        static int phase = -1;
        static int tictok = 0;
        static int testPhase = 0;
        if (tictok++ == 30) {
            phase = (testPhase++ % 4) + 1;
            tictok = 0;
        }
        LOG(INFO, "Test Phase: {}", phase);
#endif
        signalController_->controlSignals(phase);
    } catch (std::exception &e) {
        LOG(ERROR, "FlowControlModule: exception thrown" + std::string(e.what()));
    }
}

void FlowControlModule::update(ISubject* subject, const std::string& message) {
    if (subject->getType() == static_cast<int>(ComponentsType::RedisDataProvider)) {
        LOG(DEBUG, "FlowControlModule received update from RedisDataProvider: " + message);
        controlFlow(message);
    } else if (subject->getType() == static_cast<int>(ComponentsType::SignalMachine)) {
        LOG(DEBUG, "FlowControlModule received update from SignalMachine: " + message);
        std::unique_lock<std::mutex> lock(signalEnvStateMutex_);
        signalEnvState_ = SignalEnvState::fromJson(json::parse(message));
        lock.unlock();
    }
}

FlowControlModule::FlowControlModule(const std::string &config, const std::string& sensor_config,
    ISignalController* signal_controller) {
    pythonInterface_ = std::make_unique<PythonInterface>();
    //algConfigFileName_ = config;
    sensor_config_ = sensor_config;
    signalController_ = signal_controller;
}

FlowControlModule::FlowControlModule(ISignalController* signal_controller) {
    pythonInterface_ = std::make_unique<PythonInterface>();
    signalController_ = signal_controller;
}

FlowControlModule::~FlowControlModule() {
}

std::string FlowControlModule::convert_cur_state(PythonInterface &pyInterface, const std::string &origin_state) {
    // 调用 Python 类的成员函数 convert_cur_state
    try {
        // 调用封装好的 Python 接口来调用 convert_cur_state
        PyObject* result = pyInterface.callMethodWithDict("convert_cur_state", origin_state);
        PyThreadStateLock PyThreadLock;
        // 检查返回值是否为字典
        if (PyDict_Check(result)) {
            // 打印返回的 Python 字典作为 JSON 字符串
            PyObject* json_module = PyImport_ImportModule("json");
            PyObject* json_dumps = PyObject_GetAttrString(json_module, "dumps");
            PyObject* json_str = PyObject_CallFunctionObjArgs(json_dumps, result, NULL);

            const char* json_output = PyUnicode_AsUTF8(json_str);
            if (!json_output) {
                PyErr_Print();
                Py_CLEAR(json_str);
                Py_CLEAR(json_dumps);
                Py_CLEAR(json_module);
                Py_CLEAR(result);
                throw std::runtime_error("Failed to convert JSON string to UTF-8");
            }
            std::string jsonResult = {json_output};
            // 释放 Python 对象
            Py_CLEAR(json_str);
            Py_CLEAR(json_dumps);
            Py_CLEAR(json_module);
            Py_CLEAR(result);
            return jsonResult;  // 将 Python 返回的 JSON 字符串传回 C++
        }
        // 如果返回的不是字典，处理错误情况
        Py_CLEAR(result);
        throw std::runtime_error("convert_cur_state did not return a dictionary");
    } catch (std::exception &e) {
        LOG(ERROR, "convert_cur_state return error: " + std::string(e.what()));
        return {};
    }
}

int FlowControlModule::take_action(PythonInterface& pyInterface, const std::string &converted_state, const std::string
&env_state) {
    // 调用 take_action
    try {
        // 调用封装好的 Python 接口来调用 convert_cur_state
        PyObject* result = pyInterface.callMethodWithDict("take_action",converted_state, env_state);

        // 检查返回值是否为整数
        if (PyLong_Check(result)) {
            int action_value = PyLong_AsLong(result);  // 转换为 int
            Py_CLEAR(result);  // 释放返回值的引用
            return action_value;  // 返回 C++ 中的整数值
        }
        // 如果返回的不是整数，处理错误情况
        Py_CLEAR(result);
        throw std::runtime_error("take_action did not return an integer");
    } catch (std::exception &e) {
        LOG(ERROR, "take_action return error " + std::string(e.what()));
        return -1;
    }
}

bool FlowControlModule::initPythonInterface(PythonInterface& pyInterface, const std::string &pythonModuleName, const
std::string &pythonClassName) {
    pyInterface.ensureModuleLoaded(pythonModuleName);
    std::string sensorjsonString;
#if 0
    std::ifstream sensorfile(sensor_config_);
    if (!sensorfile) {
        LOG(ERROR, "无法打开 JSON 文件");
        return false;
    } else {
        // 使用stringstream来读取文件内容
        std::stringstream sensorbuffer;
        sensorbuffer << sensorfile.rdbuf(); // 将文件内容读取到缓冲区
        sensorjsonString = sensorbuffer.str(); // 将缓冲区内容转换为字符串
        //LOG(INFO, "sensor conf is " + sensorjsonString);
    }
#else
    sensorjsonString = sensorConfigStr_;
#endif

    if (!pyInterface.createInstance(pythonClassName, algConfigFileName_, sensorjsonString)) {
        LOG(ERROR, "Python Inferface Module {}, class {} create failed.", pythonModuleName, pythonClassName);
        return false;
    }
    return true;
}

bool FlowControlModule::deInitPythonInterface() {
    return true;
}

void FlowControlModule::writeJsonToFile(const nlohmann::json& algConfig) {
    // 提取 version 字段作为文件名的一部分
    std::string version;
    if (algConfig.contains("version") && algConfig["version"].is_string()) {
        version = algConfig["version"];
    } else {
        version = "default_version";  // 如果 version 字段不存在，使用默认值
    }

    // 构建文件名：algConfig_[version].json
    std::string fileName = configData.algConfigFileDir + "/algConfig_" + version + ".json";

    // 检查文件是否存在
    if (std::filesystem::exists(fileName)) {
        // 检查版本号是否一致
        if (algConfigVer_ == version) {
            LOG(DEBUG, "algConfig File version the same as old one, not changed.");
            return; // 版本号相同，不需要创建新文件
        }
    }
    std::filesystem::create_directories(configData.algConfigFileDir);
    // 如果文件不存在或版本号不同，写入新的 JSON 文件
    std::ofstream file(fileName);
    if (file.is_open()) {
        file << algConfig.dump(4);  // 4 表示缩进 4 个空格，便于格式化输出
        file.close();
        algConfigVer_ = version;
        algConfigFileName_ = fileName;
        algNeedReLoad_ = true;
        LOG(INFO, "algConfig Changed, New JSON file saved successfully: " + fileName);
    } else {
        LOG(ERROR, "Error opening file: " + fileName);
    }
}

// 从intersectionDevice中转换算法需要的SensorConfig
//Not Use.
nlohmann::json FlowControlModule::generateSensorConfig(const nlohmann::json &deviceConfig) {
    nlohmann::json outputJson;

    // 基础信息
    outputJson["id"] = deviceConfig["code"];

    // 处理radars部分
    json roadsArray = json::array();
    for (const auto &[ip, device] : deviceConfig["devices"].items()) {
        if (device["deviceType"] == 1) { // 雷达设备
            json road;
            road["id"] = "XAL_XFL_" + device["identifyDrc"].get<std::string>();
            road["cameras"] = json::array();
            road["lanes"] = json::array();
            road["radars"]["id"] = device["deviceCode"];
            road["radars"]["ip"] = device["ip"];
            road["radars"]["type"] = "XD";
            road["radars"]["position"] = device["crossPosition"];
            roadsArray.push_back(road);
        }
    }
    outputJson["roads"] = roadsArray;

    // 处理crosswalks部分
    json crosswalksArray = json::array();
    for (const auto &[ip, device] : deviceConfig["devices"].items()) {
        if (device["deviceType"] == 3) { // 摄像头设备
            json crosswalk;
            crosswalk["id"] = "XAL_XFL_" + device["identifyDrc"].get<std::string>() + "CW";
            json camera;
            camera["id"] = device["deviceCode"];
            camera["localUrl"] = device["camUrl"];
            camera["window"] = device["camWindow"];
            camera["roi"] = device["camRoi"];
            camera["ip"] = device["ip"];
            camera["password"] = "qy_12345678";  // 假设密码不变
            crosswalk["cameras"].push_back(camera);
            crosswalksArray.push_back(crosswalk);
        }
    }
    outputJson["crosswalks"] = crosswalksArray;

    return outputJson;
}