#ifndef STN_FLOWCONTROLMODULE_HPP
#define STN_FLOWCONTROLMODULE_HPP
#include "core/interfaces/IFlowController.hpp"
#include "core/interfaces/ISignalController.hpp"
#include "core/ConfigManager.hpp"
#include "utils/Logger.hpp"
#include "PythonInterface.hpp"
#include "components/ComponentsType.hpp"
#include "components/signal_controllers/SignalDevices/ISignalDevice.hpp"

class FlowControlModule final : public IFlowController {
public:
    void controlFlow(const std::string &message) override;

    void update(ISubject* subject, const std::string& message) override;

    explicit FlowControlModule(const std::string& config, const std::string& sensor_config,
        ISignalController* signal_controller);

    explicit FlowControlModule(ISignalController* signal_controller);

    ~FlowControlModule() override;

    std::string convert_cur_state(PythonInterface &pyInterface, const std::string &origin_state);

    int take_action(PythonInterface &pyInterface, const std::string &converted_state, const std::string &env_state);

    bool initPythonInterface(PythonInterface &pyInterface, const std::string &pythonModuleName, const std::string &pythonClassName);

    bool deInitPythonInterface();

    void writeJsonToFile(const nlohmann::json &algConfig);

    nlohmann::json generateSensorConfig(const nlohmann::json &deviceConfig);

    [[nodiscard]] int getType() const override {
        return static_cast<int>(ComponentsType::FlowControlModule);
    }
private:
    std::string algConfigFileName_;
    std::string sensor_config_;
    std::string code_;
    std::string algConfigVer_;
    std::string sensorConfigStr_;
    const std::string pyAlgModuleName;
    const std::string pyAlgClassName;
    bool algNeedReLoad_ = false;
    ISignalController* signalController_;
    std::unique_ptr<PythonInterface> pythonInterface_;
    SignalEnvState signalEnvState_ = {};
    std::mutex signalEnvStateMutex_;  // 互斥锁，用于保护变量

};

#endif