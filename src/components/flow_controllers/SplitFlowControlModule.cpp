//
// Created by lth on 2025/2/21.
//

#include "SplitFlowControlModule.hpp"

#include <json.hpp>
#include <utility>

#include "utils/Logger.hpp"
#include "utils/RedisClient.hpp"
#include "core/ConfigManager.hpp"
#include "components/InfoNatsMsg.hpp"


double getCurrentTimestamp() noexcept {
    const auto now = std::chrono::system_clock::now();
    return std::chrono::duration<double>(now.time_since_epoch()).count();
}

SplitFlowControlModule::~SplitFlowControlModule() = default;

SplitFlowControlModule::SplitFlowControlModule(ISignalController *signal_controller, std::string redisHost,
                                               const int redisPort, std::string redisPwd)
    : signal_controller_(signal_controller), redisHost_(std::move(redisHost)), redisPort_(redisPort),
      redisPwd_(std::move(redisPwd)),
      algOutput(nullptr) {
    local_redis_client_ = std::make_unique<RedisClient>(redisHost_, redisPort_, redisPwd_);
    local_redis_client_->selectDB(2);
}

void SplitFlowControlModule::controlFlow(const std::string &message) {
    // if signal_control is null, return directly, and log thsi.
    if (!signal_controller_) {
        LOG(WARNING, "signal_controller_ is nullptr, do nothing");
        return;
    }
    LOG(DEBUG, "FlowControlModule received from RedisDataProvider: " + message);
    nlohmann::json json_message = nlohmann::json::parse(message);
    if (!json_message.contains("alg_return") || !json_message.contains("timestamp")) {
        LOG(ERROR, "Received invalid message from RedisDataProvider: " + message);
        return;
    }
    int alg_return = json_message["alg_return"];
    double algRetTimeStamp = json_message["timestamp"];
    LOG(DEBUG, "alg return {}, timestamp {}", alg_return, algRetTimeStamp);

    double ERROR_THRESHOLD = 5.0; // default value.
    try {
        ERROR_THRESHOLD = std::stod(configData.algPhaseErrorThreshold);
    } catch (const std::exception &e) {
        LOG(ERROR, "Unexpected error: " + std::string(e.what()));
    }
    const double currentTime = getCurrentTimestamp();
    const double deltaTime = currentTime - algRetTimeStamp;
    // 再试注销，算法返回时间超过当前时间不影响，也不该出现。
    // if (algRetTimeStamp > currentTime) {
    //     LOG(ERROR, "alg return timestamp is greater than current timestamp, do nothing");
    //     return;
    // }
    if (deltaTime > ERROR_THRESHOLD) {
        alg_return = -1;
        LOG(ERROR,
            "alg return timestamp is delay {} (s) more than alg phase error threshold {} (s), algReturn override to {}",
            deltaTime, ERROR_THRESHOLD, alg_return);
    }
    signal_controller_->controlSignals(alg_return);
}


void SplitFlowControlModule::update(ISubject *subject, const std::string &message) {
    if (!subject) {
        // 添加空指针检查
        LOG(WARNING, "Received null subject");
        return;
    }

    const auto subjectType = subject->getType();
    if (subjectType == static_cast<int>(ComponentsType::SignalMachine)) {
        try {
            LOG(INFO, "FlowControlModule received update from SignalMachine: " + message);
            if (!gIntersectionCode.empty()) {
                const std::string signal_env_state_key = SIGNAL_ENV_STREAM + ":" + gIntersectionCode;
                local_redis_client_->xadd_withtrim(signal_env_state_key, "*", "data", message);
            }
        } catch (const std::exception &e) {
            LOG(ERROR, "Unexpected error: " + std::string(e.what()));
        }
    } else if (subjectType == static_cast<int>(ComponentsType::RedisDataProvider)) {
        try {
            LOG(DEBUG, "FlowControlModule received update from RedisDataProvider: " + message);
            algOutput = nlohmann::json::parse(message);
            if (!algOutput.is_object()) {
                LOG(ERROR, "algOutput is not a JSON object");
                return;
            }
            controlFlow(algOutput.dump());
        } catch (const nlohmann::json::exception &e) {
            LOG(ERROR, "JSON parsing error: " + std::string(e.what()));
        } catch (const std::exception &e) {
            LOG(ERROR, "Unexpected error: " + std::string(e.what()));
        }
    }
}