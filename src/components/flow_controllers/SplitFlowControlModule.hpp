//
// Created by lth on 2025/2/21.
//

#ifndef SPLITFLOWCONTROLMODULE_HPP
#define SPLITFLOWCONTROLMODULE_HPP

#include <json.hpp>

#include "core/interfaces/ISignalController.hpp"
#include "core/interfaces/IFlowController.hpp"
#include "components/ComponentsType.hpp"
#include "utils/RedisClient.hpp"


class SplitFlowControlModule final : public IFlowController {
  public:
    void controlFlow(const std::string &message) override;
    void update(ISubject* subject, const std::string& message) override;
    [[nodiscard]] int getType() const override {
        return static_cast<int>(ComponentsType::FlowControlModule);
    }
    SplitFlowControlModule(ISignalController* signal_controller, std::string  redisHost, int redisPort, std::string  redisPwd);
    ~SplitFlowControlModule() override ;
    private:
        ISignalController* signal_controller_;
        std::shared_ptr<RedisClient> local_redis_client_;
        std::string redisHost_;
        int redisPort_;
        std::string redisPwd_;
        std::string intersection_code_;
        nlohmann::json algOutput;
        const std::string SIGNAL_ENV_STREAM = "signal_env_state";
};




#endif //SPLITFLOWCONTROLMODULE_HPP
