#ifndef STN_COMPONENTFACTORY_HPP
#define STN_COMPONENTFACTORY_HPP

#include <memory>
#include "components/data_providers/CameraRadar.hpp"
#include "components/data_providers/RedisDataProvider.hpp"
#include "components/traffic_managers/ITM.hpp"
#include "components/data_processors/DataService.hpp"
//#include "components/flow_controllers/FlowControlModule.hpp"
#include "components/signal_controllers/SignalMachine.hpp"
#include "flow_controllers/SplitFlowControlModule.hpp"

// 工厂模式
class ComponentFactory {
public:
    static std::unique_ptr<IDataProvider> createDataProvider(const std::string& redisHost, int redisPort,
        const std::string& redisPwd, const std::string& remoteRedisHost, int remoteRedisPort,
        const std::string& remoteRedisPwd, const std::string& localHostIp,
        const std::string& intersectionKey, const std::string& originStateKey,
        const std::string& algConfigKey) {

        return std::make_unique<RedisDataProvider>(redisHost, redisPort, redisPwd,
            remoteRedisHost, remoteRedisPort, remoteRedisPwd, localHostIp, intersectionKey,
            originStateKey, algConfigKey);
    }

    static std::unique_ptr<IDataProvider> createDataProvider() {

        return std::make_unique<RedisDataProvider>();
    }
    static std::unique_ptr<ITrafficManager> createTrafficManager() {
        return std::make_unique<ITM>();
    }
    static std::unique_ptr<IDataProcessor> createDataProcessor() {
        return std::make_unique<DataService>();
    }
    static std::unique_ptr<ISignalController> createSignalController() {
        return std::make_unique<SignalMachine>();
    }
    static std::unique_ptr<IFlowController> createFlowController(ISignalController* signalController, const
    std::string& redisHost, int redisPort, const std::string& redisPwd) {
        return std::make_unique<SplitFlowControlModule>(signalController, redisHost, redisPort, redisPwd);
    }

   /*
    static std::unique_ptr<IFlowController> createFlowController(ISignalController* signalController) {

        return std::make_unique<FlowControlModule>(signalController);
    }
    */
};
#endif