#include "core/SystemManager.hpp"
#include "utils/RedisClient.hpp"
#include "utils/NatsClient.hpp"
#include <csignal>
//#include "core/PythonEnvManager.hpp"
#include "utils/version_info.hpp"
#include "utils/Logger.hpp"


#define IS_HANDLE_SIGNAL(s) \
    ((s)==SIGINT||(s)==SIGTERM||(s)==SIGQUIT||(s)==SIGSEGV||(s)==SIGABRT||(s)==SIGFPE||(s)==SIGILL||(s)==SIGBUS)


// 全局或者静态的标志位，用来检测 SIGINT 信号
volatile sig_atomic_t gStopFlag = 0;
SystemManager &gSystemManager = SystemManager::getInstance();
NatsClient &gNatsClient = NatsClient::getInstance();
Logger &gLogger = Logger::getInstance();
std::string gMainName;
std::string gIntersectionCode;



// 声明一个全局变量来跟踪消息是否被接收
std::atomic<bool> g_messageReceived(false);
// 单独声明订阅回调函数
void subscriptionCallback(const std::string& channel, const std::string& message) {
    std::cout << "Received message from " << channel << ": " << message << std::endl;
    g_messageReceived = true;
}

class SignalHandler {
public:
    static constexpr int signals[] = {
        SIGINT, SIGTERM, SIGQUIT, /*SIGSEGV,*/
        SIGABRT, SIGFPE, SIGILL, SIGBUS
    };

    static bool is_handle_signal(int sig) {
        return std::find(std::begin(signals), std::end(signals), sig)
               != std::end(signals);
    }

    static void setup() {
        struct sigaction sa{};
        sa.sa_handler = handle_signal;
        sigemptyset(&sa.sa_mask);
        sa.sa_flags = 0;

        for (int sig : signals) {
            if (sigaction(sig, &sa, nullptr) == -1) {
                SPDLOG_ERROR("Failed to set up handler for signal {}", sig);
                throw std::runtime_error("Failed to set up signal handler.");
            }
            SPDLOG_DEBUG("Registered signal handler for signal {}", sig);
        }
        SPDLOG_INFO("Signal handlers setup completed");
    }

private:
    static void handle_signal(int signal) {
        if (is_handle_signal(signal)) {
            SPDLOG_INFO("Received signal {}, initiating shutdown...", signal);
            gStopFlag = 1;
            gSystemManager.shutdown(signal);
        }
    }
};


std::string getProcessNameFromArg(const char* arg) {
    if (!arg) return "";

    std::string path(arg);
    // 找到最后一个路径分隔符
    size_t pos = path.find_last_of("/\\");
    // 如果找到了分隔符，返回后面的部分（程序名）
    if (pos != std::string::npos) {
        return path.substr(pos + 1);
    }
    // 如果没有分隔符，整个字符串就是程序名
    return path;
}


int main(int argc, char** argv) {
    try {

        // 解析命令行参数，检查是否需要以守护进程模式运行
        bool runAsDaemon = false;
        for (int i = 1; i < argc; i++) {
            std::string arg = argv[i];
            if (arg == "--daemon" || arg == "-d") {
                runAsDaemon = true;
                break;
            }
        }

        // 如果指定了守护进程模式
        if (runAsDaemon) {
            // 获取进程名称
            gMainName = getProcessNameFromArg(argv[0]);
            // 守护进程模式下的处理
            const std::string pidFile = gMainName + ".pid";
            //umask(022);
            if (daemon(1, 0) == -1) {  // 将第二个参数改为0，完全关闭标准文件描述符
                throw SystemException("Failed to daemonize process: " +
                                    std::string(strerror(errno)));
            }
            // 打印版本信息
            SPDLOG_INFO("STN Build Info: \n" + version::getVersionInfo());

            // 写入PID文件
            if (FILE* fp = fopen(pidFile.c_str(), "w")) {
                fprintf(fp, "%d\n", getpid());
                fclose(fp);
            } else {
                SPDLOG_ERROR("Failed to create PID file: {}", pidFile);
            }
            SPDLOG_INFO("Starting in daemon mode.");
        } else {
            SPDLOG_INFO("Starting in normal mode");
        }
        // 打印版本信息
        SPDLOG_INFO("STN Build Info: \n" + version::getVersionInfo());
        // 初始化信号处理
        SignalHandler::setup();
        gSystemManager.initialize();

        // 主循环
        while (!gStopFlag) {
            gSystemManager.run();
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }

        // 确保清理工作完成
        LOG(INFO, "Starting cleanup process...");

        try {
            // 先关闭 NATS 客户端
            try {
                LOG(INFO, "Shutting down NATS client...");
                gNatsClient.shutdown();
                LOG(INFO, "NATS client shutdown completed.");
            } catch (const std::exception& e) {
                LOG(ERROR, "Exception shutting down NATS client: {}", e.what());
            } catch (...) {
                LOG(ERROR, "Unknown exception shutting down NATS client");
            }

            // 然后清理 SystemManager 资源
            gSystemManager.cleanup();

            // 如果是守护进程模式，删除PID文件
            if (runAsDaemon) {
                const std::string pidFile = gMainName + ".pid";
                if (unlink(pidFile.c_str()) == 0) {
                    LOG(INFO, "PID file removed successfully");
                }
            }

            LOG(INFO, "Program exit successfully");
        } catch (const std::exception& e) {
            LOG(ERROR, "Exception during cleanup: {}", e.what());
        } catch (...) {
            LOG(ERROR, "Unknown exception during cleanup");
        }
        LOG(INFO, "Waiting for cleanup to complete...");

        // 等待一小段时间，给其他线程时间清理
        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        // 强制退出程序，不等待其他线程
        LOG(INFO, "Forcing program exit...");
        std::_Exit(EXIT_SUCCESS);  // 使用 _Exit 而不是 exit，避免调用析构函数

        // 下面的代码实际上不会执行，因为 _Exit 会立即终止程序
        return EXIT_SUCCESS;
    }
    catch (const std::exception& e) {
        LOG(ERROR, "Unexpected error: {}", e.what());
        return EXIT_FAILURE;
    }
}