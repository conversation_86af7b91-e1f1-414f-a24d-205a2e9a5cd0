//
// Created by firefly on 6/11/24.
//

#ifndef RKNN_YOLOV5_DEMO_THREADPOOL_H
#define RKNN_YOLOV5_DEMO_THREADPOOL_H

#include <vector>
#include <queue>
#include <future>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <iostream>
#include <spdlog/spdlog.h>

class ThreadPool {
public:
    explicit ThreadPool(size_t);
    ~ThreadPool();

    template<class F, class... Args>
    auto enqueue(F&& f, Args&&... args) -> std::future<typename std::invoke_result<F, Args...>::type>;

private:
    std::vector<std::thread> workers;
    std::queue<std::function<void()>> tasks;

    std::mutex queue_mutex;
    std::condition_variable condition;
    bool stop;
};

inline ThreadPool::ThreadPool(size_t threads) : stop(false) {
    SPDLOG_INFO("ThreadPool constructor for {} threads.", threads);
    for (size_t i = 0; i < threads; ++i)
        workers.emplace_back([=] {
            //std::cout << "Start Thread id " << std::this_thread::get_id() << std::endl;
            std::stringstream ss;
            ss << std::this_thread::get_id();
            SPDLOG_INFO("Starting thread {} - {}", i, ss.str());
            for (;;) {
                std::function<void()> task;
                {
                    std::unique_lock<std::mutex> lock(this->queue_mutex);
                    this->condition.wait(lock, [this] { return this->stop || !this->tasks.empty(); });
                    if (this->stop && this->tasks.empty()) {
                        SPDLOG_INFO("Stopping thread...");
                        return;
                    }
                    task = std::move(this->tasks.front());
                    this->tasks.pop();
                }
                task();
            }
        });
}

template<class F, class... Args>
auto ThreadPool::enqueue(F&& f, Args&&... args) -> std::future<typename std::invoke_result<F, Args...>::type> {
    using return_type = typename std::invoke_result<F, Args...>::type;

    auto task = std::make_shared<std::packaged_task<return_type()>>(std::bind(std::forward<F>(f), std::forward<Args>(args)...));

    std::future<return_type> res = task->get_future();
    {
        std::unique_lock<std::mutex> lock(queue_mutex);

        if (stop)
            throw std::runtime_error("enqueue on stopped ThreadPool");

        tasks.emplace([task]() { (*task)(); });
    }
    condition.notify_one();
    return res;
}

inline ThreadPool::~ThreadPool() {
    {
        std::unique_lock<std::mutex> lock(queue_mutex);
        stop = true;
    }
    condition.notify_all();
    for (std::thread& worker : workers) {
        std::stringstream ss;
        ss << worker.get_id();  // 将线程 ID 转换为字符串
        if (worker.joinable()) {
            worker.join();
        }
    }
    SPDLOG_INFO("ThreadPool destructor destoryed All threads.");
}



#endif //RKNN_YOLOV5_DEMO_THREADPOOL_H