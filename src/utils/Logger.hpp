#ifndef STN_LOGGER_HPP_H
#define STN_LOGGER_HPP_H

#include <spdlog/spdlog.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <mutex>
#include <string>
#include <cstdarg>
#include <memory>
#include <filesystem>
#include <iostream>

class Logger {
public:
    enum class Level {DEBUG, INFO, WARNING, ERROR};

    struct LogConfig {
        std::string logDir;            // 日志目录
        std::string baseFileName;      // 基础文件名
        bool enableConsole;            // 是否启用控制台输出
        bool enableRotating;           // 是否启用文件大小分割
        bool enableDaily;              // 是否启用每日分割
        bool enableStartupFile;        // 是否启动时新建日志文件
        size_t maxFileSize;            // 单个文件最大大小
        size_t maxFiles;               // 最大保留文件数
        int rotateHour;                // 每日轮转时间（小时）
        int rotateMinute;              // 每日轮转时间（分钟）

        // 构造函数提供默认值
        LogConfig()
            : logDir("logs")
            , baseFileName("stn")
            , enableConsole(true)
            , enableRotating(false)
            , enableDaily(true)
            , enableStartupFile(true)
            , maxFileSize(10 * 1024 * 1024)  // 5MB
            , maxFiles(5)
            , rotateHour(0)
            , rotateMinute(0)
        {}
    };

    static Logger& getInstance() {
        static Logger instance;
        return instance;
    }

    // 初始化日志系统
    void init(const LogConfig& config = LogConfig()) {
        std::lock_guard<std::mutex> lock(mutex_);
        config_ = config;

        try {
            std::vector<spdlog::sink_ptr> sinks;

            // 确保日志目录存在
            std::filesystem::create_directories(config_.logDir);

            // 始终添加文件日志sink，确保daemon模式下也能正常工作
            if (config_.enableDaily) {
                auto daily_sink = std::make_shared<spdlog::sinks::daily_file_sink_mt>(
                    config_.logDir + "/" + config_.baseFileName + "_daily.log",
                    config_.rotateHour,
                    config_.rotateMinute,
                    true,  // 强制立即打开文件
                    config_.maxFiles
                );
                daily_sink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%P] [%l] %v");
                daily_sink->set_level(spdlog::level::trace);
                sinks.push_back(daily_sink);
            }

            if (config_.enableRotating) {
                auto rotating_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                    config_.logDir + "/" + config_.baseFileName + "_rotating.log",
                    config_.maxFileSize,
                    config_.maxFiles,
                    true  // 强制立即打开文件
                );
                rotating_sink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%P] [%l] %v");
                rotating_sink->set_level(spdlog::level::trace);
                sinks.push_back(rotating_sink);
            }

            // 控制台输出作为可选sink
            if (config_.enableConsole) {
                auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
                console_sink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%P] [%^%l%$] %v");
                console_sink->set_level(spdlog::level::trace);
                sinks.push_back(console_sink);
            }

            // 创建多sink logger
            auto combined_logger = std::make_shared<spdlog::logger>(
                "combined_logger",
                begin(sinks),
                end(sinks)
            );

            // 配置logger
            combined_logger->set_level(spdlog::level::trace);
            combined_logger->flush_on(spdlog::level::info);  // info及以上级别立即刷新
            combined_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%P] [%l] %v");

            // 设置为默认logger
            spdlog::set_default_logger(combined_logger);

            // 设置定期刷新策略
            spdlog::flush_every(std::chrono::seconds(1));

        } catch (const spdlog::spdlog_ex& ex) {
            std::cerr << "Log initialization failed: " << ex.what() << std::endl;
        }
    }

    static void setLogLevel(const Level level) {
        switch (level) {
            case Level::DEBUG:
                spdlog::set_level(spdlog::level::debug);
                spdlog::flush_on(spdlog::level::debug);
                break;
            case Level::INFO:
                spdlog::set_level(spdlog::level::info);
                spdlog::flush_on(spdlog::level::info);
                break;
            case Level::WARNING:
                spdlog::set_level(spdlog::level::warn);
                spdlog::flush_on(spdlog::level::warn);
                break;
            case Level::ERROR:
                spdlog::set_level(spdlog::level::err);
                spdlog::flush_on(spdlog::level::err);
                break;
            default:
                spdlog::set_level(spdlog::level::info);
                spdlog::flush_on(spdlog::level::info);
                break;
        }
    }

#if 0
    // 支持可变参数的日志记录
    void log(const Level level, const char* file, int line, const char* format, ...) {
        std::lock_guard<std::mutex> lock(mutex_);

        va_list args;
        va_start(args, format);
        char buffer[1024];
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);

        std::string message = formatMessage(file, line, buffer);
        logMessage(level, message);
    }
#endif

    // 处理纯字符串的情况
    void log(const Level level, const char* file, int line, const std::string& msg) {
        std::lock_guard<std::mutex> lock(mutex_);
        std::string message = formatMessage(file, line, msg);
        logMessage(level, message);
    }

    void log(const Level level, const char* file, int line, const char* msg) {
        std::lock_guard<std::mutex> lock(mutex_);
        std::string message = formatMessage(file, line, msg);
        logMessage(level, message);
    }

    // 委托给 spdlog 处理日志记录
    template<typename... Args>
    void log(const Level level, const char* file, int line, spdlog::string_view_t fmt, const Args &...args) {
        std::lock_guard<std::mutex> lock(mutex_);
        std::string message = formatMessage(file, line, fmt::format(fmt, args...));
        logMessage(level, message);
    }

    // 简化reconfigureForDaemon，只需禁用控制台输出
    void reconfigureForDaemon() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (config_.enableConsole) {
            config_.enableConsole = false;
            init(config_);  // 重新初始化，但保持文件日志配置不变
        }
    }

    static std::string getFileName(const std::string& fullPath) {
        size_t lastSlash = fullPath.find_last_of("/\\");
        return (lastSlash != std::string::npos) ? fullPath.substr(lastSlash + 1) : fullPath;
    }

private:
    Logger() = default;
    std::mutex mutex_;
    LogConfig config_;

    std::string getStartupLogFileName() const {
        auto now = std::chrono::system_clock::now();
        auto now_time_t = std::chrono::system_clock::to_time_t(now);
        auto now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        std::stringstream ss;
        ss << config_.logDir << "/" << config_.baseFileName << "_";
        ss << std::put_time(std::localtime(&now_time_t), "%Y%m%d_%H%M%S");
        ss << "_" << std::setfill('0') << std::setw(3) << now_ms.count() << ".log";
        return ss.str();
    }

    static std::string formatMessage(const char* file, int line, const std::string& message) {
        return "[" + std::string(file) + ":" + std::to_string(line) + "] " + message;
    }

    void logMessage(Level level, const std::string& message) {
        switch (level) {
            case Level::DEBUG:
                spdlog::debug(message);
                break;
            case Level::INFO:
                spdlog::info(message);
                break;
            case Level::WARNING:
                spdlog::warn(message);
                break;
            case Level::ERROR:
                spdlog::error(message);
                break;
            default:
                spdlog::info("UNKNOWN: " + message);
                break;
        }
    }
    ~Logger() {spdlog::shutdown();}
};

// 日志宏定义
#define LOG(level, ...) Logger::getInstance().log(Logger::Level::level, \
    Logger::getFileName(__FILE__).c_str(), __LINE__, ##__VA_ARGS__)

#endif // STN_LOGGER_HPP_H