//
// Created by lth on 2024/10/24.
//

#ifndef IPADDRESS_HPP
#define IPADDRESS_HPP

#include <string>
#include <vector>
#include <stdexcept>
#include <cstring>
#include <ifaddrs.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <unistd.h>

class NetworkInterface {
public:
    static NetworkInterface& getInstance() {
        static NetworkInterface instance;
        return instance;
    }
    // 网络接口信息结构
    struct InterfaceInfo {
        std::string name;    // 网卡名称
        std::string ip;      // IP地址
        bool isUp;          // 是否启用
        bool isLoopback;    // 是否是回环接口

        // 构造函数
        InterfaceInfo(const std::string& n = "", const std::string& i = "",
                     bool up = false, bool loop = false)
            : name(n), ip(i), isUp(up), isLoopback(loop) {}
    };

    // 获取所有网络接口信息
    static std::vector<InterfaceInfo> getAllInterfaces() {
        std::vector<InterfaceInfo> interfaces;
        struct ifaddrs *ifaddr, *ifa;

        if (getifaddrs(&ifaddr) == -1) {
            throw std::runtime_error("Failed to get network interfaces");
        }

        for (ifa = ifaddr; ifa != nullptr; ifa = ifa->ifa_next) {
            // 跳过空地址
            if (ifa->ifa_addr == nullptr) {
                continue;
            }

            // 只处理IPv4地址
            if (ifa->ifa_addr->sa_family != AF_INET) {
                continue;
            }

            InterfaceInfo info;
            info.name = ifa->ifa_name;
            info.isUp = (ifa->ifa_flags & IFF_UP) != 0;
            info.isLoopback = (ifa->ifa_flags & IFF_LOOPBACK) != 0;

            // 获取IP地址
            char ip[INET_ADDRSTRLEN];
            void* addr = &((struct sockaddr_in*)ifa->ifa_addr)->sin_addr;
            inet_ntop(AF_INET, addr, ip, INET_ADDRSTRLEN);
            info.ip = ip;

            interfaces.push_back(info);
        }

        freeifaddrs(ifaddr);
        return interfaces;
    }

    // 获取指定网卡的IP地址
    static std::string getInterfaceIP(const std::string& interfaceName) {
        auto interfaces = getAllInterfaces();
        for (const auto& interface : interfaces) {
            if (interface.name == interfaceName && !interface.isLoopback) {
                return interface.ip;
            }
        }
        return "";
    }


    // 获取指定网卡的IP地址
    static std::string getDefaultInterfaceIP() {
        auto interfaces = getAllInterfaces();
        for (const auto& interface : interfaces) {
            if (!interface.isLoopback && interface.isUp) {
                return interface.ip;
            }
        }
        return "";
    }

    // 打印所有网络接口信息
    static void printAllInterfaces() {
        try {
            auto interfaces = getAllInterfaces();
            printf("\n找到 %zu 个网络接口：\n", interfaces.size());
            for (const auto& interface : interfaces) {
                printf("\n接口名称: %s\n"
                       "IP 地址: %s\n"
                       "状态: %s\n"
                       "类型: %s\n",
                       interface.name.c_str(),
                       interface.ip.c_str(),
                       interface.isUp ? "启用" : "禁用",
                       interface.isLoopback ? "回环" : "物理");
            }
        } catch (const std::exception& e) {
            printf("Error: %s\n", e.what());
        }
    }
};




#endif //IPADDRESS_HPP
