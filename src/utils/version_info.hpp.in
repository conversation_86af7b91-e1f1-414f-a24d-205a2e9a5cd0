//
// Created by lth on 2025/4/1.
//

#ifndef SMARTTRAFFICNEXUS_VERSION_INFO_HPP_IN_HPP
#define SMARTTRAFFICNEXUS_VERSION_INFO_HPP_IN_HPP


// 自动生成的版本信息 - 请勿手动修改
#pragma once
#include <string>
#include <sstream>


namespace version {
    const std::string kBuildVersion = "@GIT_VERSION@";
    const std::string kBuildDate = "@BUILD_DATE@";
    const std::string kCommitMessage = "@GIT_COMMIT_MSG@";
    const std::string kGitBranch = "@GIT_BRANCH@";
    const std::string kGitTag = "@GIT_TAG@";
    const std::string kGitHash = "@GIT_HASH@";


inline std::string getVersionInfo() {
    std::ostringstream oss;
    oss << "Version Information:\n"
        << "  Build Version: " << "@GIT_VERSION@" << "\n"
        << "  Build Date:    " << "@BUILD_DATE@" << "\n"
        << "  Git Branch:    " << "@GIT_BRANCH@" << "\n"
        << "  Git Tag:       " << "@GIT_TAG@" << "\n"
        << "  Commit Hash:   " << "@GIT_HASH@" << "\n"
        << "  Last Commit:   " << "@GIT_COMMIT_MSG@";
    return oss.str();
}
}














#endif //SMARTTRAFFICNEXUS_VERSION_INFO_HPP_IN_HPP
