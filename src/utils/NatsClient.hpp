//
// Created by lth on 2024/10/23.
//

#ifndef NATSCLIENT_HPP
#define NATSCLIENT_HPP

#include <nats/nats.h>
#include <string>
#include <memory>
#include <functional>
#include <mutex>
#include <queue>
#include <thread>
#include <atomic>
#include <vector>
#include <iostream>
#include <csignal>

// 声明外部的停止标志
extern volatile sig_atomic_t gStopFlag;

class NatsClient {
public:
    // 配置结构
    struct Config {
        std::string url;              // NATS服务器地址
        std::string username;         // 用户名（可选）
        std::string password;         // 密码（可选）
        int reconnectWaitTime;        // 重连等待时间(ms)
        int maxReconnectAttempts;     // 最大重连次数
        int64_t connectTimeout;       // 连接超时时间(ms)
        int64_t requestTimeout;       // 请求超时时间(ms)
        bool verbose;                 // 是否打印详细日志

        Config()
            : url("nats://localhost:4222")
            , username("")
            , password("")
            , reconnectWaitTime(2000)
            , maxReconnectAttempts(60)
            , connectTimeout(5000)    // 默认5秒连接超时
            , requestTimeout(2000)    // 默认2秒请求超时
            , verbose(false)
        {}
    };

    // 消息回调函数类型
    using MessageCallback = std::function<void(const std::string&, const std::string&)>;
    // 定义带返回值的消息回调函数类型
    using MessageCallbackWithReply = std::function<std::string(const std::string&, const std::string&)>;

    static NatsClient& getInstance() {
        static NatsClient instance;
        return instance;
    }

    // 初始化NATS客户端
    bool init(const Config& config = Config()) {
        std::lock_guard<std::mutex> lock(mutex_);

        if (isInitialized_) {
            return true;
        }

        // 如果已经设置了停止标志，则不进行初始化
        if (gStopFlag) {
            lastError_ = "Initialization aborted due to stop flag";
            return false;
        }

        config_ = config;

        natsStatus status = nats_Open(-1);
        if (status != NATS_OK) {
            lastError_ = std::string("Failed to initialize NATS: ") + natsStatus_GetText(status);
            return false;
        }

        isInitialized_ = true;
        return connect();
    }

    // 连接到NATS服务器
    bool connect() {
        if (gStopFlag) {
            lastError_ = "Connection aborted due to stop flag";
            return false;
        }

        if (isConnected_) {
            return true;
        }

        natsOptions *opts = nullptr;
        natsStatus status = natsOptions_Create(&opts);
        if (status != NATS_OK) {
            lastError_ = std::string("Failed to create options: ") + natsStatus_GetText(status);
            return false;
        }

        // 设置连接和请求超时
        status = natsOptions_SetTimeout(opts, config_.connectTimeout);

        // 设置其他选项
        status = natsOptions_SetURL(opts, config_.url.c_str());
        status = natsOptions_SetReconnectWait(opts, config_.reconnectWaitTime);
        status = natsOptions_SetMaxReconnect(opts, config_.maxReconnectAttempts);
        status = natsOptions_SetVerbose(opts, config_.verbose);

        if (!config_.username.empty() && !config_.password.empty()) {
            status = natsOptions_SetUserInfo(opts,
                config_.username.c_str(),
                config_.password.c_str());
        }

        // 设置回调
        status = natsOptions_SetErrorHandler(opts, onErrorHandler, this);
        status = natsOptions_SetReconnectedCB(opts, onReconnectedCB, this);
        status = natsOptions_SetDisconnectedCB(opts, onDisconnectedCB, this);
        status = natsOptions_SetClosedCB(opts, onClosedCB, this);

        // 连接到服务器
        status = natsConnection_Connect(&conn_, opts);
        natsOptions_Destroy(opts);

        if (status != NATS_OK) {
            lastError_ = std::string("Connect failed: ") + natsStatus_GetText(status);
            return false;
        }

        isConnected_ = true;
        return true;
    }

    // 发布消息
    bool publish(const std::string& subject, const std::string& message) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (gStopFlag) {
            lastError_ = "Operation aborted due to stop flag";
            return false;
        }

        if (!checkConnection()) {
            return false;
        }

        natsStatus status = natsConnection_Publish(conn_,
            subject.c_str(),
            message.c_str(),
            message.length());

        if (status != NATS_OK) {
            lastError_ = std::string("Publish failed: ") + natsStatus_GetText(status);
            return false;
        }

        status = natsConnection_Flush(conn_);
        if (status != NATS_OK) {
            lastError_ = std::string("Flush failed: ") + natsStatus_GetText(status);
            return false;
        }

        return true;
    }

    // 请求-响应模式
    bool request(const std::string& subject, const std::string& message,
                std::string& response) {
       std::lock_guard<std::mutex> lock(mutex_);
        if (gStopFlag) {
            lastError_ = "Operation aborted due to stop flag";
            return false;
        }

        if (!checkConnection()) {
            return false;
        }

        natsMsg* reply = nullptr;
        natsStatus status = natsConnection_RequestString(&reply, conn_,
            subject.c_str(), message.c_str(), config_.requestTimeout);

        if (status != NATS_OK) {
            lastError_ = std::string("Request failed: ") + natsStatus_GetText(status);
            return false;
        }

        response = std::string(natsMsg_GetData(reply), natsMsg_GetDataLength(reply));
        natsMsg_Destroy(reply);

        return true;
    }

    // 带自定义超时的请求
    bool requestWithTimeout(const std::string& subject, const std::string& message,
                          std::string& response, int64_t timeout_ms) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (gStopFlag) {
            lastError_ = "Operation aborted due to stop flag";
            return false;
        }

        if (!checkConnection()) {
            return false;
        }

        natsMsg* reply = nullptr;
        natsStatus status = natsConnection_RequestString(&reply, conn_,
            subject.c_str(), message.c_str(), timeout_ms);

        if (status != NATS_OK) {
            lastError_ = std::string("Request failed: ") + natsStatus_GetText(status);
            return false;
        }

        response = std::string(natsMsg_GetData(reply), natsMsg_GetDataLength(reply));
        natsMsg_Destroy(reply);

        return true;
    }

    // 订阅主题
    bool subscribe(const std::string& subject, MessageCallback callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (gStopFlag) {
            lastError_ = "Operation aborted due to stop flag";
            return false;
        }

        if (!checkConnection()) {
            return false;
        }

        natsSubscription* subscription = nullptr;
        natsStatus status = natsConnection_Subscribe(&subscription, conn_,
            subject.c_str(), onMessageCallback, new MessageCallback(callback));

        if (status != NATS_OK) {
            lastError_ = std::string("Subscribe failed: ") + natsStatus_GetText(status);
            return false;
        }

        subscriptions_.push_back(subscription);
        return true;
    }

    // 订阅带返回值的主题
    bool subscribeWithReply(const std::string& subject, MessageCallbackWithReply callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (!checkConnection()) {
            return false;
        }

        auto callbackWrapper = new MessageCallbackWithReply(std::move(callback));

        natsSubscription* subscription = nullptr;
        natsStatus status = natsConnection_Subscribe(&subscription, conn_,
            subject.c_str(), onMessageCallbackWithReply, callbackWrapper);

        if (status != NATS_OK) {
            delete callbackWrapper;
            lastError_ = std::string("Subscribe failed: ") + natsStatus_GetText(status);
            return false;
        }

        subscriptions_.push_back(subscription);
        return true;
    }

    void shutdown() {
        try {
            // 首先标记状态为未初始化，防止其他线程继续使用
            {
                std::lock_guard<std::mutex> lock(mutex_);
                isConnected_ = false;
                isInitialized_ = false;
            }

            // 先清理所有订阅
            std::vector<natsSubscription*> subs_copy;
            {
                std::lock_guard<std::mutex> lock(mutex_);
                subs_copy = subscriptions_;
                subscriptions_.clear();
            }

            for (auto& sub : subs_copy) {
                if (sub != nullptr) {
                    try {
                        // 设置超时，防止阻塞
                        natsSubscription_SetPendingLimits(sub, 0, 0);
                        natsSubscription_Unsubscribe(sub);
                        natsSubscription_Destroy(sub);
                    } catch (...) {
                        // 忽略异常，继续清理
                    }
                }
            }

            // 然后关闭连接
            natsConnection* conn_copy = nullptr;
            {
                std::lock_guard<std::mutex> lock(mutex_);
                conn_copy = conn_;
                conn_ = nullptr;
            }

            if (conn_copy != nullptr) {
                try {
                    // 设置超时，防止阻塞
                    natsOptions* options = nullptr;
                    natsOptions_Create(&options);
                    natsOptions_SetClosedCB(options, nullptr, nullptr);
                    natsOptions_SetDisconnectedCB(options, nullptr, nullptr);
                    natsOptions_SetReconnectedCB(options, nullptr, nullptr);

                    // 关闭连接
                    natsConnection_Close(conn_copy);
                    natsConnection_Destroy(conn_copy);

                    if (options != nullptr) {
                        natsOptions_Destroy(options);
                    }
                } catch (...) {
                    // 忽略异常
                }
            }

            std::cerr << "NATS client shutdown completed successfully" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Exception in NatsClient::shutdown: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "Unknown exception in NatsClient::shutdown" << std::endl;
        }
    }

    std::string getLastError() const { return lastError_; }
    bool isConnected() const { return isConnected_; }
    bool isInitialized() const { return isInitialized_; }

private:
    // 成员变量声明顺序要与构造函数初始化列表顺序一致
    std::atomic<bool> isConnected_;
    std::atomic<bool> isInitialized_;
    natsConnection* conn_;
    Config config_;
    std::vector<natsSubscription*> subscriptions_;
    std::string lastError_;
    std::mutex mutex_;

    // 构造函数，初始化列表顺序与成员声明顺序一致
    NatsClient()
        : isConnected_(false)
        , isInitialized_(false)
        , conn_(nullptr)
        , config_()
        , subscriptions_()
        , lastError_()
        , mutex_()
    {}

    ~NatsClient() {
        shutdown();
    }
    bool checkConnection() {
        if (!isConnected_) {
            lastError_ = "Not connected to NATS server";
            return false;
        }
        return true;
    }

    // NATS回调函数
    static void onMessageCallback([[maybe_unused]] natsConnection* nc, [[maybe_unused]]natsSubscription* sub,
                                natsMsg* msg, void* closure) {
        if (gStopFlag) return;  // 如果设置了停止标志，不处理新消息

        auto callback = static_cast<MessageCallback*>(closure);
        if (callback) {
            std::string subject = natsMsg_GetSubject(msg);
            std::string data(natsMsg_GetData(msg), natsMsg_GetDataLength(msg));
            (*callback)(subject, data);
        }
        natsMsg_Destroy(msg);
    }

    // 处理带返回值的消息回调
    static void onMessageCallbackWithReply(natsConnection* nc,
                                         [[maybe_unused]]natsSubscription* sub,
                                         natsMsg* msg,
                                         void* closure) {
        if (gStopFlag) return;

        if (auto callback = static_cast<MessageCallbackWithReply*>(closure)) {
            try {
                std::string subject = natsMsg_GetSubject(msg);
                std::string data(natsMsg_GetData(msg), natsMsg_GetDataLength(msg));

                // 调用用户的回调函数获取响应
                std::string reply = (*callback)(subject, data);

                // 如果消息需要回复（有reply主题），发送响应
                if (const char* replyTo = natsMsg_GetReply(msg)) {
                    natsConnection_Publish(nc, replyTo, reply.c_str(), reply.length());
                    natsConnection_Flush(nc);
                }
            } catch (const std::exception& e) {
                std::cerr << "Error in callback: " << e.what() << std::endl;
            }
        }

        natsMsg_Destroy(msg);
    }

    static void onErrorHandler([[maybe_unused]] natsConnection *nc, [[maybe_unused]] natsSubscription *subscription,
                             natsStatus err, void *closure) {
        auto client = static_cast<NatsClient*>(closure);
        if (client) {
            client->lastError_ = natsStatus_GetText(err);
            std::cerr << "NATS error: " << client->lastError_ << std::endl;
        }
    }

    static void onReconnectedCB([[maybe_unused]] natsConnection* nc, void* closure) {
        if (gStopFlag) return;
        auto client = static_cast<NatsClient*>(closure);
        client->isConnected_ = true;
    }

    static void onDisconnectedCB([[maybe_unused]] natsConnection* nc, void* closure) {
        auto client = static_cast<NatsClient*>(closure);
        client->isConnected_ = false;
    }

    static void onClosedCB([[maybe_unused]] natsConnection* nc, void* closure) {
        auto client = static_cast<NatsClient*>(closure);
        client->isConnected_ = false;
    }
};



#endif //NATSCLIENT_HPP
