//
// Created by lth on 2024/11/1.
//

#ifndef UDPCLIENT_HPP
#define UDPCLIENT_HPP

#include <string>
#include <stdexcept>
#include <memory>
#include <mutex>
#include <thread>
#include <functional>
#include <atomic>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <fcntl.h>
#include <unistd.h>
#include <cstring>
#include <iostream>
#include <system_error>
#include <netdb.h>

class UDPClient {
public:
    // 定义回调函数类型
    using MessageCallback = std::function<void(const std::string&, const std::string&, uint16_t)>;

    explicit UDPClient(const std::string& serverIp = "127.0.0.1",
                      uint16_t serverPort = 8888,
                      size_t receiveBufferSize = 4096)
        : m_serverIp(serverIp)
        , m_serverPort(serverPort)
        , m_socket(-1)
        , m_receiveBufferSize(receiveBufferSize)
        , m_isReceiving(false) {
        init();
    }

    ~UDPClient() {
        try {
            // 确保异步接收线程已停止
            m_isReceiving = false;
            if (m_receiveThread.joinable()) {
                m_receiveThread.join();
            }

            // 关闭套接字
            if (m_socket != -1) {
                close(m_socket);
                m_socket = -1;
            }
        } catch (const std::exception& e) {
            std::cerr << "Exception in UDPClient destructor: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "Unknown exception in UDPClient destructor" << std::endl;
        }
    }

    // 禁用拷贝构造和赋值操作
    UDPClient(const UDPClient&) = delete;
    UDPClient& operator=(const UDPClient&) = delete;

    // 发送消息接口
    bool sendMessage(const std::string& message) {
        std::lock_guard<std::mutex> lock(m_mutex);
        return sendToServer(message.c_str(), message.length());
    }

    // 发送二进制数据接口
    bool sendData(const void* data, size_t length) {
        std::lock_guard<std::mutex> lock(m_mutex);
        return sendToServer(data, length);
    }

    // 同步接收消息（带超时）
    std::optional<std::string> receiveMessage(int timeoutMs = 1000) const {
        std::vector<char> buffer(m_receiveBufferSize);
        struct sockaddr_in senderAddr;
        socklen_t senderLen = sizeof(senderAddr);

        // 设置超时
        struct timeval tv;
        tv.tv_sec = timeoutMs / 1000;
        tv.tv_usec = (timeoutMs % 1000) * 1000;

        if (setsockopt(m_socket, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv)) < 0) {
            throw std::system_error(errno, std::system_category(), "Failed to set socket timeout");
        }

        ssize_t bytesRead = recvfrom(m_socket,
                                    buffer.data(),
                                    buffer.size(),
                                    0,
                                    reinterpret_cast<struct sockaddr *>(&senderAddr),
                                    &senderLen);

        if (bytesRead < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                return std::nullopt; // 超时
            }
            throw std::system_error(errno, std::system_category(), "Failed to receive data");
        }

        return std::string(buffer.data(), bytesRead);
    }

    // 开始异步接收
    void startAsyncReceive(MessageCallback callback) {
        if (m_isReceiving) {
            return;
        }

        m_isReceiving = true;
        m_callback = std::move(callback);

        // 设置非阻塞模式
        int flags = fcntl(m_socket, F_GETFL, 0);
        fcntl(m_socket, F_SETFL, flags | O_NONBLOCK);

        // 启动接收线程
        m_receiveThread = std::thread(&UDPClient::receiveLoop, this);
    }

    // 停止异步接收
    void stopAsyncReceive() {
        m_isReceiving = false;
        if (m_receiveThread.joinable()) {
            m_receiveThread.join();
        }
    }

    void setServerAddress(const std::string& serverIp, uint16_t serverPort) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_serverIp = serverIp;
        m_serverPort = serverPort;
    }

    // 新增: 发送二进制数据并等待响应
    std::optional<std::vector<uint8_t>> sendAndReceive(const void* data,
                                                      size_t length,
                                                      int timeoutMs = 1000) {
        std::lock_guard<std::mutex> lock(m_mutex);

        // 发送数据
        if (!sendToServer(data, length)) {
            return std::nullopt;
        }

        // 接收响应
        std::vector<char> buffer(m_receiveBufferSize);
        struct sockaddr_in senderAddr;
        socklen_t senderLen = sizeof(senderAddr);

        // 设置接收超时
        struct timeval tv;
        tv.tv_sec = timeoutMs / 1000;
        tv.tv_usec = (timeoutMs % 1000) * 1000;

        if (setsockopt(m_socket, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv)) < 0) {
            return std::nullopt;
        }

        ssize_t bytesRead = recvfrom(m_socket,
                                    buffer.data(),
                                    buffer.size(),
                                    0,
                                    reinterpret_cast<struct sockaddr*>(&senderAddr),
                                    &senderLen);

        if (bytesRead < 0) {
            return std::nullopt;
        }

        // 将接收到的数据转换为vector<uint8_t>
        return std::vector<uint8_t>(buffer.data(), buffer.data() + bytesRead);
    }

    // 新增: 实现send接口(给QueryExecutor使用)
    bool send(const uint8_t* data, size_t length) {
        return sendData(data, length);
    }

    // 新增: 实现receive接口(给QueryExecutor使用)
    size_t receive(uint8_t* buffer, size_t maxLength, int timeoutMs = 1000) const {
        auto result = receiveMessage(timeoutMs);
        if (!result) {
            return 0;
        }

        size_t copyLength = std::min(maxLength, result->length());
        std::memcpy(buffer, result->data(), copyLength);
        return copyLength;
    }

private:
    std::string m_serverIp;
    uint16_t m_serverPort;
    int m_socket;
    std::mutex m_mutex;
    size_t m_receiveBufferSize;

    // 异步接收相关成员
    std::atomic<bool> m_isReceiving;
    std::thread m_receiveThread;
    MessageCallback m_callback;

    void init() {
        m_socket = socket(AF_INET, SOCK_DGRAM, 0);
        if (m_socket == -1) {
            throw std::runtime_error("Failed to create socket");
        }
    }

    bool sendToServer(const void* data, size_t length) const {
        struct sockaddr_in serverAddr = {};
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(m_serverPort);

        // 尝试将m_serverIp解析为IP地址
        if (inet_pton(AF_INET, m_serverIp.c_str(), &serverAddr.sin_addr) <= 0) {
            // 如果不是有效的IP地址，尝试将其作为域名解析
            struct addrinfo hints = {};
            struct addrinfo* result;

            hints.ai_family = AF_INET;      // 使用IPv4
            hints.ai_socktype = SOCK_DGRAM; // UDP套接字

            int status = getaddrinfo(m_serverIp.c_str(), nullptr, &hints, &result);
            if (status != 0) {
                throw std::runtime_error("域名解析失败: " + std::string(gai_strerror(status)));
            }

            // 使用第一个解析结果
            memcpy(&serverAddr.sin_addr,
                   &((struct sockaddr_in*)result->ai_addr)->sin_addr,
                   sizeof(serverAddr.sin_addr));

            freeaddrinfo(result);
        }

        ssize_t bytesSent = sendto(m_socket,
                                  data,
                                  length,
                                  0,
                                  reinterpret_cast<struct sockaddr *>(&serverAddr),
                                  sizeof(serverAddr));

        return bytesSent == static_cast<ssize_t>(length);
    }

    void receiveLoop() {
        std::vector<char> buffer(m_receiveBufferSize);
        struct sockaddr_in senderAddr;
        socklen_t senderLen = sizeof(senderAddr);

        while (m_isReceiving) {
            ssize_t bytesRead = recvfrom(m_socket,
                                       buffer.data(),
                                       buffer.size(),
                                       0,
                                       reinterpret_cast<struct sockaddr *>(&senderAddr),
                                       &senderLen);

            if (bytesRead > 0) {
                std::string message(buffer.data(), bytesRead);
                std::string senderIp(inet_ntoa(senderAddr.sin_addr));
                uint16_t senderPort = ntohs(senderAddr.sin_port);

                if (m_callback) {
                    m_callback(message, senderIp, senderPort);
                }
            } else if (bytesRead < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
                // 处理错误情况
                std::cerr << "Receive error: " << std::strerror(errno) << std::endl;
            }

            // 短暂休眠，避免CPU占用过高
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
};







#endif //UDPCLIENT_HPP
