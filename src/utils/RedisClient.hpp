#ifndef SMARTTRAFFICNEXUS_REDISCLIENT_HPP
#define SMARTTRAFFICNEXUS_REDISCLIENT_HPP

#include <stdexcept>
#include <thread>
#include <utility>
#include <optional>
#include <atomic>
#include <functional>
#include <iostream>
#include <mutex>
#include <hiredis/hiredis.h>
#include <hiredis/async.h>
#include <hiredis/adapters/libevent.h>
#include <spdlog/spdlog.h>

// 解决一个编译问题
#if 0
inline void *hi_malloc(unsigned long size) {
    return malloc(size);
}
#endif

// 前向声明，避免在头文件中包含完整的hiredis头文件
struct redisContext;
struct redisAsyncContext;
struct event_base;

class RedisClient {
public:
    explicit RedisClient(const std::string &host = "localhost", int port = 6379, std::string password = "");

    ~RedisClient();

    // 禁用拷贝构造和赋值操作
    RedisClient(const RedisClient &) = delete;

    RedisClient &operator=(const RedisClient &) = delete;

    // 基本操作
    [[maybe_unused]] void auth(const std::string &pass) const;

    void set(const std::string &key, const std::string &value);

    std::optional<std::string> get(const std::string &key, int db = 0);

    bool del(const std::string &key) const;

    bool exists(const std::string &key) const;

    void xadd(const std::string &key, const std::string &id, const std::string &field, const std::string &value);

    void xadd_withtrim(const std::string &key, const std::string &id, const std::string &field,
                       const std::string &value) const;

    int incr(const std::string &key) const;

    void xtrim(const std::string &key, int value);

    void selectDB(int db);

    // 事务操作
    void multi();

    void exec();

    [[maybe_unused]] void discard();

    // 发布操作
    void publish(const std::string &channel, const std::string &message);

    // 订阅操作
    using MessageCallback = std::function<void(const std::string &, const std::string &)>;

    void subscribe(const std::string &channel, MessageCallback callback);

    [[maybe_unused]] void unsubscribe(const std::string &channel) const;

    void unsubscribeAll();

    // 获取Stream中最新的一条数据
    std::optional<std::pair<std::string, std::string> > getLatestFromStream(const std::string &key, const int db = 0);

private:
    redisContext *m_context;
    redisAsyncContext *m_subscriber;
    event_base *m_event_base;
    MessageCallback m_callback;
    std::atomic<bool> m_is_subscribing;
    std::string m_password;
    mutable std::mutex m_mutex; // 添加 mutable 关键字
    int m_maxlen4stream{5000};
    std::thread m_subscription_thread; // Add thread object to manage subscription thread

    // 添加所有需要的内部无锁版本方法
    void selectDB_internal(int db);
    static void handleReply_internal(redisReply *reply, const std::string &command);

    static void subscriptionCallback(redisAsyncContext *c, void *r, void *privdata);
};

inline RedisClient::RedisClient(const std::string &host, int port, std::string password)
    : m_context(nullptr), m_subscriber(nullptr), m_event_base(nullptr), m_is_subscribing(false),
      m_password(std::move(password)) {
    m_context = redisConnect(host.c_str(), port);
    if (m_context == nullptr) {
        throw std::runtime_error("Redis连接分配错误");
    }
    if (m_context->err) {
        std::string error = m_context->errstr;
        if (error.empty()) {
            error = "连接错误，但没有返回错误消息";
        }
        SPDLOG_ERROR("RedisClient error: {}:{}", m_context->err, error);
        redisFree(m_context);
        throw std::runtime_error("Redis连接错误: " + error);
    }
    if (!m_password.empty()) {
        auto *reply = static_cast<redisReply *>(
            redisCommand(m_context, "AUTH %s", m_password.c_str())
        );
        handleReply_internal(reply, "AUTH");
    }
}

inline RedisClient::~RedisClient() {
    // First stop the subscription thread
    m_is_subscribing = false;
    if (m_subscription_thread.joinable()) {
        m_subscription_thread.join();
    }

    // Then unsubscribe and clean up resources
    unsubscribeAll();
    if (m_context) {
        redisFree(m_context);
        m_context = nullptr;
    }
    if (m_subscriber) {
        redisAsyncFree(m_subscriber);
        m_subscriber = nullptr;
    }
    if (m_event_base) {
        event_base_free(m_event_base);
        m_event_base = nullptr;
    }
}

inline void RedisClient::set(const std::string &key, const std::string &value) {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context, "SET %s %s", key.c_str(), value.c_str())
    );
    handleReply_internal(reply, "SET");
}

inline void RedisClient::xadd(const std::string &key, const std::string &id, const std::string &field,
                              const std::string &value) {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context, "XADD %s %s %s %s", key.c_str(), id.c_str(), field.c_str(), value.c_str())
    );
    handleReply_internal(reply, "XADD");
}

inline void RedisClient::xtrim(const std::string &key, const int value) {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context, "XTRIM %s MAXLEN ~ %d", key.c_str(), value)
    );
    handleReply_internal(reply, "XTRIM");
}

inline void RedisClient::selectDB(int db) {
    std::lock_guard<std::mutex> lock(m_mutex);
    selectDB_internal(db);
}

inline void RedisClient::selectDB_internal(int db) {
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context, "SELECT %d", db)
    );
    handleReply_internal(reply, "SELECT DB");
}

inline int RedisClient::incr(const std::string &key) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context, "INCR %s", key.c_str())
    );

    if (!reply) {
        throw std::runtime_error("Redis INCR command failed");
    }

    int value = 0;
    if (reply->type == REDIS_REPLY_INTEGER) {
        value = static_cast<int>(reply->integer);
    } else {
        throw std::runtime_error("Unexpected Redis INCR reply type");
    }
    freeReplyObject(reply);
    return value;
}


inline std::optional<std::string> RedisClient::get(const std::string &key, const int db) {
    std::lock_guard<std::mutex> lock(m_mutex);
    selectDB_internal(db);
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context, "GET %s", key.c_str())
    );
    std::optional<std::string> result;
    if (reply && reply->type == REDIS_REPLY_STRING) {
        result = std::string(reply->str, reply->len);
    }
    freeReplyObject(reply);
    return result;
}

inline bool RedisClient::del(const std::string &key) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context, "DEL %s", key.c_str())
    );
    bool result = (reply && reply->integer == 1);
    freeReplyObject(reply);
    return result;
}

inline bool RedisClient::exists(const std::string &key) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context, "EXISTS %s", key.c_str())
    );
    bool result = (reply && reply->integer == 1);
    freeReplyObject(reply);
    return result;
}

inline void RedisClient::multi() {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(redisCommand(m_context, "MULTI"));
    handleReply_internal(reply, "MULTI");
}

inline void RedisClient::exec() {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(redisCommand(m_context, "EXEC"));
    handleReply_internal(reply, "EXEC");
}

inline void RedisClient::discard() {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(redisCommand(m_context, "DISCARD"));
    handleReply_internal(reply, "DISCARD");
}

inline void RedisClient::publish(const std::string &channel, const std::string &message) {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context, "PUBLISH %s %s", channel.c_str(), message.c_str())
    );
    handleReply_internal(reply, "PUBLISH");
}

inline void RedisClient::subscribe(const std::string &channel, MessageCallback callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!m_subscriber) {
        m_event_base = event_base_new();
        m_subscriber = redisAsyncConnect(m_context->tcp.host, m_context->tcp.port);
        if (!m_password.empty()) {
            redisAsyncCommand(m_subscriber, nullptr, nullptr, "AUTH %s", m_password.c_str());
        }
        redisLibeventAttach(m_subscriber, m_event_base);
    }

    redisAsyncCommand(m_subscriber, subscriptionCallback, this, "SUBSCRIBE %s", channel.c_str());

    m_callback = std::move(callback);
    m_is_subscribing = true;

    // Use a joinable thread instead of detached thread
    if (m_subscription_thread.joinable()) {
        m_is_subscribing = false;
        m_subscription_thread.join();
    }

    m_subscription_thread = std::thread([this]() {
        while (m_is_subscribing) {
            event_base_loop(m_event_base, EVLOOP_NONBLOCK);
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    });
}

inline void RedisClient::unsubscribe(const std::string &channel) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_subscriber) {
        redisAsyncCommand(m_subscriber, nullptr, nullptr, "UNSUBSCRIBE %s", channel.c_str());
    }
}

inline void RedisClient::unsubscribeAll() {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_subscriber) {
        redisAsyncCommand(m_subscriber, nullptr, nullptr, "UNSUBSCRIBE");
        m_is_subscribing = false;

        // Wait for subscription thread to finish
        if (m_subscription_thread.joinable()) {
            m_subscription_thread.join();
        }
    }
}

inline void RedisClient::handleReply_internal(redisReply *reply, const std::string &command) {
    if (reply == nullptr) {
        throw std::runtime_error("Redis " + command + " 命令执行失败: reply nullptr.");
    } else if (reply->type == REDIS_REPLY_ERROR) {
        std::string error_msg = reply->str ? reply->str : "Unknown error";
        freeReplyObject(reply);
        throw std::runtime_error("Redis " + command + " 命令执行失败: " + error_msg);
    }
    freeReplyObject(reply);
}

inline void RedisClient::subscriptionCallback([[maybe_unused]] redisAsyncContext *c, void *r, void *privdata) {
    try {
        auto *redis_client = static_cast<RedisClient *>(privdata);
        auto *reply = static_cast<redisReply *>(r);
        if (reply && reply->type == REDIS_REPLY_ARRAY && reply->elements == 3) {
            std::string channel(reply->element[1]->str, reply->element[1]->len);
            std::string message(reply->element[2]->str, reply->element[2]->len);
            redis_client->m_callback(channel, message);
        }
    } catch (const std::exception &e) {
        std::cerr << "Error in subscription callback: " << e.what() << std::endl;
    }
}

inline void RedisClient::auth(const std::string &pass) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context, "AUTH %s", pass.c_str())
    );
    handleReply_internal(reply, "AUTH");
}

inline void RedisClient::xadd_withtrim(const std::string &key, const std::string &id, const std::string &field,
                                       const std::string &value) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto *reply = static_cast<redisReply *>(
        redisCommand(m_context,
                     "XADD %s MAXLEN ~ %d %s %s %s", key.c_str(), m_maxlen4stream, id.c_str(), field.c_str(),
                     value.c_str())
    );
    handleReply_internal(reply, "XADD with trim");
}

inline std::optional<std::pair<std::string, std::string> > RedisClient::getLatestFromStream(const std::string &key,
    const int db) {
    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        selectDB_internal(db);
        auto *reply = static_cast<redisReply *>(
            redisCommand(m_context, "XREVRANGE %s + - COUNT 1", key.c_str())
        );

        if (!reply) {
            throw std::runtime_error("Redis XREVRANGE command failed: null reply");
        }

        std::optional<std::pair<std::string, std::string> > result;

        if (reply->type == REDIS_REPLY_ARRAY && reply->elements > 0) {
            redisReply *entry = reply->element[0];
            if (entry && entry->type == REDIS_REPLY_ARRAY && entry->elements == 2) {
                redisReply *values = entry->element[1];
                if (values && values->type == REDIS_REPLY_ARRAY && values->elements >= 2) {
                    result = std::make_pair(
                        std::string(values->element[0]->str, values->element[0]->len),
                        std::string(values->element[1]->str, values->element[1]->len)
                    );
                }
            }
        }

        handleReply_internal(reply, "XREVRANGE");
        return result;
    } catch (const std::exception &e) {
        std::cerr << "Error in getLatestFromStream: " << e.what() << std::endl;
        return std::nullopt;
    }
}


#endif // SMARTTRAFFICNEXUS_REDISCLIENT_HPP
