//
// Created by x<PERSON><PERSON><PERSON> on 7/5/24.
//

#ifndef SMARTTRAFFICNEXUS_TRAFFICCONTROLSERVER_HPP
#define SMARTTRAFFICNEXUS_TRAFFICCONTROLSERVER_HPP

#include <iostream>
#include "ProtocolHandler.hpp"
#include <boost/asio.hpp>

class TrafficControlServer {
public:
    explicit TrafficControlServer(std::string  address = "0.0.0.0", int port = 1049);

    void start();
    void stop();

    void sendToClient(const std::string& message);

    bool errorCheck();

private:
    void acceptConnection();

    std::string address_;
    int port_;
    bool is_running_;
    boost::asio::io_context io_context_;
    boost::asio::ip::tcp::acceptor acceptor_;
    ProtocolHandler protocol_handler_;

};


#endif //SMARTTRAFFICNEXUS_TRAFFICCONTROLSERVER_HPP
