//
// Created by x<PERSON><PERSON><PERSON> on 7/5/24.
//
#include "TrafficControlServer.hpp"
#include <Python.h>
#include <json.hpp>
#include <fstream>
#include <iostream>


// 使用简化的 json 命名空间
using json = nlohmann::json;

// 将 nlohmann::json 转换为 Python 字典
PyObject* json_to_pydict(const json& j) {
    PyObject* py_dict = PyDict_New();
    for (auto& [key, value] : j.items()) {
        PyObject* py_key = PyUnicode_FromString(key.c_str());
        PyObject* py_value = nullptr;

        if (value.is_string()) {
            py_value = PyUnicode_FromString(value.get<std::string>().c_str());
        } else if (value.is_number_integer()) {
            py_value = PyLong_FromLong(value.get<int>());
        } else if (value.is_number_float()) {
            py_value = PyFloat_FromDouble(value.get<double>());
        } else if (value.is_boolean()) {
            py_value = PyBool_FromLong(value.get<bool>());
        } else if (value.is_object()) {
            py_value = json_to_pydict(value);  // 递归转换嵌套对象
        } else if (value.is_array()) {
            PyObject* py_list = PyList_New(value.size());
            for (size_t i = 0; i < value.size(); ++i) {
                PyObject* item = nullptr;
                if (value[i].is_string()) {
                    item = PyUnicode_FromString(value[i].get<std::string>().c_str());
                } else if (value[i].is_number_integer()) {
                    item = PyLong_FromLong(value[i].get<int>());
                } else if (value[i].is_number_float()) {
                    item = PyFloat_FromDouble(value[i].get<double>());
                } else if (value[i].is_boolean()) {
                    item = PyBool_FromLong(value[i].get<bool>());
                }
                PyList_SetItem(py_list, i, item);  // 设置列表项
            }
            py_value = py_list;
        }

        if (py_value) {
            PyDict_SetItem(py_dict, py_key, py_value);  // 将 key-value 对插入 Python 字典
            Py_DECREF(py_value);  // 减少引用计数
        }
        Py_DECREF(py_key);  // 减少引用计数
    }
    return py_dict;
}
// 函数用于打印 Python 对象
void print_pyobject(PyObject* obj) {
    if (PyUnicode_Check(obj)) {
        // 如果是字符串
        const char* str = PyUnicode_AsUTF8(obj);
        std::cout << "返回值: " << str << std::endl;
    } else if (PyLong_Check(obj)) {
        // 如果是整数
        long val = PyLong_AsLong(obj);
        std::cout << "返回值: " << val << std::endl;
    } else if (PyFloat_Check(obj)) {
        // 如果是浮点数
        double val = PyFloat_AsDouble(obj);
        std::cout << "返回值: " << val << std::endl;
    } else if (PyBool_Check(obj)) {
        // 如果是布尔值
        bool val = (obj == Py_True);
        std::cout << "返回值: " << (val ? "True" : "False") << std::endl;
    } else if (PyDict_Check(obj)) {
        // 如果是字典
        std::cout << "返回值是一个字典" << std::endl;
        // 可选择遍历字典打印内容
        // 打印字典的 JSON 表示
        PyObject* json_module = PyImport_ImportModule("json");
        PyObject* json_dumps = PyObject_GetAttrString(json_module, "dumps");

        if (json_dumps && PyCallable_Check(json_dumps)) {
            PyObject* json_str = PyObject_CallFunctionObjArgs(json_dumps, obj, NULL);
            if (json_str) {
                const char* json_output = PyUnicode_AsUTF8(json_str);
                std::cout << "返回值是一个字典 (JSON 表示): " << json_output << std::endl;
                Py_DECREF(json_str);
            } else {
                PyErr_Print();  // 打印错误信息
            }
        }
        Py_DECREF(json_dumps);
        Py_DECREF(json_module);
    } else if (PyList_Check(obj)) {
        // 如果是列表
        std::cout << "返回值是一个列表" << std::endl;
        // 可选择遍历列表打印内容
        for (Py_ssize_t i = 0; i < PyList_Size(obj); ++i) {
            PyObject* item = PyList_GetItem(obj, i);  // 获取列表项
            std::cout << "  列表项 " << i << ": ";
            print_pyobject(item);
        }
    } else {
        std::cout << "未知的返回类型" << std::endl;
    }
}
int main() {
    Py_Initialize();
    PyObject *pModule = PyImport_ImportModule("advanced_control");
    if (pModule == NULL) {
        PyErr_Print();
        fprintf(stderr, "Error loading module advanced_control\n");
        return -1;
    }

    if (pModule != nullptr) {
        // 从模块中获取类
        PyObject *pClass = PyObject_GetAttrString(pModule, "AdvancedControl");

        if (pClass && PyCallable_Check(pClass)) {
            // 实例化类，传入构造函数参数
            // 准备构造函数的参数
            PyObject *pConfigPath = PyUnicode_FromString("/home/<USER>/stn/alg/XAL_XFL_net.json");  // config_path 参数
            //PyObject *pSensorCnf = PyDict_New();  // 空的 sensor_cnf 字典
            //PyObject *pLogger = PySys_GetObject("stdout");  // logger 默认为 stdout

            PyObject *pArgs = PyTuple_Pack(1, pConfigPath);  // 传递一个整数参数
            PyObject *pInstance = PyObject_CallObject(pClass, pArgs);
            // 释放参数对象
            Py_DECREF(pConfigPath);
            //Py_DECREF(pSensorCnf);
            //Py_DECREF(pLogger);
            Py_DECREF(pArgs);

            if (pInstance != nullptr) {

                // 读取 origin_state JSON 文件
                std::ifstream file("/home/<USER>/stn/alg/origin_state.json");
                if (!file) {
                    std::cerr << "无法打开 JSON 文件" << std::endl;
                    return 1;
                }

                json j;
                file >> j;  // 解析 JSON 文件

                // 将 nlohmann::json 转换为 Python 字典
                PyObject* pStateDict = json_to_pydict(j);

                // 读取envState json文件
                std::ifstream envfile("/home/<USER>/stn/alg/envstates.json");
                if (!envfile) {
                    std::cerr << "无法打开 ENV JSON 文件" << std::endl;
                    return 1;
                }

                json envj;
                envfile >> envj;  // 解析 JSON 文件

                // 将 nlohmann::json 转换为 Python 字典
                PyObject* pEnvStateDict = json_to_pydict(envj);

                // 读取envState json文件
                std::ifstream statfile("/home/<USER>/stn/alg/states.json");
                if (!statfile) {
                    std::cerr << "无法打开 states JSON 文件" << std::endl;
                    return 1;
                }

                json statj;
                statfile >> statj;  // 解析 JSON 文件

                // 将 nlohmann::json 转换为 Python 字典
                PyObject* pStatStateDict = json_to_pydict(statj);


                // 调用实例的方法
                PyObject *pResult = PyObject_CallMethod(pInstance, "convert_cur_state", "(O)", pStateDict);
                // 释放字典
                Py_DECREF(pStateDict);

                if (pResult != nullptr) {
                    print_pyobject(pResult);
                    // 调用 take_action 方法，传递返回值和 env_state
                    PyObject *pActionResult = PyObject_CallMethod(pInstance, "take_action", "(OO)", pStatStateDict, pEnvStateDict);
                    if (pActionResult != nullptr) {
                        print_pyobject(pActionResult);
                        Py_DECREF(pActionResult);
                    } else {
                        PyErr_Print();  // 打印错误信息
                    }
                    Py_DECREF(pResult);
                } else {
                    PyErr_Print();  // 打印错误
                }

                Py_DECREF(pInstance);
            } else {
                PyErr_Print();  // 打印错误
            }

            Py_DECREF(pClass);
        } else {
            PyErr_Print();  // 打印错误
        }

        Py_DECREF(pModule);
    } else {
        PyErr_Print();  // 打印错误
    }


    // 释放对象
    Py_DECREF(pModule);



    Py_Finalize();




#if 0
    TrafficControlServer trafficControlServer;
    trafficControlServer.start();
    std::cout << "Server started successfully!" << std::endl;
#endif
    return 0;

}