//
// Created by x<PERSON><PERSON><PERSON> on 7/5/24.
//

#include "TrafficControlServer.hpp"
#include <utility>


using boost::asio::ip::tcp;

TrafficControlServer::TrafficControlServer(std::string address, int port): address_(std::move(address)), port_(port),
is_running_(false), acceptor_(io_context_, tcp::endpoint(boost::asio::ip::make_address(address_), port_)){
    std::cout<< "test" << std::endl;
}

void TrafficControlServer::start() {
    is_running_ = true;
    std::cout << "Server started at " << address_ << ":" << port_ << std::endl;
    acceptConnection();
    io_context_.run();
}

void TrafficControlServer::stop() {
    is_running_ = false;
    io_context_.stop();
    std::cout << "Server stopped." << std::endl;
}

void TrafficControlServer::acceptConnection() {
    auto socket = std::make_shared<tcp::socket>(io_context_);
    acceptor_.async_accept(*socket, [this, socket](boost::system::error_code ec) {
        if (!ec) {
            std::thread([this, socket]() {
                boost::asio::streambuf buffer;
                try {
                    boost::asio::read_until(*socket, buffer, "</Message>");
                    std::istream is(&buffer);
                    std::ostringstream oss;
                    oss << is.rdbuf();
                    std::string request = oss.str();

                    protocol_handler_.handleRequest(request);
                    std::string response = protocol_handler_.generateResponse();

                    boost::asio::write(*socket, boost::asio::buffer(response + "\n"));
                } catch (const boost::system::system_error& e) {
                    std::cerr << "Error: " << e.what() << std::endl;
                }
            }).detach();
        }
        if (is_running_) {
            acceptConnection();
        }
    });
}

void TrafficControlServer::sendToClient(const std::string &message) {
    auto socket = std::make_shared<tcp::socket>(io_context_);
    boost::asio::write(*socket, boost::asio::buffer(message + "\n"));
}