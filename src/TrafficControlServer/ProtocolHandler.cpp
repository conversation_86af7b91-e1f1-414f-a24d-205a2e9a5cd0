//
// Created by xiaol<PERSON> on 7/5/24.
//

#include <thread>
#include "ProtocolHandler.hpp"

void ProtocolHandler::handleRequest(const std::string &request) {
    if (!request.empty()) {
        std::cout << "Handling request: " << request << std::endl;
    }
    //模拟处理请求
    std::this_thread::sleep_for(std::chrono::seconds(1));
    std::cout << "Request handled successfully." << std::endl;
}

std::string ProtocolHandler::generateResponse() {
    return {};
}
