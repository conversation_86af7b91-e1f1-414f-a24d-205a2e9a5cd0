//
// Created by <PERSON><PERSON><PERSON><PERSON> on 7/10/24.
//

#ifndef SMARTTRAFFICNEXUS_VIDEOPOSTPROCESS_HPP
#define SMARTTRAFFICNEXUS_VIDEOPOSTPROCESS_HPP
#include <cstdint>
#include <vector>
#include <opencv2/core/core.hpp>
#include <opencv2/imgproc.hpp>



#define OBJ_NAME_MAX_SIZE 16
#define OBJ_NUMB_MAX_SIZE 64
#define OBJ_CLASS_NUM     80
#define NMS_THRESH        0.45
#define BOX_THRESH        0.25
#define PROP_BOX_SIZE     (5 + OBJ_CLASS_NUM)

typedef struct _BOX_RECT {
    int left;
    int right;
    int top;
    int bottom;
} BOX_RECT;

typedef struct detect_result_t {
    char name[OBJ_NAME_MAX_SIZE];
    BOX_RECT box;
    float prop;
    int count;
} detect_result_t;

typedef struct detect_result_group_t {
    int id;
    int count;
    detect_result_t results[OBJ_NUMB_MAX_SIZE];
} detect_result_group_t;

int post_process(int8_t* input0, int8_t* input1, int8_t* input2, int model_in_h, int model_in_w,
                 float conf_threshold, float nms_threshold, float scale_w, float scale_h,
                 std::vector<int32_t>& qnt_zps, std::vector<float>& qnt_scales,
                 detect_result_group_t* group);

void deinitPostProcess();

void mapPoint1080pTo640(uint32_t x_original, uint32_t y_original, uint32_t &x_new, uint32_t &y_new);
float IoU(const BOX_RECT& box1, const BOX_RECT& box2);
float center_distance(const BOX_RECT& box1, const BOX_RECT& box2);

bool is_point_in_polygon(const cv::Point& point, const std::vector<cv::Point>& polygon);
bool is_detection_in_roi(const cv::Point& detection_center, const std::vector<cv::Point>& roi);
cv::Point get_center_point_and_scale(const BOX_RECT& box, float width_scale, float height_scale);

void draw_roi_and_detections(cv::Mat &image, const std::vector<cv::Point> &roi, const BOX_RECT& detection,
                             float width_scale, float height_scale);
std::string detections2json(const std::vector<detect_result_t> &detections, const std::string& camIdx);


double get_timestamp();
#endif //SMARTTRAFFICNEXUS_VIDEOPOSTPROCESS_HPP
