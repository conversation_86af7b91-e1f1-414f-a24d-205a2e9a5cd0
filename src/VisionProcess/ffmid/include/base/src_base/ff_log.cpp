//
// Created by <PERSON>gh on 2025/4/11.
//
#include "base/ff_log.h"
#include <stdarg.h>

unsigned int ff_log_level = 2; // Default to INFO level

void ff_log_init() {
    // Initialize logging if needed
    char* env_level = getenv("ff_log_level");
    if (env_level) {
        ff_log_level = atoi(env_level);
    }
}

void _ff_log(const char* prefix, const char* tag, const char* fname, const char* fmt, ...) {
    va_list args;
    va_start(args, fmt);

    if (prefix && tag && fname) {
        fprintf(stderr, "[%s][%s][%s] ", prefix, tag, fname);
    }

    vfprintf(stderr, fmt, args);
    fprintf(stderr, "\n");

    va_end(args);
}