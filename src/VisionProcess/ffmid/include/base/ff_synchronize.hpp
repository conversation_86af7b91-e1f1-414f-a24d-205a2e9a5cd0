#ifndef __FF_SYNCHRONIZE_HPP__
#define __FF_SYNCHRONIZE_HPP__

#include <sys/time.h>
#include <inttypes.h>
#include <stdlib.h>

#include "ff_type.hpp"

class Synchronize
{
public:
    struct Clock {
        int64_t start_time;
        int64_t current_pts;
    };

private:
    Clock audio;
    Clock video;
    Clock absolute;
    float ptsRatio;
    SynchronizeType type;

    int video_last_duration;
    int audio_last_duration;

    int min_refrsh_s;  // Video minimum delay interval，default 10 000us.
    int max_refrsh_s;  // Video maximum delay interval，default 100 000us

    /*
        For playing network video streams, caching more than 2
        frames before playing will have better smoothness.
    */
    int first_frame_duration;  // default 0us
public:
    explicit Synchronize(SynchronizeType _type)
    {
        type =_type;
    }
    int64_t getCurrentTime()
    {
        struct timeval tv;
        gettimeofday(&tv,nullptr);
        return tv.tv_sec * 1000000ULL + tv.tv_usec;
    }
    void reset()
    {
        audio.start_time = 0;
        audio.current_pts = 0;
        video.start_time = 0;
        video.current_pts = 0;
        absolute.start_time = 0;
        absolute.current_pts = 0;
        ptsRatio = 0;
        type = SYNCHRONIZETYPE_ABSOLUTE;
        video_last_duration = 0;
        audio_last_duration = 0;
        min_refrsh_s = 10000;
        max_refrsh_s = 100000;
        first_frame_duration = 0;
    }
    void setPtsRatio(float ratio)
    {
        ptsRatio = ratio;
    }
    Clock& getMasterClock()
    {

        if(type == SYNCHRONIZETYPE_VIDEO)
        {
            return  video;
        }
        else if(type == SYNCHRONIZETYPE_AUDIO)
        {
            return  audio;
        }
        else
        {
            return absolute;
        }
    }
    int64_t getMasterTime()
    {
        if(type == SYNCHRONIZETYPE_VIDEO)
        {
            return  video.current_pts;
        }
        else if(type == SYNCHRONIZETYPE_AUDIO)
        {
            return  audio.current_pts;
        }
        else
        {
            return absolute.current_pts;
        }
    }
    void setClockTime(SynchronizeType _type, int64_t pts)
    {
        if(type == SYNCHRONIZETYPE_VIDEO)
        {
             video.current_pts = pts;
             video.start_time = getCurrentTime();
        }
        else if(type == SYNCHRONIZETYPE_AUDIO)
        {
             audio.current_pts = pts;
             audio.start_time = getCurrentTime();
        }
        else
        {
             absolute.current_pts = pts;
             absolute.start_time = getCurrentTime();
        }
    }
    int64_t getClockTime(const Clock& clock)
    {
        return clock.current_pts;
    }
    void setRefrshS(int maxUs, int minUs)
    {
        min_refrsh_s = minUs;
        max_refrsh_s = maxUs;
    }
    void setFirstFrameDuration(int durationUs)
    {
        first_frame_duration = durationUs;
    }
    int updateVideo(int64_t pts, int64_t duration)
    {
        video.current_pts = pts;
        video_last_duration = duration;
    }
    int updateAudio(int samples, int samplerate, int64_t pts)
    {
        audio.current_pts = pts;
        audio_last_duration = samples / samplerate;
    }
    int updateAudioByBytesSize(unsigned bytesSize, int samplerate, int channels,
                               int bitsPerSample, int64_t pts)
    {
        audio.current_pts = pts;
        audio_last_duration = (bytesSize /(bitsPerSample * channels)) / samplerate;
    }
};


#endif
