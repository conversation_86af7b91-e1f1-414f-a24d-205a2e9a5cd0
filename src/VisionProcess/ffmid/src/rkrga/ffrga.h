#ifndef FFRGA_H
#define FFRGA_H
#include <stdio.h>

#include "RgaUtils.h"
#include "im2d.hpp"
#include "im2d_type.h"



class FFRga
{
public:
    enum Rga_Process_Type{
        CVT_COLOR = 0,
        ROTATE,
        FLIP,
        BLEND
    };


    FFRga();

    ~FFRga();

    void SetSrcRgaBuffer(int width,int height,int fmt,uint8_t * srcData);

    void SetDstRgaBuffer(int width,int height,int fmt,uint8_t *dstData);

    void SetRgaProcessType(Rga_Process_Type type,int  paramVal);

    bool Init();

    void DeInit();

    bool ProcessRga();

    uint8_t *GetSrcData();
    uint8_t *GetDstData();

    uint64_t GetSrcDataSize();
    uint64_t GetDstDataSize();

private:

    Rga_Process_Type type_;
    rga_buffer_t src_img;
    rga_buffer_t dst_img;
    rga_buffer_handle_t src_handle;
    rga_buffer_handle_t dst_handle;

    int srcWidth,srcHeight,dstWidth,dstHeight,srcFmt,dstFmt;
    uint8_t *srcData_;
    uint8_t *dstData_;

    uint64_t srcDataSize = 0;
    uint64_t dstDataSize = 0;
    int paramVal_;
};

#endif // FFRGA_H
