#include <string.h>
#include "ffrga.h"
#include <stdlib.h>

FFRga::FFRga()
{
    srcWidth = 0;
    srcHeight = 0;
    dstWidth = 0;
    dstHeight = 0;
    srcFmt = -1;
    dstFmt = -1;
    srcData_ = nullptr;
    dstData_ = nullptr;
    paramVal_ = -1;
}

FFRga::~FFRga()
{

}

void FFRga::SetSrcRgaBuffer(int width, int height, int fmt, uint8_t *srcData)
{
    srcWidth = width;
    srcHeight = height;
    srcFmt = fmt;
    srcData_ = srcData;
    int src_buf_size = srcWidth * srcHeight * get_bpp_from_format(srcFmt);
    if(!srcData_)
    {

        srcData_ = (uint8_t *)malloc(src_buf_size);
    }

    srcDataSize = src_buf_size;
}

void FFRga::SetDstRgaBuffer(int width, int height, int fmt, uint8_t *dstData)
{
    dstWidth = width;
    dstHeight = height;
    dstFmt =fmt;
    dstData_ = dstData;
    int dst_buf_size = dstWidth * dstHeight * get_bpp_from_format(dstFmt);
    if(!dstData_)
    {

        dstData_ = (uint8_t *)malloc(dst_buf_size);
    }
    dstDataSize = dst_buf_size;
}

void FFRga::SetRgaProcessType(Rga_Process_Type type,int  paramVal)
{
    type_ = type;
    paramVal_ = paramVal;
}

bool FFRga::Init()
{
    memset(&src_img, 0, sizeof(src_img));
    memset(&dst_img, 0, sizeof(dst_img));

    int src_buf_size = srcWidth * srcHeight * get_bpp_from_format(srcFmt);
    int dst_buf_size = dstWidth * dstHeight * get_bpp_from_format(dstFmt);

    src_handle = importbuffer_virtualaddr(srcData_, src_buf_size);
    dst_handle = importbuffer_virtualaddr(dstData_, dst_buf_size);
    if (src_handle == 0 || dst_handle == 0) {

        if (src_handle)
            releasebuffer_handle(src_handle);
        if (dst_handle)
            releasebuffer_handle(dst_handle);
        return false;
    }
    src_img = wrapbuffer_handle(src_handle, srcWidth, srcHeight, srcFmt);
    dst_img = wrapbuffer_handle(dst_handle, dstWidth, dstHeight, dstFmt);

    int ret = imcheck(src_img, dst_img, {}, {});
    if (IM_STATUS_NOERROR != ret) {
       // printf("%d, check error! %s", __LINE__, imStrError((IM_STATUS)ret));
        if (src_handle)
            releasebuffer_handle(src_handle);
        if (dst_handle)
            releasebuffer_handle(dst_handle);
        return false;
    }
    return true;
}

void FFRga::DeInit()
{
    if (src_handle)
        releasebuffer_handle(src_handle);
    if (dst_handle)
        releasebuffer_handle(dst_handle);

    memset(&src_img, 0, sizeof(src_img));
    memset(&dst_img, 0, sizeof(dst_img));
    if(srcData_)
    {
        free(srcData_);
        srcData_ = nullptr;
    }
    if(dstData_)
    {
        free(dstData_);
        dstData_ = nullptr;
    }
}

bool FFRga::ProcessRga()
{
    if(type_ == CVT_COLOR)
    {
        int ret = imcvtcolor(src_img, dst_img, srcFmt, dstFmt);
        if (ret != IM_STATUS_SUCCESS) {
            DeInit();
            return false;
        }

    }
    else if(type_ == ROTATE)
    {

        int ret = imrotate(src_img, dst_img,paramVal_);
        if (ret != IM_STATUS_SUCCESS) {
            DeInit();
            return false;
        }
    }
    else if(type_ == FLIP)
    {
        int ret = imflip(src_img, dst_img,paramVal_);
        if (ret != IM_STATUS_SUCCESS) {
            DeInit();
            return false;
        }

    }
    else if(type_ == BLEND)
    {
        int ret = imblend(src_img, dst_img,paramVal_);
        if (ret != IM_STATUS_SUCCESS) {
            DeInit();
            return false;
        }
    }
    return true;
}

uint8_t *FFRga::GetSrcData()
{
    return srcData_;
}

uint8_t *FFRga::GetDstData()
{
    return dstData_;
}

uint64_t FFRga::GetSrcDataSize()
{
    return srcDataSize;
}

uint64_t FFRga::GetDstDataSize()
{
    return dstDataSize;
}
