#ifndef RTSPCLIENT_H
#define RTSPCLIENT_H
#include <iostream>
#include "base/ff_type.hpp"
#include <thread>
#include <atomic>
#include "Cond.h"
#include <list>
#include "module/module_media.hpp"
extern "C"
{
    #include <libavcodec/avcodec.h>
    #include <libavformat/avformat.h>
    #include <libavutil/time.h>
    #include <libavutil/pixfmt.h>
    #include <libavutil/display.h>
    #include <libavutil/avstring.h>
    #include <libavutil/opt.h>
    #include <libswscale/swscale.h>
    #include <libswresample/swresample.h>
}
#define ERROR_STRING_SIZE 1024

using namespace std;

class RTSPClient
{
public:
    enum RTSPPLAYER_STATUS {
        RTSPPLAYER_STATUS_CLOSED,
        RTSPPLAYER_STATUS_OPENED,
        RTSPPLAYER_STATUS_PLAYING,
        RTSPPLAYER_STATUS_PAUSE,
    };

    RTSPClient(const char* url,RTSP_STREAM_TYPE _stream_type,bool enable_video,bool enable_audio);

    ~RTSPClient();

    bool Init();

    bool Start();

    void Stop();

    void SetTimeout(int val);

    int GetAudioChannel();

    int GetAudioSampleRate();

    int GetAudioFormat();

    int GetAudioNbSample();

    int GetVideoFPS();

    int GetVideoWidth();

    int GetVideoHeight();

    AVCodecID GetCodecType();

    RTSPPLAYER_STATUS GetRtspPlayerStatus();

    int GetOnePkt(shared_ptr<MediaBuffer> buffer);

    int GetVideoStreamIdx();

    int GetAudioStreamIdx();

protected:
    int InitDemux(const char* inputUrl);


    void CloseDemux();

    void StartDemuxFunc();

    bool inputVideoQuene(AVPacket &pkt);

    void clearVideoQuene();

    bool inputAudioQuene(const AVPacket &pkt);

    void clearAudioQuene();

    bool inputDataQueue(AVPacket &pkt);

    void clearDataQueue();



private:
    char m_Url[100];
    RTSP_STREAM_TYPE m_RtspStreamType;
    bool m_EnableVideo;
    bool m_EnableAudio;

//    double audio_clock; ///音频时钟
//    double video_clock; ///<pts of last decoded frame / predicted pts of next decoded frame
    AVStream *mVideoStream; //视频流
    AVStream *mAudioStream; //视频流

    AVFormatContext *pFormatCtx;

    AVCodec* aCodec = nullptr;
    AVCodecContext *aCodecCtx = nullptr;
    AVCodecParserContext* aParser = nullptr;

    AVBSFContext *bsf_ctx = nullptr;
    AVPacket *m_Pkt = nullptr;
    int m_TimeOut;

//    Cond *mConditon_Video;
//    list<AVPacket> mVideoPacktList;


//    Cond *mConditon_Audio;
//    list<AVPacket> mAudioPacktList;

    Cond *mConditon_Datas;
    list<AVPacket> mPacktList;


    int videoStream = -1;
    int audioStream = -1;

    atomic<bool> m_IsDemuxThrdRunning;
    atomic<bool> m_IsQuit;
    thread *m_DemuxThread;

    bool m_IsInit;
    RTSPPLAYER_STATUS m_RtsplayerStatus;
};

#endif // RTSPCLIENT_H
