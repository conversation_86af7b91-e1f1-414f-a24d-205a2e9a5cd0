#include "rtspclient.hpp"


RTSPClient::RTSPClient(const char *url, RTSP_STREAM_TYPE _stream_type, bool enable_video, bool enable_audio)
{

    memset(m_Url,0,100);
    memcpy(m_Url,url,strlen(url));
    m_RtspStreamType = _stream_type;
    m_EnableVideo = enable_video;
    m_EnableAudio = enable_audio;
    m_TimeOut = 3000000;
    m_DemuxThread = nullptr;
    m_IsDemuxThrdRunning = false;
    m_IsQuit = false;
    mVideoStream = nullptr;
    mAudioStream = nullptr;
    m_RtsplayerStatus = RTSPPLAYER_STATUS_CLOSED;
    mConditon_Datas = new Cond;
    m_IsInit = false;
}

RTSPClient::~RTSPClient()
{
    Stop();
    if(mConditon_Datas)
    {
        delete mConditon_Datas;
        mConditon_Datas = nullptr;
    }
}

bool RTSPClient::Init()
{


    if (!m_IsInit)
    {
        av_register_all(); //初始化FFMPEG  调用了这个才能正常使用编码器和解码器
        avformat_network_init(); //支持打开网络文件

        m_IsInit = true;
    }
    int ret = InitDemux(m_Url);
    if(ret == -1)
    {
        printf("Init demux error!\n");
        return false;
    }
    return true;
}

bool RTSPClient::Start()
{
    if(m_DemuxThread)
    {
        if(m_DemuxThread->joinable())
        {
            m_DemuxThread->join();
        }
        delete m_DemuxThread;
        m_DemuxThread = nullptr;
    }
    m_IsDemuxThrdRunning =true;
    m_DemuxThread = new thread(&RTSPClient::StartDemuxFunc,this);
    return true;
}

void RTSPClient::Stop()
{
    m_IsQuit =  true;
    m_IsDemuxThrdRunning = false;
    if(m_DemuxThread)
    {
        if(m_DemuxThread->joinable())
        {
            m_DemuxThread->join();
        }
        delete m_DemuxThread;
        m_DemuxThread = nullptr;
    }
    CloseDemux();
    clearVideoQuene();
    clearAudioQuene();
    m_RtsplayerStatus = RTSPPLAYER_STATUS_CLOSED;

}

void RTSPClient::SetTimeout(int val)
{
    m_TimeOut = val;
}

int RTSPClient::GetAudioChannel()
{
    if(mAudioStream)
    {
        AVCodecParameters *codecpar_ = mAudioStream->codecpar;
        if(codecpar_)
        {
            return   codecpar_->channels;
        }

    }
    return 0;
}

int RTSPClient::GetAudioSampleRate()
{
    if(mAudioStream)
    {
        AVCodecParameters *codecpar_ = mAudioStream->codecpar;
        if(codecpar_)
        {
            return  codecpar_->sample_rate;
        }

    }
    return 0;
}

int RTSPClient::GetAudioFormat()
{
    if(mAudioStream)
    {
        AVCodecParameters *codecpar_ = mAudioStream->codecpar;
        if(codecpar_)
        {
            return  codecpar_->format;
        }

    }
    return 0;
}

int RTSPClient::GetAudioNbSample()
{
    if(mAudioStream)
    {
        AVCodecParameters *codecpar_ = mAudioStream->codecpar;
        if(codecpar_)
        {
            return  codecpar_->sample_rate;
        }

    }
    return 0;
}

int RTSPClient::GetVideoFPS()
{
    if(mVideoStream)
    {
        return   mVideoStream->avg_frame_rate.num;
    }
    return 0;
}

int RTSPClient::GetVideoWidth()
{
    if(mVideoStream)
    {
       return mVideoStream->codecpar->width;
    }
    return 0;
}

int RTSPClient::GetVideoHeight()
{
    if(mVideoStream)
    {
       return mVideoStream->codecpar->height;
    }
    return 0;

}

AVCodecID RTSPClient::GetCodecType()
{
    if(mVideoStream)
    {
       return mVideoStream->codecpar->codec_id;
    }
    return AV_CODEC_ID_NONE;
}

RTSPClient::RTSPPLAYER_STATUS RTSPClient::GetRtspPlayerStatus()
{
    return m_RtsplayerStatus;
}

int RTSPClient::InitDemux(const char *inputUrl)
{
    int video_idx = -1;
    int audio_idx = -1;
    int ret = 0;
    char errors[ERROR_STRING_SIZE+1];  // 主要是用来缓存解析FFmpeg api返回值的错误string
    pFormatCtx = avformat_alloc_context();
    if(!pFormatCtx) {
        printf("avformat_alloc_context failed\n");
        //        fclose(aac_fd);
        return -1;
    }

    string streamType ="";
    if(m_RtspStreamType == RTSP_STREAM_TYPE_UDP)
    {
        streamType = "udp";
    }
    else if(m_RtspStreamType == RTSP_STREAM_TYPE_TCP)
    {
        streamType = "tcp";
    }

    AVDictionary* opts = NULL;
    av_dict_set(&opts, "rtsp_transport", streamType.c_str(), 0);
    string timeout_str = to_string(m_TimeOut);
    av_dict_set(&opts, "stimeout", timeout_str.c_str(), 0);


    ret = avformat_open_input(&pFormatCtx, inputUrl, NULL, &opts);
    if(ret < 0) {
        av_strerror(ret, errors, ERROR_STRING_SIZE);
        printf("avformat_open_input failed:%d\n", ret);
        printf("avformat_open_input failed:%s\n", errors);
        avformat_close_input(&pFormatCtx);
        return -1;
    }
    if (avformat_find_stream_info(pFormatCtx, nullptr)<0)
    {
        printf("Couldn't find stream information.\n");
        return -1;
    }
    if(m_EnableVideo)
    {
        video_idx = av_find_best_stream(pFormatCtx, AVMEDIA_TYPE_VIDEO, -1, -1, NULL, 0);
        if(video_idx == -1) {
            printf("av_find_best_stream video_index failed\n");
            avformat_close_input(&pFormatCtx);
            return -1;
        }

        const AVBitStreamFilter *bsfilter = nullptr;
        if(pFormatCtx->streams[video_idx]->codecpar->codec_id == AV_CODEC_ID_H264)
        {
        // h264_mp4toannexb
            bsfilter = av_bsf_get_by_name("h264_mp4toannexb");      // 对应面向对象的方法
            if(!bsfilter) {
                avformat_close_input(&pFormatCtx);
                printf("av_bsf_get_by_name h264_mp4toannexb failed\n");
                return -1;
            }
        }
        else if(pFormatCtx->streams[video_idx]->codecpar->codec_id == AV_CODEC_ID_HEVC)
        {
            bsfilter = av_bsf_get_by_name("hevc_mp4toannexb");      // 对应面向对象的方法
            if(!bsfilter) {
                avformat_close_input(&pFormatCtx);
                printf("av_bsf_get_by_name h264_mp4toannexb failed\n");
                return -1;
            }
        }

        bsf_ctx = NULL;        // 对应面向对象的变量
        ret = av_bsf_alloc(bsfilter, &bsf_ctx);
        if(ret < 0) {
            av_strerror(ret, errors, ERROR_STRING_SIZE);
            printf("av_bsf_alloc failed:%s\n", errors);
            avformat_close_input(&pFormatCtx);
            return -1;
        }
        //AVCodecContext *codecCtx = pFormatCtx->streams[video_idx]->codec;
        ret = avcodec_parameters_copy(bsf_ctx->par_in, pFormatCtx->streams[video_idx]->codecpar);
        if(ret < 0) {
            av_strerror(ret, errors, ERROR_STRING_SIZE);
            printf("avcodec_parameters_copy failed:%s\n", errors);
            avformat_close_input(&pFormatCtx);
            av_bsf_free(&bsf_ctx);
            return -1;
        }
        ret = av_bsf_init(bsf_ctx);
        if(ret < 0) {
            av_strerror(ret, errors, ERROR_STRING_SIZE);
            printf("av_bsf_init failed:%s\n", errors);
            avformat_close_input(&pFormatCtx);
            av_bsf_free(&bsf_ctx);
            return -1;
        }
        mVideoStream = pFormatCtx->streams[video_idx];
    }

   if(m_EnableAudio)
   {
       audio_idx = av_find_best_stream(pFormatCtx, AVMEDIA_TYPE_AUDIO, -1, -1, NULL, 0);
       if(audio_idx == -1) {
            printf("av_find_best_stream audio_index failed\n");
       }

       if(audio_idx >= 0)
       {

            enum AVCodecID audio_codec_id = pFormatCtx->streams[audio_idx]->codecpar->codec_id;
            aCodec = avcodec_find_decoder(audio_codec_id);  // AV_CODEC_ID_AAC
            if (!aCodec) {
                fprintf(stderr, "Codec not found\n");
                return -1;
            }
            aParser = av_parser_init(audio_codec_id);
            if (!aParser) {
                fprintf(stderr, "Parser not found\n");
                return -1;
            }
            aCodecCtx = avcodec_alloc_context3(aCodec);
            if (!aCodecCtx) {
                fprintf(stderr, "Could not allocate audio codec context\n");
                return -1;
            }
            if ((ret = avcodec_parameters_to_context(aCodecCtx, pFormatCtx->streams[audio_idx]->codecpar)) < 0) {
                // 将解码器和解码器上下文进行关联
                fprintf(stderr, "failed copy codec params\n");
                return -1;

            }
            if (avcodec_open2(aCodecCtx, aCodec, NULL) < 0) {
                fprintf(stderr, "Could not open codec\n");
                return -1;
            }
            mAudioStream = pFormatCtx->streams[audio_idx];
        }
    }
    m_Pkt = av_packet_alloc();
    videoStream = video_idx ;
    audioStream = audio_idx;
    m_RtsplayerStatus = RTSPPLAYER_STATUS_OPENED;
    return 0;
}

void RTSPClient::CloseDemux()
{

    if(m_Pkt)
    {
        av_packet_free(&m_Pkt);
    }
    if(bsf_ctx)
        av_bsf_free(&bsf_ctx);
    if(pFormatCtx)
        avformat_close_input(&pFormatCtx);


    if(aCodecCtx)
    {
        avcodec_free_context(&aCodecCtx);
    }
    if(aParser)
    {
       av_parser_close(aParser);
    }

}

void RTSPClient::StartDemuxFunc()
{

    while(m_IsDemuxThrdRunning)
    {
        int ret = av_read_frame(pFormatCtx, m_Pkt);     // 不会去释放pkt的buf，如果我们外部不去释放，就会出现内存泄露
        if(ret < 0 )
        {
            char errors[ERROR_STRING_SIZE+1];
            av_strerror(ret, errors, ERROR_STRING_SIZE);
            printf("av_read_frame failed:%s\n", errors);
            m_IsDemuxThrdRunning = false;
            m_IsQuit = true;
            m_RtsplayerStatus = RTSPPLAYER_STATUS_CLOSED;
            break;
        }
        m_RtsplayerStatus = RTSPPLAYER_STATUS_PLAYING;
        if (m_Pkt->stream_index == videoStream && mVideoStream)
        {

            ret = av_bsf_send_packet(bsf_ctx, m_Pkt); // 内部把我们传入的buf转移到自己bsf内部
            if(ret < 0) {
                char errors[ERROR_STRING_SIZE+1];
                av_strerror(ret, errors, ERROR_STRING_SIZE);
                printf("av_bsf_send_packet failed:%s\n", errors);
                av_packet_unref(m_Pkt);
                continue;
            }
            while (1)
            {
                ret = av_bsf_receive_packet(bsf_ctx, m_Pkt);
                if(ret != 0) {
                    av_packet_unref(m_Pkt);
                    break;
                }

                if (m_IsQuit)
                {
                    av_packet_unref(m_Pkt);
                }
                else
                {
                    inputDataQueue(*m_Pkt);
                    if(mAudioStream == nullptr || audioStream == -1)
                    {

                          int val = (1000 / GetVideoFPS());
                          this_thread::sleep_for(chrono::milliseconds(val));
                    }
                }
               av_packet_unref(m_Pkt);
            }


        }
        else if(m_Pkt->stream_index == audioStream && mAudioStream)
        {

            if (m_IsQuit)
            {
                av_packet_unref(m_Pkt);
            }
            else
            {
                inputDataQueue(*m_Pkt);
            }

        }
        else
        {
            av_packet_unref(m_Pkt);
        }
        if(m_IsQuit)
        {
            m_IsDemuxThrdRunning = false;
        }
    }

}

bool RTSPClient::inputVideoQuene(AVPacket &pkt)
{
//    mConditon_Video->Lock();
//    mVideoPacktList.push_back(pkt);
//    mConditon_Video->Signal();
//    mConditon_Video->Unlock();

    return true;
}

void RTSPClient::clearVideoQuene()
{
//    mConditon_Video->Lock();
//    for (AVPacket pkt : mVideoPacktList)
//    {
//        av_packet_unref(&pkt);
//    }
//    mVideoPacktList.clear();
//    mConditon_Video->Unlock();
}

bool RTSPClient::inputAudioQuene(const AVPacket &pkt)
{
//    mConditon_Audio->Lock();
//    mAudioPacktList.push_back(pkt);
//    mConditon_Audio->Signal();
//    mConditon_Audio->Unlock();

    return true;
}

void RTSPClient::clearAudioQuene()
{
//    mConditon_Audio->Lock();
//    for (AVPacket pkt : mAudioPacktList)
//    {
//        av_packet_unref(&pkt);
//    }
//    mAudioPacktList.clear();
    //    mConditon_Audio->Unlock();
}

bool RTSPClient::inputDataQueue(AVPacket &pkt)
{
    mConditon_Datas->Lock();
    mPacktList.push_back(pkt);
    mConditon_Datas->Unlock();
    return true;
}

void RTSPClient::clearDataQueue()
{
    mConditon_Datas->Lock();
    for (AVPacket pkt : mPacktList)
    {
        av_packet_unref(&pkt);
    }
    mPacktList.clear();
    mConditon_Datas->Unlock();
}

int RTSPClient::GetOnePkt(shared_ptr<MediaBuffer> buffer)
{

    mConditon_Datas->Lock();
    if(mPacktList.size() == 0)
    {

        mConditon_Datas->Unlock();
        return -1;
    }
    AVPacket packet = mPacktList.front();
    mPacktList.pop_front();
    if(buffer->getData() == nullptr)
    {
      buffer->allocBuffer(packet.size);
    }
    memcpy(buffer->getData(),packet.data,packet.size);
    buffer->setPUstimestamp(packet.pts);
    buffer->setDUstimestamp(packet.dts);
    buffer->setIndex(packet.stream_index);
    //av_packet_unref(&packet);
    mConditon_Datas->Unlock();

    return 0;
}

int RTSPClient::GetVideoStreamIdx()
{
    return videoStream;

}

int RTSPClient::GetAudioStreamIdx()
{
    return audioStream;
}
