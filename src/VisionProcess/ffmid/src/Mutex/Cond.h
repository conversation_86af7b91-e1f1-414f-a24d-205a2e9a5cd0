#ifndef COND_H
#define COND_H



#ifndef _MSC_VER
#define MINGW
#endif

//#if defined(WIN32) && !defined(MINGW)
#if defined(WIN32) || defined (_WIN32) || defined (__WIN64)
    #include <WinSock2.h>
    #include <Windows.h>
#else
#if defined(__unix) || defined(unix) || defined(linux)
    #include <pthread.h>
    #include <time.h>
#endif
#endif

class Cond
{
public:
    Cond();
    ~Cond();

    //上锁
    int Lock();

    //解锁
    int Unlock();

    //
    int Wait();

    //固定时间等待
    int TimedWait(int second);

    //
    int Signal();

    //唤醒所有睡眠线程
    int Broadcast();

private:

//#if defined(WIN32) && !defined(MINGW)
#if defined(WIN32) || defined (_WIN32) || defined (__WIN64)
    CRITICAL_SECTION m_mutex;
    RTL_CONDITION_VARIABLE m_cond;
#else
#if defined(__unix) || defined(unix) || defined(linux)
    pthread_mutex_t m_mutex;
    pthread_cond_t m_cond;
#endif
#endif

};

#endif // MUTEX_H
