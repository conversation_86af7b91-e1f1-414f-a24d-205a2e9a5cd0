#include "mppdecoder.h"

MppDecoder::MppDecoder()
{
    m_ImgWidth = 0;
    m_ImgHeight = 0;
    m_VdecType = RK_VIDEO_ID_Unused;
}

MppDecoder::~MppDecoder()
{

}

bool MppDecoder::Init()
{
    memset(&m_VDecCtx,0,sizeof(PLAYER_INFO_S));
    RK_MPI_SYS_Init();

    int ret = RK_INIT_Decoder(&m_VDecCtx,m_ImgWidth,m_ImgHeight,m_VdecType);
    if(ret == RK_SUCCESS)
    {
        return true;
    }
    return false;
}

void MppDecoder::DeInit()
{
    RK_VDEC_Stop(&m_VDecCtx);

    RK_MPI_SYS_Exit();
}

void MppDecoder::SetImgWidth(int width)
{
    m_ImgWidth = width;
}

void MppDecoder::SetImgHeight(int height)
{
    m_ImgHeight = height;
}

void MppDecoder::SetVdecType(RK_CODEC_ID_E type)
{
    m_VdecType = type;
}

int MppDecoder::Vdec_SendPkt(uint8_t *pktData, int pktSize, uint64_t pts)
{
   return RK_VDEC_SendPkt(&m_VDecCtx,pktData,pktSize,pts);
}

int MppDecoder::Vdec_GetOneFrame(VIDEO_FRAME_INFO_S *bframe)
{
    return RK_VDEC_GetAFrame(&m_VDecCtx,bframe);
}

RK_U8 *MppDecoder::Vdec_MB_Handle2VirAddr(VIDEO_FRAME_INFO_S *bframe)
{
    RK_U8 * data = (RK_U8 *)RK_MPI_MB_Handle2VirAddr((*bframe).stVFrame.pMbBlk);
    RK_MPI_SYS_MmzFlushCache((*bframe).stVFrame.pMbBlk, RK_TRUE);
    return data;
}

void MppDecoder::Vdec_ReleaseFrame(VIDEO_FRAME_INFO_S *bframe)
{
    RK_MPI_VDEC_ReleaseFrame(m_VDecCtx.vdecChannel, bframe);
}
