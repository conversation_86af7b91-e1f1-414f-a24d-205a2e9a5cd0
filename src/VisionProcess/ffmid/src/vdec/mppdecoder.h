#ifndef MPPDECODER_H
#define MPPDECODER_H
#include "vdec/rk_vdec.h"

class MppDecoder
{
public:
    MppDecoder();

    ~MppDecoder();

    bool Init();

    void DeInit();

    void SetImgWidth(int width);

    void SetImgHeight(int height);

    void SetVdecType(RK_CODEC_ID_E type);

    int Vdec_SendPkt(uint8_t *pktData,int pktSize,uint64_t pts);

    int Vdec_GetOneFrame(VIDEO_FRAME_INFO_S *bframe);

    RK_U8* Vdec_MB_Handle2VirAddr(VIDEO_FRAME_INFO_S *bframe);

    void Vdec_ReleaseFrame(VIDEO_FRAME_INFO_S *bframe);
private:
    PLAYER_INFO_S m_VDecCtx;

    int m_ImgWidth;
    int m_ImgHeight;
    RK_CODEC_ID_E m_VdecType;

};

#endif // MPPDECODER_H
