#include "rk_vdec.h"


#define MAX_STREAM_CNT               8
#define MAX_TIME_OUT_MS              180

#define RK356X_VO_DEV_HD0               0 // signle screen only
#define RK356X_VOP_LAYER_CLUSTER_0      0

RK_S32 RK_VO_Open(PLAYER_INFO_S *ctx, RECT_S *pImgRect, RECT_S *pDispRect, RK_U32 frameRate, RK_S32 pixFmt);
RK_S32 RK_VO_Close(PLAYER_INFO_S *ctx);



bool bRunning = true;

void exitHandler(int dummy) {
    bRunning = false;
}

int RK_INIT_Decoder(void *ctx_,int width,int height,RK_CODEC_ID_E vdecType)
{
    if(ctx_ == nullptr)
        return -1;
   printf("start RK_INIT_Decoder\n");
    PLAYER_INFO_S *ctx = (PLAYER_INFO_S *)ctx_;


    // Video decoder settings
    ctx->enCodecId =vdecType;// RK_VIDEO_ID_AVC;
    ctx->vDecInputMode = VIDEO_MODE_STREAM;//VIDEO_MODE_STREAM;
    ctx->u32CompressMode = COMPRESS_MODE_NONE;//COMPRESS_AFBC_16x16; //COMPRESS_MODE_NONE
    ctx->u32FrameBufferCnt = 8;
    ctx->u32ReadSize = 1024;
    ctx->bEnableColmv = RK_TRUE;
    ctx->vdecChannel = 0;
    ctx->u32SrcWidth = width;
    ctx->u32SrcHeight = height;
    ctx->bEnableMbPool = RK_FALSE;
    ctx->s32OutputPixFmt = (RK_S32)RK_FMT_YUV420SP;
    ctx->u32ChnIndex = 0;

   if(ctx->enCodecId == RK_VIDEO_ID_MJPEG || ctx->enCodecId == RK_VIDEO_ID_JPEG)
   {
       ctx->u32ReadSize = ctx->u32SrcWidth  * ctx->u32SrcHeight;
       ctx->vDecInputMode = VIDEO_MODE_FRAME;
   }
   if( RK_VDEC_Start(ctx) != 0)
   {
      printf("RK_VDEC_Start failed!\n");
      return -1;
   }

    return RK_SUCCESS;
}

RK_S32 RK_VO_Open(PLAYER_INFO_S *ctx, RECT_S *pImgRect, RECT_S *pDispRect, RK_U32 frameRate, RK_S32 pixFmt)
{
    RK_S32 s32Ret = RK_SUCCESS;

    VO_PUB_ATTR_S voAttr;
    VO_CHN_ATTR_S voChannelAttr;
    VO_VIDEO_LAYER_ATTR_S layerAttr;
    MPP_CHN_S stSrcChn, stDestChn;

    RK_MPI_VO_DisableLayer(ctx->voLayer);
    RK_MPI_VO_Disable(ctx->voDev);

    memset(&voAttr, 0, sizeof(VO_PUB_ATTR_S));
    memset(&voChannelAttr, 0, sizeof(VO_CHN_ATTR_S));
    memset(&layerAttr, 0, sizeof(VO_VIDEO_LAYER_ATTR_S));

    //The source w/h /of the image
    layerAttr.stImageSize.u32Width = pImgRect->u32Width;
    layerAttr.stImageSize.u32Height = pImgRect->u32Height;

    //the display settings
    layerAttr.enPixFormat = (PIXEL_FORMAT_E)pixFmt;
    layerAttr.stDispRect.s32X = pDispRect->s32X;
    layerAttr.stDispRect.s32Y = pDispRect->s32Y;
    layerAttr.u32DispFrmRt = frameRate;
    layerAttr.stDispRect.u32Width = pDispRect->u32Width;
    layerAttr.stDispRect.u32Height = pDispRect->u32Height;

    s32Ret = RK_MPI_VO_GetPubAttr(ctx->voDev, &voAttr);
    if (s32Ret != RK_SUCCESS) {
        return s32Ret;
    }

    voAttr.enIntfType = ctx->enIntfType;
    voAttr.enIntfSync = ctx->enIntfSync;

    s32Ret = RK_MPI_VO_SetPubAttr(ctx->voDev, &voAttr);
    if (s32Ret != RK_SUCCESS) {
        return s32Ret;
    }

    s32Ret = RK_MPI_VO_Enable(ctx->voDev);
    if (s32Ret != RK_SUCCESS) {
        return s32Ret;
    }

    s32Ret = RK_MPI_VO_SetLayerAttr(ctx->voLayer, &layerAttr);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("RK_MPI_VO_SetLayerAttr failed,s32Ret:%d\n", s32Ret);
        return RK_FAILURE;
    }

    s32Ret = RK_MPI_VO_BindLayer(ctx->voLayer, ctx->voDev, ctx->voLayerMode);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("RK_MPI_VO_BindLayer failed,s32Ret:%d\n", s32Ret);
        return RK_FAILURE;
    }

    s32Ret = RK_MPI_VO_EnableLayer(ctx->voLayer);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("RK_MPI_VO_EnableLayer failed,s32Ret:%d\n", s32Ret);
        return RK_FAILURE;
    }

    //First VO channel only
    voChannelAttr.stRect.u32Width = layerAttr.stImageSize.u32Width / 1;
    voChannelAttr.stRect.u32Height = layerAttr.stImageSize.u32Height / 1;
    voChannelAttr.stRect.s32X = 0 * voChannelAttr.stRect.u32Width / 2;
    voChannelAttr.stRect.s32Y = 0 * voChannelAttr.stRect.u32Height / 2;
    voChannelAttr.u32Priority = 0;
    voChannelAttr.u32FgAlpha = 128;
    voChannelAttr.u32BgAlpha = 0;

    s32Ret = RK_MPI_VO_SetChnAttr(ctx->voLayer, 0, &voChannelAttr);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("set chn Attr failed,s32Ret:%d\n", s32Ret);
        return RK_FAILURE;
    }

    stSrcChn.enModId    = RK_ID_VDEC;
    stSrcChn.s32DevId   = 0;
    stSrcChn.s32ChnId   = 0;

    stDestChn.enModId   = RK_ID_VO;
    stDestChn.s32DevId  = 0;
    stDestChn.s32ChnId  = 0;

    s32Ret = RK_MPI_SYS_Bind(&stSrcChn, &stDestChn);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("vi band vo fail:%x", s32Ret);
        return -1;
    }

    // enable vo
    s32Ret = RK_MPI_VO_EnableChn(ctx->voLayer, 0);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("Enalbe vo chn failed, s32Ret = %d\n", s32Ret);
        return -1;
    }

    return s32Ret;
}

RK_S32 RK_VO_Bind(VDEC_CHN VdecChn, VO_DEV VoDev, VO_CHN VoChn) {
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    stSrcChn.enModId = RK_ID_VDEC;
    stSrcChn.s32DevId = 0;
    stSrcChn.s32ChnId = VdecChn;

    stDestChn.enModId = RK_ID_VO;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = VoChn;

    s32Ret = RK_MPI_SYS_Bind(&stSrcChn, &stDestChn);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("failed with %#x!", s32Ret);
        return RK_FAILURE;
    }

    return s32Ret;
}

RK_S32 RK_VO_Unbind(VDEC_CHN VdecChn, VO_DEV VoDev, VO_CHN VoChn)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    stSrcChn.enModId = RK_ID_VDEC;
    stSrcChn.s32DevId = 0;
    stSrcChn.s32ChnId = VdecChn;

    stDestChn.enModId = RK_ID_VO;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = VoChn;

    s32Ret = RK_MPI_SYS_UnBind(&stSrcChn, &stDestChn);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("failed with %#x!", s32Ret);
        return RK_FAILURE;
    }

    return s32Ret;
}

RK_S32 RK_VO_Close(PLAYER_INFO_S *ctx)
{
    RK_S32 s32Ret = RK_SUCCESS;

    s32Ret = RK_MPI_VO_DisableLayer(ctx->voLayer);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("RK_MPI_VO_DisableLayer disable vo layer failed");
    }

    s32Ret = RK_MPI_VO_Disable(ctx->voDev);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("RK_MPI_VO_Disable close dev failed");
    }

    RK_MPI_VO_UnBindLayer(ctx->voLayer, ctx->voDev);

    RK_MPI_VO_CloseFd();

    return s32Ret;
}

RK_S32 RK_VDEC_Start(PLAYER_INFO_S *ctx)
{
    RK_S32 s32Ret = RK_SUCCESS;

    VDEC_CHN_ATTR_S stAttr;
    VDEC_CHN_PARAM_S stVdecParam;

    MB_POOL_CONFIG_S stMbPoolCfg;
    VDEC_PIC_BUF_ATTR_S stVdecPicBufAttr;
    MB_PIC_CAL_S stMbPicCalResult;
    VDEC_MOD_PARAM_S stModParam;

    memset(&stAttr, 0, sizeof(VDEC_CHN_ATTR_S));
    memset(&stVdecParam, 0, sizeof(VDEC_CHN_PARAM_S));

    memset(&stModParam, 0, sizeof(VDEC_MOD_PARAM_S));


    if (ctx->bEnableMbPool) {
        stModParam.enVdecMBSource = MB_SOURCE_USER;
        s32Ret = RK_MPI_VDEC_SetModParam(&stModParam);
        if (s32Ret != RK_SUCCESS) {
            RK_LOGE("vdec %d SetModParam failed! errcode %x", ctx->u32ChnIndex, s32Ret);
            return s32Ret;
        }
    }

    stAttr.enMode = ctx->vDecInputMode;

    stAttr.enType = ctx->enCodecId;
    stAttr.u32PicWidth = ctx->u32SrcWidth;
    stAttr.u32PicHeight = ctx->u32SrcHeight;
    stAttr.u32FrameBufCnt = ctx->u32FrameBufferCnt;
    stAttr.u32StreamBufCnt = MAX_STREAM_CNT;

    if (!ctx->bEnableColmv) {
        stAttr.stVdecVideoAttr.bTemporalMvpEnable = RK_FALSE;
    }

    s32Ret = RK_MPI_VDEC_CreateChn(ctx->vdecChannel, &stAttr);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("create %d vdec failed! ", ctx->vdecChannel);
        return s32Ret;
    }

    //stVdecParam.stVdecVideoParam.enCompressMode = (COMPRESS_MODE_E)ctx->u32CompressMode;

    if (ctx->enCodecId == RK_VIDEO_ID_MJPEG) {
        stVdecParam.stVdecPictureParam.enPixelFormat = (PIXEL_FORMAT_E)ctx->s32OutputPixFmt;
    } else {
        stVdecParam.stVdecVideoParam.enCompressMode = (COMPRESS_MODE_E)ctx->u32CompressMode;
    }

    if (ctx->bEnableDei) {
        stVdecParam.stVdecVideoParam.bDeiEn = ctx->bEnableDei;
    }

//    if (ctx->bIsDeinterlace) {
//        stVdecParam.stVdecVideoParam.bDeiEn = ctx->bIsDeinterlace;
//    }
    // it is only effective to disable MV when decoding sequence output
    if (!ctx->bEnableColmv) {
        stVdecParam.stVdecVideoParam.enOutputOrder = VIDEO_OUTPUT_ORDER_DEC;
    }

    s32Ret = RK_MPI_VDEC_SetChnParam(ctx->vdecChannel, &stVdecParam);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("set chn %d param failed %x! ", ctx->vdecChannel, s32Ret);
        return s32Ret;
    }

    ctx->s32VdecChannelFd = RK_MPI_VDEC_GetFd(ctx->vdecChannel);
    if (ctx->s32VdecChannelFd <= 0) {
        RK_LOGE("get fd chn %d failed %d", ctx->vdecChannel, ctx->s32VdecChannelFd);
        return s32Ret;
    }

    if (ctx->bEnableMbPool) {
//        MB_POOL_CONFIG_S stMbPoolCfg;
//        MB_PIC_CAL_S stMbPicCalResult;
//        VDEC_PIC_BUF_ATTR_S stVdecPicBufAttr;

        memset(&stMbPoolCfg, 0, sizeof(MB_POOL_CONFIG_S));
        memset(&stVdecPicBufAttr, 0, sizeof(VDEC_PIC_BUF_ATTR_S));

        stVdecPicBufAttr.enCodecType = ctx->enCodecId;
        stVdecPicBufAttr.stPicBufAttr.u32Width = ctx->u32SrcWidth;
        stVdecPicBufAttr.stPicBufAttr.u32Height = ctx->u32SrcHeight;
        stVdecPicBufAttr.stPicBufAttr.enPixelFormat = (PIXEL_FORMAT_E)ctx->s32OutputPixFmt;
        stVdecPicBufAttr.stPicBufAttr.enCompMode = (COMPRESS_MODE_E)ctx->u32CompressMode;
        s32Ret = RK_MPI_CAL_VDEC_GetPicBufferSize(&stVdecPicBufAttr, &stMbPicCalResult);
        if (s32Ret != RK_SUCCESS) {
            RK_LOGE("get picture buffer size failed. err 0x%x", s32Ret);
            return s32Ret;
        }

        stMbPoolCfg.u64MBSize = stMbPicCalResult.u32MBSize;;
        stMbPoolCfg.u32MBCnt  = 25;
        stMbPoolCfg.enRemapMode = MB_REMAP_MODE_CACHED;
        stMbPoolCfg.bPreAlloc = RK_TRUE;
        ctx->stMbPool = RK_MPI_MB_CreatePool(&stMbPoolCfg);
        if (ctx->stMbPool == MB_INVALID_POOLID) {
            RK_LOGE("create pool failed!");
            return s32Ret;
        }
        s32Ret = RK_MPI_VDEC_AttachMbPool(ctx->vdecChannel, ctx->stMbPool);
        if (s32Ret != RK_SUCCESS) {
            RK_LOGE("attatc vdec mb pool %d failed! ", ctx->vdecChannel);
            return s32Ret;
        }
    }

    s32Ret = RK_MPI_VDEC_StartRecvStream(ctx->vdecChannel);
    if (s32Ret != RK_SUCCESS) {
        RK_LOGE("start recv chn %d failed %x! ", ctx->vdecChannel, s32Ret);
        return s32Ret;
    }

//    s32Ret = RK_MPI_VDEC_SetDisplayMode(ctx->vdecChannel, VIDEO_DISPLAY_MODE_PLAYBACK);
//    if (s32Ret != RK_SUCCESS) {
//        RK_LOGE("RK_MPI_VDEC_SetDisplayMode failed with %#x!", s32Ret);
//        return s32Ret;
//    }

    return RK_SUCCESS;
}

RK_S32 RK_VDEC_Stop(PLAYER_INFO_S *ctx)
{
    RK_MPI_VDEC_StopRecvStream(ctx->vdecChannel);

    if (ctx->s32VdecChannelFd > 0) {
        RK_MPI_VDEC_CloseFd(ctx->vdecChannel);
    }

    if (ctx->bEnableMbPool) {
        RK_MPI_VDEC_DetachMbPool(ctx->vdecChannel);
        RK_MPI_VDEC_DestroyChn(ctx->vdecChannel);
        RK_MPI_MB_DestroyPool(ctx->stMbPool);
    } else {
        RK_MPI_VDEC_DestroyChn(ctx->vdecChannel);
    }

    return RK_SUCCESS;
}

static RK_S32 mpi_vdec_free(void *opaque) {
    if (opaque)
    {
        free(opaque);
        opaque = nullptr;
    }
    return 0;
}

int RK_VDEC_SendPkt(void *pArgs,RK_U8 *pktData,int pktSize,RK_U64 pts)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MB_EXT_CONFIG_S stMbExtConfig;
    VDEC_STREAM_S stStream;
    PLAYER_INFO_S *ctx = (PLAYER_INFO_S *)pArgs;

    MB_BLK buffer = RK_NULL;
    RK_S32 s32ReachEOS = 0;

    //STREAM_INFO_S *pstStreamInfo = &pstThreadInfo->stStreamInfo;


    memset(&stMbExtConfig, 0, sizeof(MB_EXT_CONFIG_S));
    memset(&stStream, 0, sizeof(VDEC_STREAM_S));

    if(pktData!=nullptr)
    {
        RK_U8 *data = (RK_U8 *)reinterpret_cast<RK_U8 *>(calloc(pktSize, sizeof(RK_U8)));
        memset(data, 0,pktSize);
        memcpy(data,pktData,pktSize);
        memset(&stMbExtConfig, 0, sizeof(MB_EXT_CONFIG_S));
        stMbExtConfig.pFreeCB = mpi_vdec_free;
        stMbExtConfig.pOpaque = data;
        stMbExtConfig.pu8VirAddr = data;
        stMbExtConfig.u64Size = pktSize;

        RK_MPI_SYS_CreateMB(&buffer, &stMbExtConfig);

        stStream.u64PTS = 0;
        stStream.pMbBlk = buffer;
        stStream.u32Len = pktSize;
        stStream.bEndOfStream = s32ReachEOS ? RK_TRUE : RK_FALSE;
        stStream.bEndOfFrame = s32ReachEOS ? RK_TRUE : RK_FALSE;//RK_TRUE;
        stStream.bBypassMbBlk = RK_TRUE;
       // stStream.u64PTS = pts;
    }
    else
    {
        stStream.pMbBlk = MB_INVALID_HANDLE;
        stStream.u32Len = 0;
        stStream.bEndOfStream = RK_TRUE;
        stStream.bEndOfFrame =RK_TRUE;
    }

    s32Ret = RK_MPI_VDEC_SendStream(ctx->vdecChannel, &stStream, 20);
    if(s32Ret == RK_SUCCESS)
    {
       printf("RK_MPI_VDEC_SendStream  SUCCESS\n");
    }
    else if(s32Ret == RK_ERR_VDEC_BUF_FULL)
    {
        printf("RK_MPI_VDEC_SendStream  RK_ERR_VDEC_BUF_FULL\n");

    }
    else
    {
        RK_LOGV("RK_MPI_VDEC_SendStream failed with 0x%x\n", s32Ret);

    }


      RK_MPI_MB_ReleaseMB(stStream.pMbBlk);
      stStream.pMbBlk= nullptr;


      return s32Ret;
}

static void* RK_VDEC_SendStreamProc(void *pArgs)
{
    RK_S32 s32Ret = RK_SUCCESS;
    RK_BOOL bReachEos = RK_FALSE;
    MB_BLK pStreamBlk = MB_INVALID_HANDLE;
    MB_EXT_CONFIG_S stMbExtConfig;
    VDEC_STREAM_S stStream;
    PLAYER_INFO_S *ctx = (PLAYER_INFO_S *)pArgs;

    MB_BLK buffer = RK_NULL;
    RK_S32 s32ReachEOS = 0;
    RK_S32 s32Size = 0;
    //STREAM_INFO_S *pstStreamInfo = &pstThreadInfo->stStreamInfo;

    FILE *fp = NULL;

    memset(&stMbExtConfig, 0, sizeof(MB_EXT_CONFIG_S));
    memset(&stStream, 0, sizeof(VDEC_STREAM_S));

    // if(FF_DEMUX_Open(ctx->srcFileUri, &ctx->s32VideoStreamIdx, &ctx->s32AudioStreamIdx) < 0) {
    //     printf("FF_DEMUX_Open failed\n");
    //     return RK_NULL;
    // }

    fp = fopen(ctx->srcFileUri, "r");
    if (fp == RK_NULL) {
        RK_LOGE("open file %s failed", ctx->srcFileUri);
        return RK_NULL;
    }

    while (ctx->bVdecThreadStart) {

        // AVPacket *pkt = FF_DEMUX_Get_Pkt();
        // if (NULL == pkt)
        // {
        //     ctx->bVdecThreadStart = RK_FALSE;
        //     break;
        // }

        // if (pkt->stream_index != ctx->s32VideoStreamIdx) {
        //     FF_DEMUX_Release_Pkt(pkt);
        //     continue;
        // }

        RK_U8 *data = reinterpret_cast<RK_U8 *>(calloc(ctx->u32ReadSize, sizeof(RK_U8)));
        memset(data, 0, ctx->u32ReadSize);
        s32Size = fread(data, 1, ctx->u32ReadSize, fp);
        if (s32Size <= 0) {
            s32ReachEOS = 1;
        }

        memset(&stMbExtConfig, 0, sizeof(MB_EXT_CONFIG_S));
        stMbExtConfig.pFreeCB = mpi_vdec_free;
        stMbExtConfig.pOpaque = data;
        stMbExtConfig.pu8VirAddr = data;
        stMbExtConfig.u64Size = s32Size;

        RK_MPI_SYS_CreateMB(&buffer, &stMbExtConfig);

        stStream.u64PTS = 0;
        stStream.pMbBlk = buffer;
        stStream.u32Len = s32Size;
        stStream.bEndOfStream = s32ReachEOS ? RK_TRUE : RK_FALSE;
        stStream.bEndOfFrame = s32ReachEOS ? RK_TRUE : RK_FALSE;
        stStream.bBypassMbBlk = RK_TRUE;

        //printf("send stream to video decoder %p:%d\n", pkt->data, pkt->size);
__RETRY:
        s32Ret = RK_MPI_VDEC_SendStream(ctx->vdecChannel, &stStream, 200);
        if (s32Ret != RK_SUCCESS) {
            if (!ctx->bVdecThreadStart) {
                mpi_vdec_free(data);
                RK_MPI_SYS_Free(stStream.pMbBlk);
                break;
            }
            RK_LOGV("RK_MPI_VDEC_SendStream failed with 0x%x", s32Ret);
            goto  __RETRY;
        }
        RK_MPI_SYS_Free(stStream.pMbBlk);
        if (stStream.bEndOfStream) {
            RK_LOGE("reach eos");
            break;
        }
    }

    if (fp)
        fclose(fp);

    RK_LOGD("%s out\n", __FUNCTION__);
    return RK_NULL;
}

void dump_frame_to_file(VIDEO_FRAME_INFO_S *pstFrame, FILE *fp) {
    RK_U32 i;
    RK_U32 width    = 0;
    RK_U32 height   = 0;
    RK_U32 h_stride = 0;
    RK_U32 v_stride = 0;
    RK_U8 *base_y = RK_NULL;
    RK_U8 *base_c = RK_NULL;
    RK_U8 *base = RK_NULL;

    if (NULL == fp)
        return;

    base = (RK_U8 *)RK_MPI_MB_Handle2VirAddr(pstFrame->stVFrame.pMbBlk);
    RK_MPI_SYS_MmzFlushCache(pstFrame->stVFrame.pMbBlk, RK_TRUE);
    width = pstFrame->stVFrame.u32Width;
    height = pstFrame->stVFrame.u32Height;

    switch (pstFrame->stVFrame.enPixelFormat) {
        case RK_FMT_YUV420SP_VU :
        case RK_FMT_YUV420SP : {
            h_stride = pstFrame->stVFrame.u32VirWidth;
            v_stride = pstFrame->stVFrame.u32VirHeight;
            RK_LOGE("cur frame fmt:yuv420sp\n");
            base_y = base;
            base_c = base + h_stride * v_stride;

            for (i = 0; i < height; i++, base_y += h_stride) {
                fwrite(base_y, 1, width, fp);
            }
            for (i = 0; i < height / 2; i++, base_c += h_stride) {
                fwrite(base_c, 1, width, fp);
            }

        } break;

        case RK_FMT_YUV420SP_10BIT : {
            h_stride = pstFrame->stVFrame.u32VirWidth * 10 / 8;
            v_stride = pstFrame->stVFrame.u32VirHeight;
            RK_LOGE("cur frame fmt:yuv420sp_10bt\n");
            base_y = base;
            base_c = base + h_stride * v_stride;

            for (i = 0; i < height; i++, base_y += h_stride) {
                fwrite(base_y, 1, width, fp);
            }
            for (i = 0; i < height / 2; i++, base_c += h_stride) {
                fwrite(base_c, 1, width, fp);
            }
        } break;

        case RK_FMT_RGB565:
        case RK_FMT_BGR565: {
            h_stride = pstFrame->stVFrame.u32VirWidth * 2;
            v_stride = pstFrame->stVFrame.u32VirHeight;
            base_y = base;
            RK_LOGE("cur frame fmt:rgb565|bgr565\n");
            for (i = 0; i < height; i++, base_y += h_stride)
                fwrite(base_y, 1, width * 2, fp);
        } break;
        case RK_FMT_RGB888:
        case RK_FMT_BGR888: {
            h_stride = pstFrame->stVFrame.u32VirWidth * 3;
            v_stride = pstFrame->stVFrame.u32VirHeight;
            base_y = base;
             RK_LOGE("cur frame fmt:rgb888|bgr888\n");
            for (i = 0; i < height; i++, base_y += h_stride)
                fwrite(base_y, 1, width * 3, fp);
        } break;
        default : {
            RK_LOGE("not supported format %d\n", pstFrame->stVFrame.enPixelFormat);
        } break;
    }
}

RK_S32 mpi_vdec_poll_event(RK_S32 timeoutMsec, RK_S32 fd) {
    RK_S32 num_fds = 1;
    struct pollfd pollFds[num_fds];
    RK_S32 ret = 0;

    RK_ASSERT(fd > 0);
    memset(pollFds, 0, sizeof(pollFds));
    pollFds[0].fd = fd;
    pollFds[0].events = (POLLPRI | POLLIN | POLLERR | POLLNVAL | POLLHUP);

    ret = poll(pollFds, num_fds, timeoutMsec);
    if (ret > 0 && (pollFds[0].revents & (POLLERR | POLLNVAL | POLLHUP))) {
        RK_LOGE("fd:%d polled error", fd);
        return -1;
    }

    return ret;
}
int RK_VDEC_GetAFrame(PLAYER_INFO_S *ctx,VIDEO_FRAME_INFO_S *bframe)
{
    if(ctx == nullptr)
        return RK_FAILURE;

//    VIDEO_FRAME_INFO_S sFrame;
    RK_S32 s32Ret;

    RK_S32 s32FrameCount = 0;

    memset(bframe, 0, sizeof(VIDEO_FRAME_INFO_S));

   // printf("RK_MPI_VDEC_GetFrame \n");
    s32Ret = RK_MPI_VDEC_GetFrame(ctx->vdecChannel, bframe, MAX_TIME_OUT_MS);
    if (s32Ret >= 0) {
        s32FrameCount++;
        //printf("RK_MPI_VDEC_GetFrame  Ok\n");
        RK_LOGI("get chn %d frame %d", ctx->vdecChannel, s32FrameCount);


      //  RK_MPI_VDEC_ReleaseFrame(ctx->vdecChannel, &sFrame);
    }
    else if(s32Ret == RK_ERR_VDEC_BUF_EMPTY){
        printf("RK_MPI_VDEC_GetFrame is empty\n");
        RK_LOGI("Video decoder buffer is empty!");
        return 0;
    }
    else {
        printf("RK_MPI_VDEC_GetFrame failed\n");
        RK_LOGI("RK_VDEC_GetAFrame err : %#X",s32Ret);
        return -1;
    }



    return 1;
}

int cnt_ = 0;
void* RK_VDEC_DumpFrameProc(void *pArgs) {
    PLAYER_INFO_S *ctx = reinterpret_cast<PLAYER_INFO_S *>(pArgs);
    FILE *fp = RK_NULL;
    VIDEO_FRAME_INFO_S sFrame;
    RK_S32 s32Ret;
    char name[256] = {0};
    RK_S32 s32FrameCount = 0;

    memset(&sFrame, 0, sizeof(VIDEO_FRAME_INFO_S));

    if (ctx->dstFileUri != RK_NULL) {
        fp = fopen(ctx->dstFileUri, "wb");
        if (fp == RK_NULL) {
            RK_LOGE("can't open output file %s\n", ctx->dstFileUri);
            return NULL;
        }
    }

    while (ctx->bDumpFrameThreadStart) {
        if (ctx->s32VdecChannelFd > 0) {
            s32Ret = mpi_vdec_poll_event(-1, ctx->s32VdecChannelFd);
            if (s32Ret < 0) {
                if (ctx->bDumpFrameThreadStart)
                    break;

                usleep(1000llu);
                continue;
            }
        }
        if(cnt_ == 3000)
        {
            break;
        }
        s32Ret = RK_MPI_VDEC_GetFrame(ctx->vdecChannel, &sFrame, MAX_TIME_OUT_MS);
        if (s32Ret >= 0) {
            s32FrameCount++;
            RK_LOGI("get chn %d frame %d", ctx->vdecChannel, s32FrameCount);
            if ((sFrame.stVFrame.u32FrameFlag & FRAME_FLAG_SNAP_END) == FRAME_FLAG_SNAP_END) {
                RK_MPI_VDEC_ReleaseFrame(ctx->vdecChannel, &sFrame);
                RK_LOGI("chn %d reach eos frame.", ctx->vdecChannel);
                break;
            }

            dump_frame_to_file(&sFrame, fp);
            cnt_ ++;
            RK_MPI_VDEC_ReleaseFrame(ctx->vdecChannel, &sFrame);
        } else {
            if (ctx->bDumpFrameThreadStart)
                break;

            usleep(1000llu);
        }
    }

    if (fp)
        fclose(fp);
    RK_LOGI("%s out", __FUNCTION__);
    return RK_NULL;
}

RK_S32 RK_VDEC_StartSendStream(PLAYER_INFO_S *ctx)
{
    ctx->bVdecThreadStart = RK_TRUE;

    RK_S32 s32Ret = pthread_create(&(ctx->pVdecThread), 0, RK_VDEC_SendStreamProc, (RK_VOID *)ctx);
    if (s32Ret < 0)
        return RK_FAILURE;

    return RK_SUCCESS;
}

RK_S32 RK_VDEC_StartDumpFrame(PLAYER_INFO_S *ctx)
{
    ctx->bDumpFrameThreadStart = RK_TRUE;
    //memcpy(&ctx->stStreamInfo, pstStreamInfo, sizeof(STREAM_INFO_S));

    RK_S32 s32Ret = pthread_create(&(ctx->pDumpFrameThread), 0, RK_VDEC_DumpFrameProc, (RK_VOID *)ctx);
    if (s32Ret < 0)
        return RK_FAILURE;

    return RK_SUCCESS;
}

RK_S32 RK_VDEC_StopSendStream(PLAYER_INFO_S *ctx)
{
    if (RK_TRUE == ctx->bVdecThreadStart) {
        ctx->bVdecThreadStart = RK_FALSE;
        pthread_join(ctx->pVdecThread, 0);
    }

    return RK_SUCCESS;
}

RK_S32 RK_VDEC_StopDumpFrame(PLAYER_INFO_S *ctx)
{
    if (RK_TRUE == ctx->bDumpFrameThreadStart) {
        ctx->bDumpFrameThreadStart = RK_FALSE;
        pthread_join(ctx->pDumpFrameThread, 0);
    }

    return RK_SUCCESS;
}
