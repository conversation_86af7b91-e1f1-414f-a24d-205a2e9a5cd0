#ifndef RK_PLAYER_H
#define RK_PLAYER_H

#include <signal.h>
#include <cstdio>
#include <cerrno>
#include <cstring>
#include <cstdlib>
#include <unistd.h>
#include <pthread.h>
#include <sys/stat.h>
#include <sys/poll.h>
#include <sys/types.h>
#include <atomic>
#include <mutex>

#include "rk_debug.h"
#include "rk_mpi_vdec.h"
#include "rk_mpi_sys.h"
#include "rk_mpi_mb.h"
#include "rk_mpi_cal.h"
#include "rk_mpi_vo.h"


// #include "demux.h"

typedef struct _rkMpiVDECCtx {
    const char *srcFileUri;
    const char *dstFileUri;

    RK_U32 u32SrcWidth;
    RK_U32 u32SrcHeight;
    RK_U32 u32ReadSize;
    RK_U32 u32ChnIndex;
    //the video decoding settings
    VIDEO_MODE_E vDecInputMode;

    RK_CODEC_ID_E enCodecId;
    RK_U32 u32CompressMode;
    RK_U32 u32FrameBufferCnt;
    RK_BOOL bEnableMbPool;

    RK_BOOL bIsDeinterlace;
    RK_BOOL bEnableColmv;
    RK_BOOL bEnableDei;
    MB_POOL stMbPool;
    VDEC_CHN vdecChannel;
    RK_S32 s32VdecChannelFd;

    //the demux settings
    // RK_S32 s32VideoStreamIdx;
    // RK_S32 s32AudioStreamIdx;
    // AVPacket *pkt;

    //the output settings
    VO_INTF_TYPE_E enIntfType;
    VO_INTF_SYNC_E enIntfSync;
    VO_LAYER  voLayer;
    VO_DEV  voDev;
    VO_LAYER_MODE_E voLayerMode;
    RK_S32 dispPixFmt;
    RK_U32 u32DispFrameRate;

    //the video decoding thread settings
    pthread_t pVdecThread;
    pthread_t pDumpFrameThread;
    RK_BOOL bVdecThreadStart;
    RK_BOOL bDumpFrameThreadStart;
    RK_S32 s32OutputPixFmt;

} PLAYER_INFO_S;

RK_S32 RK_VDEC_Start(PLAYER_INFO_S *ctx);
RK_S32 RK_VDEC_Stop(PLAYER_INFO_S *ctx);
RK_S32 RK_VDEC_StartSendStream(PLAYER_INFO_S *ctx);
RK_S32 RK_VDEC_StopSendStream(PLAYER_INFO_S *ctx);
RK_S32 RK_VDEC_StartDumpFrame(PLAYER_INFO_S *ctx);
RK_S32 RK_VDEC_StopDumpFrame(PLAYER_INFO_S *ctx);

void dump_frame_to_file(VIDEO_FRAME_INFO_S *pstFrame, FILE *fp);
int RK_INIT_Decoder(void *ctx_,int width,int height,RK_CODEC_ID_E vdecType);
int RK_VDEC_SendPkt(void *pArgs,RK_U8 *pktData,int pktSize,RK_U64 pts);
int RK_VDEC_GetAFrame(PLAYER_INFO_S *ctx,VIDEO_FRAME_INFO_S *bframe);
RK_S32 mpi_vdec_poll_event(RK_S32 timeoutMsec, RK_S32 fd);
#endif // RK_PLAYER_H
