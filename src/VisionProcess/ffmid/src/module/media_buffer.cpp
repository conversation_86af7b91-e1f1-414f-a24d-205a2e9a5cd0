#include "base/media_buffer.hpp"


MediaBuffer::MediaBuffer(size_t _size)
{
    index =  0;
    data = nullptr;
    size = 0;
    active_data = nullptr;
    active_size = 0;
    p_ustimestamp = 0;
    d_ustimestamp = 0;
    eos = false;
    private_data = nullptr;

    media_type = BUFFER_TYPE_VIDEO;
    status = false;
    ref_count = 0;
    if(_size > 0)
    {
        allocBuffer(_size);
    }
}

MediaBuffer::~MediaBuffer()
{
    if(data)
    {
        free(data);
        data = nullptr;
    }
    size = 0;
}

void MediaBuffer::allocBuffer(size_t _size)
{
     if(_size > 0)
     {
         if(data != nullptr)
         {
             free(data);
             data = nullptr;
         }
         data = malloc(_size);
         size = _size;
     }

}

void MediaBuffer::fillWithBlack()
{

}

bool MediaBuffer::getStatus()
{
    return status;
}

uint16_t MediaBuffer::increaseRefCount()
{
    ref_count ++;
}

uint16_t MediaBuffer::decreaseRefCount()
{
    if(ref_count != 0)
    {
        ref_count --;
        if(ref_count == 0)
        {
            if(data)
            {
                free(data);
                data = nullptr;
            }
            size = 0;
        }
    }
    return ref_count;
}
