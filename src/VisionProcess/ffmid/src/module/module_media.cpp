#include "module_media.hpp"


ModuleMedia::ModuleMedia(const char *name_)
{
    output_data_callback = nullptr;
    work_thread = nullptr;
    other_work_thread = nullptr;

}

ModuleMedia::~ModuleMedia()
{

}

void ModuleMedia::start()
{


    setup();

    work_flag = true;
    work_thread = new thread(&ModuleMedia::work,this);


    for(int idx = 0;idx < consumers.size();idx ++)
    {
        consumers[idx].get()->start();

    }

}

void ModuleMedia::stop()
{
    for(int idx = 0;idx < consumers.size();idx ++)
    {
        consumers[idx].get()->stop();
    }

    work_flag = false;
    if(work_thread)
    {
        if(work_thread->joinable())
        {
            work_thread->join();
        }
        delete work_thread;
        work_thread = nullptr;
    }
    teardown();
}

void ModuleMedia::setProductor(shared_ptr<ModuleMedia> module)
{
        productor = module;
        shared_ptr<ModuleMedia> consumer(this);
        module.get()->addConsumer(consumer);
}

shared_ptr<ModuleMedia> ModuleMedia::getProductor()
{
    return productor.lock();
}

void ModuleMedia::addConsumer(shared_ptr<ModuleMedia> consumer)
{
    consumers.push_back(consumer);
}

void ModuleMedia::removeConsumer(shared_ptr<ModuleMedia> consumer)
{
    bool isRemoved = false;
    int idx = 0;
    for(;idx < consumers.size();idx ++)
    {
        if(consumers[idx] == consumer)
        {
            isRemoved = true;
            break;
        }
    }
    if(isRemoved)
    {
        consumers.erase(consumers.begin() + idx);
    }
}

shared_ptr<MediaBuffer> ModuleMedia::getBufferFromIndex(uint16_t index)
{
   shared_lock<shared_timed_mutex> lock(productor_mtx);
   if(strcmp(name,"ModuleMppDec") == 0)
   {
       int k = 0;
   }
   if(buffer_pool.size() == 0)
   {
       return nullptr;
   }
   shared_ptr<MediaBuffer> buf = buffer_pool[index];

   if(buf->getMediaBufferType() != BUFFER_TYPE_VIDEO )
   {
         buf = nullptr;
   }

   buffer_pool.erase(buffer_pool.begin() + index);
   return buf;
}

void ModuleMedia::setOutputDataCallback(void_object_p ctx, callback_handler callback)
{
    callback_ctx = ctx;
    output_data_callback = callback;
}

ModuleMedia::ConsumeResult ModuleMedia::doConsume(shared_ptr<MediaBuffer> input_buffer, shared_ptr<MediaBuffer> output_buffer)
{
    int k = 0;
}

ModuleMedia::ProduceResult ModuleMedia::doProduce(shared_ptr<MediaBuffer> buffer)
{

}

int ModuleMedia::initBuffer()
{

}

int ModuleMedia::initBuffer(VideoBuffer::BUFFER_TYPE buffer_type)
{

}

void ModuleMedia::bufferReleaseCallBack(shared_ptr<MediaBuffer> buffer)
{

}

cv_status ModuleMedia::waitForProduce(std::unique_lock<mutex> &lk)
{
       return cv_status::timeout;
}

void ModuleMedia::waitAllForConsume()
{
    return ;
}

cv_status ModuleMedia::waitForConsume(std::unique_lock<mutex> &lk)
{
    return cv_status::timeout;
}

void ModuleMedia::notifyProduce()
{

}

void ModuleMedia::notifyConsume()
{

}

void ModuleMedia::work()
{

    if(strcmp(name,"ModuleMppDec") == 0)
    {
        other_work_flag = true;
        other_work_thread = new thread(&ModuleMedia::otherWork,this);
    }

    while(work_flag)
    {
       shared_ptr<ModuleMedia> productor_ptr = productor.lock();

       if(!productor_ptr)
       {
           shared_ptr<MediaBuffer> buffer(new MediaBuffer);
           ProduceResult ret = this->doProduce(buffer);
           if(ret == PRODUCE_SUCCESS)
           {
               unique_lock<shared_timed_mutex> lock(productor_mtx);
               buffer_pool.push_back(buffer);

           }
           else if(ret == PRODUCE_EMPTY)
           {
                this_thread::sleep_for(chrono::milliseconds(5));
                continue;
           }
           else {
               work_flag = false;
               break;
           }
       }
       else if(productor_ptr && this->getConsumersCount() > 0)
       {
          shared_ptr<MediaBuffer> srcBuf =  productor_ptr->getBufferFromIndex(0);
          if(srcBuf == nullptr)
          {
              this_thread::sleep_for(chrono::milliseconds(10));
              continue;
          }
          shared_ptr<MediaBuffer> dstBuf(new MediaBuffer);
          ConsumeResult ret = this->doConsume(srcBuf,dstBuf);
          if(ret == CONSUME_SUCCESS)
          {
              if(output_data_callback)
              {
                    output_data_callback(callback_ctx,dstBuf);
              }

              unique_lock<shared_timed_mutex> lock(productor_mtx);
              buffer_pool.push_back(dstBuf);

          }

       }
       else if(productor_ptr && this->getConsumersCount() == 0)
       {
           shared_ptr<MediaBuffer> srcBuf =  productor_ptr->getBufferFromIndex(0);
           if(srcBuf == nullptr)
           {
               this_thread::sleep_for(chrono::milliseconds(10));
               continue;
           }


           shared_ptr<MediaBuffer> buffer(new MediaBuffer);
           ConsumeResult ret = this->doConsume(srcBuf,buffer);
           if(ret == CONSUME_SUCCESS)
           {
               if(output_data_callback)
               {
                    output_data_callback(callback_ctx,buffer);
               }
           }

       }


    }
    other_work_flag = false;
    if(other_work_thread && other_work_thread->joinable())
    {
        other_work_thread->join();
        delete other_work_thread;
        other_work_thread = nullptr;
    }

}

void ModuleMedia::otherWork()
{
    while(other_work_flag)
    {
        shared_ptr<MediaBuffer> buffer(new MediaBuffer);
        int ret = doProduce(buffer);
        if(ret == PRODUCE_SUCCESS)
        {
            unique_lock<shared_timed_mutex> lock(productor_mtx);
            buffer_pool.push_back(buffer);

        }
    }
}

void ModuleMedia::_dumpPipe(int depth, std::function<void (ModuleMedia *)> func)
{

}

void ModuleMedia::printOutputPara(ModuleMedia *module)
{
    return ;
}

void ModuleMedia::printSummary(ModuleMedia *module)
{

}

int ModuleMedia::checkInputPara()
{

}

void ModuleMedia::reset()
{

}

void ModuleMedia::resetModule()
{

}

int ModuleMedia::nextBufferPos(uint16_t pos)
{

}

void ModuleMedia::produceOneBuffer(shared_ptr<MediaBuffer> buffer)
{

}

void ModuleMedia::consumeOneBuffer()
{

}

void ModuleMedia::consumeOneBufferNoLock()
{

}

shared_ptr<MediaBuffer> ModuleMedia::inputBufferQueueTail()
{

}

bool ModuleMedia::inputBufferQueueIsFull()
{

}

bool ModuleMedia::inputBufferQueueIsEmpty()
{

}
