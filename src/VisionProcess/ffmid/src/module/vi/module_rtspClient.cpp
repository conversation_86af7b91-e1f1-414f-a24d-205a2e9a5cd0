#include "module_rtspClient.hpp"


ModuleRtspClient::ModuleRtspClient(char* rtsp_url, RTSP_STREAM_TYPE _stream_type, bool enable_video, bool enable_audio)
{

    rtsp_client = make_shared<RTSPClient>(rtsp_url,_stream_type,enable_video,enable_audio);
    encodecType = -1;
    name = "ModuleRtspClient";
}

ModuleRtspClient::~ModuleRtspClient()
{

}

int ModuleRtspClient::changeSource(const string& rtsp_url, RTSP_STREAM_TYPE _stream_type)
{
      rtsp_client = make_shared<RTSPClient>(rtsp_url.c_str(),_stream_type,true,true);
}

int ModuleRtspClient::init()
{
   if(!rtsp_client.get()->Init())
   {
       return -1;
   }
   rtsp_client.get()->SetTimeout(time_msec);
   int width = rtsp_client.get()->GetVideoWidth();

   int height = rtsp_client.get()->GetVideoHeight();

    ImagePara para;
    para.width = width;
    para.height = height;
    para.hstride = width;
    para.vstride = height;
    setOutputImagePara(para);
    return 0;
}

const uint8_t *ModuleRtspClient::videoExtraData()
{
    return nullptr;
}

unsigned ModuleRtspClient::videoExtraDataSize()
{
    return 0;
}

const uint8_t *ModuleRtspClient::audioExtraData()
{
    return nullptr;
}

unsigned ModuleRtspClient::audioExtraDataSize()
{
    return 0;
}

int ModuleRtspClient::audioChannel()
{
   return  rtsp_client.get()->GetAudioChannel();
}

int ModuleRtspClient::audioSampleRate()
{
    return rtsp_client.get()->GetAudioSampleRate();
}

uint32_t ModuleRtspClient::videoFPS()
{
    return rtsp_client.get()->GetVideoFPS();
}

ModuleRtspClient::SESSION_STATUS ModuleRtspClient::getSessionStatus()
{
    RTSPClient::RTSPPLAYER_STATUS status = rtsp_client.get()->GetRtspPlayerStatus();
    if(status == RTSPClient::RTSPPLAYER_STATUS_CLOSED)
    {
        return SESSION_STATUS_CLOSED;
    }
    else if(status == RTSPClient::RTSPPLAYER_STATUS_OPENED)
    {
        return SESSION_STATUS_OPENED;
    }
    else if(status == RTSPClient::RTSPPLAYER_STATUS_PLAYING)
    {
        return SESSION_STATUS_PLAYING;
    }
    else {
        return SESSION_STATUS_PAUSE;
    }
}

int ModuleRtspClient::getEncodecType()
{
    if(rtsp_client.get()->GetCodecType() == AV_CODEC_ID_H264)
    {
        return DECODE_TYPE_H264;
    }
    else if(rtsp_client.get()->GetCodecType() == AV_CODEC_ID_HEVC)
    {
        return DECODE_TYPE_H265;
    }
    else if(rtsp_client.get()->GetCodecType() == AV_CODEC_ID_MJPEG)
    {
        return DECODE_TYPE_MJPEG;
    }
    return DECODE_TYPE_MAX;
}

ModuleMedia::ProduceResult ModuleRtspClient::doProduce(shared_ptr<MediaBuffer> output_buffer)
{


    int ret = rtsp_client.get()->GetOnePkt(output_buffer);
    if(ret == -1)
    {
          return PRODUCE_EMPTY;
    }
    int streamIdx = output_buffer->getIndex();
    if(output_buffer.get()->getData() == nullptr)
    {
        return PRODUCE_EMPTY;
    }
    else {

        if(streamIdx == rtsp_client.get()->GetVideoStreamIdx())
        {
            output_buffer.get()->setMediaBufferType(BUFFER_TYPE_VIDEO);
            ImagePara param;
            param.width = rtsp_client.get()->GetVideoWidth();
            param.height = rtsp_client.get()->GetVideoHeight();
            param.v4l2Fmt = (uint32_t)rtsp_client.get()->GetCodecType();
            output_buffer.get()->setImagePara(param);
            output_buffer->setMediaBufferType(BUFFER_TYPE_VIDEO);

        }
        else if(streamIdx == rtsp_client.get()->GetAudioStreamIdx())
        {
            output_buffer.get()->setMediaBufferType(BUFFER_TYPE_AUDIO);
            SampleInfo info;
            info.channels = rtsp_client.get()->GetAudioChannel();
            info.sample_rate = rtsp_client.get()->GetAudioSampleRate();

            AVSampleFormat sampleFmt =(AVSampleFormat)rtsp_client.get()->GetAudioFormat();
            if(sampleFmt == AV_SAMPLE_FMT_U8)
            {
                    info.fmt = SAMPLE_FMT_U8;
            }
            else if(sampleFmt == AV_SAMPLE_FMT_S16)
            {
                    info.fmt = SAMPLE_FMT_S16;
            }
            else if(sampleFmt == AV_SAMPLE_FMT_S32)
            {
                    info.fmt = SAMPLE_FMT_S32;
            }
            else if(sampleFmt == AV_SAMPLE_FMT_FLT)
            {
                    info.fmt = SAMPLE_FMT_FLT;
            }
            else if(sampleFmt == AV_SAMPLE_FMT_U8P)
            {
                    info.fmt = SAMPLE_FMT_U8P;
            }
            else if(sampleFmt == AV_SAMPLE_FMT_S16P)
            {
                    info.fmt = SAMPLE_FMT_S16P;
            }
            else if(sampleFmt == AV_SAMPLE_FMT_S32P)
            {
                    info.fmt = SAMPLE_FMT_S32P;
            }
            else if(sampleFmt == AV_SAMPLE_FMT_FLTP)
            {
                    info.fmt = SAMPLE_FMT_FLTP;
            }
            output_buffer.get()->setSamplePara(info);
            output_buffer->setMediaBufferType(BUFFER_TYPE_AUDIO);
        }
        return PRODUCE_SUCCESS;

    }
}

void ModuleRtspClient::bufferReleaseCallBack(shared_ptr<MediaBuffer> buffer)
{

}

bool ModuleRtspClient::setup()
{
    return rtsp_client.get()->Start();
}

bool ModuleRtspClient::teardown()
{
      rtsp_client.get()->Stop();
      return true;
}

int ModuleRtspClient::fromRtpGetVideoParameter()
{

}

void ModuleRtspClient::closeHandlerFunc(void *arg, int err, int result)
{

}

bool ModuleRtspClient::open()
{

}
