#include "module_rga.hpp"

ModuleRga::ModuleRga()
{
    rga = make_shared<FFRga>();
}

ModuleRga::ModuleRga(const ImagePara &output_para, RgaRotate rotate)
{

    rotate = rotate;
    setOutputImagePara(output_para);
    rga = make_shared<FFRga>();
    name = "ModuleRga";
}

ModuleRga::ModuleRga(const ImagePara &input_para, const ImagePara &output_para, RgaRotate rotate)
{
    setInputImagePara(input_para);
    setOutputImagePara(output_para);
    rotate = rotate;
    rga = make_shared<FFRga>();
    name = "ModuleRga";
}

ModuleRga::~ModuleRga()
{
    rga->DeInit();
}

int ModuleRga::changeOutputPara(const ImagePara &para)
{
    setOutputImagePara(para);
}

int ModuleRga::init()
{
    shared_ptr<ModuleMedia> productor =  getProductor();
    if(productor != nullptr)
    {
       ImagePara inputPara = productor->getOutputImagePara();
       uint32_t width = inputPara.width;
       uint32_t height = inputPara.height;


       int inputFmt = RK_FORMAT_BGR_888;
       if(inputPara.v4l2Fmt == V4L2_PIX_FMT_NV12)
       {
           inputFmt = RK_FORMAT_YCbCr_420_SP;
       }
       rga->SetSrcRgaBuffer(width,height,inputFmt,nullptr);

       ImagePara outputPara = this->getOutputImagePara();
       uint32_t dstWidth = outputPara.width;
       uint32_t dstHeight = outputPara.height;

       int outFmt = RK_FORMAT_BGR_888;
       if(outputPara.v4l2Fmt == V4L2_PIX_FMT_NV12)
       {
           outFmt = RK_FORMAT_YCbCr_420_SP;
       }
       else if(outputPara.v4l2Fmt == V4L2_PIX_FMT_NV21)
       {
           outFmt =RK_FORMAT_YCrCb_420_SP;
       }
       else if(outputPara.v4l2Fmt == V4L2_PIX_FMT_RGB24)
       {
           outFmt =RK_FORMAT_RGB_888;
       }
       else if(outputPara.v4l2Fmt == V4L2_PIX_FMT_RGBA32)
       {
           outFmt =RK_FORMAT_RGBA_8888;
       }
       else if(outputPara.v4l2Fmt == V4L2_PIX_FMT_BGRA32)
       {
           outFmt =RK_FORMAT_BGRA_8888;
       }


       rga->SetDstRgaBuffer(dstWidth,dstHeight,outFmt,nullptr);

       if(rotate == RGA_ROTATE_NONE)
       {
           rga->SetRgaProcessType(FFRga::CVT_COLOR,0);
       }
       else if(rotate == RGA_ROTATE_90)
       {
           rga->SetRgaProcessType(FFRga::ROTATE,90);
       }
       else if(rotate == RGA_ROTATE_180)
       {
           rga->SetRgaProcessType(FFRga::ROTATE,180);
       }
       else if(rotate == RGA_ROTATE_270)
       {
           rga->SetRgaProcessType(FFRga::ROTATE,270);
       }
       else if(rotate == RGA_ROTATE_270)
       {
           rga->SetRgaProcessType(FFRga::ROTATE,270);
       }
       else if(rotate == RGA_ROTATE_VFLIP)
       {
           rga->SetRgaProcessType(FFRga::FLIP,IM_HAL_TRANSFORM_FLIP_V);
       }
       else if(rotate == RGA_ROTATE_HFLIP)
       {
           rga->SetRgaProcessType(FFRga::FLIP,IM_HAL_TRANSFORM_FLIP_H);
       }
       bool ret = rga->Init();
       if(!ret)
       {
           rga->DeInit();
           return -1;
       }
       return 0;
    }
    return -1;
}

void ModuleRga::setSrcPara(uint32_t fmt, uint32_t x, uint32_t y, uint32_t w, uint32_t h, uint32_t hstride, uint32_t vstride)
{


        ImagePara srcParam;
        srcParam.v4l2Fmt = fmt;
        srcParam.width = w;
        srcParam.height = h;
        srcParam.hstride = hstride;
        srcParam.vstride = vstride;
        setInputImagePara(srcParam);


}

void ModuleRga::setDstPara(uint32_t fmt, uint32_t x, uint32_t y, uint32_t w, uint32_t h, uint32_t hstride, uint32_t vstride)
{
    ImagePara dstParam;
    dstParam.v4l2Fmt = fmt;
    dstParam.width = w;
    dstParam.height = h;
    dstParam.hstride = hstride;
    dstParam.vstride = vstride;
    setOutputImagePara(dstParam);
}

void ModuleRga::setPatPara(uint32_t fmt, uint32_t x, uint32_t y, uint32_t w, uint32_t h, uint32_t hstride, uint32_t vstride)
{
    ImagePara dstParam;
    dstParam.v4l2Fmt = fmt;
    dstParam.width = w;
    dstParam.height = h;
    dstParam.hstride = hstride;
    dstParam.vstride = vstride;
    setOutputImagePara(dstParam);
}

void ModuleRga::setSrcBuffer(void *buf)
{

}

void ModuleRga::setSrcBuffer(int fd)
{

}

void ModuleRga::setPatBuffer(void *buf, ModuleRga::RGA_BLEND_MODE mode)
{

}

void ModuleRga::setPatBuffer(int fd, ModuleRga::RGA_BLEND_MODE mode)
{

}

void ModuleRga::setBlendCallback(void_object_p ctx, callback_handler callback)
{

}

void ModuleRga::setRotate(RgaRotate rotate)
{
    rotate = rotate;
}

void ModuleRga::setRgaSchedulerCore(ModuleRga::RGA_SCHEDULER_CORE core)
{

}

shared_ptr<MediaBuffer> ModuleRga::newModuleMediaBuffer(VideoBuffer::BUFFER_TYPE buffer_type)
{

}

shared_ptr<MediaBuffer> ModuleRga::exportUseMediaBuffer(shared_ptr<MediaBuffer> match_buffer, shared_ptr<MediaBuffer> input_buffer, int flag)
{

}

int ModuleRga::dstFillColor(int color)
{

}

void ModuleRga::alignStride(uint32_t fmt, uint32_t &wstride, uint32_t &hstride)
{

}

shared_ptr<FFRga> ModuleRga::GetRgaObj()
{
    return rga;
}

ModuleMedia::ConsumeResult ModuleRga::doConsume(shared_ptr<MediaBuffer> input_buffer, shared_ptr<MediaBuffer> output_buffer)
{
    if(output_buffer->getData() == nullptr)
    {
        output_buffer->allocBuffer(rga->GetDstDataSize());
    }
    memcpy(rga->GetSrcData(),input_buffer->getData(),input_buffer->getSize());
    bool ret = rga->ProcessRga();
    if(!ret)
    {
        return CONSUME_FAILED;
    }

    memcpy(output_buffer->getData(),rga->GetDstData(),rga->GetDstDataSize());
    ImagePara outPara = this->getOutputImagePara();
    output_buffer->setImagePara(outPara);

    return CONSUME_SUCCESS;
}
