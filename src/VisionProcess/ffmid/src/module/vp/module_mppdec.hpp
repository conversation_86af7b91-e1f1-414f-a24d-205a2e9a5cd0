#ifndef MODULEMPPDEC_H
#define MODULEMPPDEC_H


#include "module/module_media.hpp"
#include "base/ff_type.hpp"
#include "vdec/mppdecoder.h"



class ModuleMppDec : public ModuleMedia
{
private:
    thread *getFrameThrd = nullptr;
    shared_ptr<MppDecoder> dec;
    DecodeType decode_type;
    uint32_t need_split;

protected:
    virtual ConsumeResult doConsume(shared_ptr<MediaBuffer> input_buffer, shared_ptr<MediaBuffer> output_buffer) override;
    virtual ProduceResult doProduce(shared_ptr<MediaBuffer> buffer) override;
    virtual int initBuffer() override;
    virtual void bufferReleaseCallBack(shared_ptr<MediaBuffer> buffer) override;

    void reset() override;

public:
    ModuleMppDec();
    ModuleMppDec(const ImagePara& input_para);
    ModuleMppDec(const ImagePara& input_para, DecodeType type);
    ~ModuleMppDec();
    void setNeedSplit(uint32_t split);
    void setAlign(DecodeType decodeType);
    int init() override;
     //shared_ptr<MediaBuffer> getBufferFromIndex(uint16_t index);

};

#endif // MODULEMPPDEC_H
