#include "module_mppdec.hpp"


void cutYuv(unsigned char *tarYuv, unsigned char *srcYuv, int startW,
            int startH, int cutW, int cutH, int srcW, int srcH)
{
  int i;
  int j = 0;
  int k = 0;
  //分配一段内存，用于存储裁剪后的Y分量
  unsigned char *tmpY = (unsigned char *)malloc(cutW*cutH);
  //分配一段内存，用于存储裁剪后的UV分量
    unsigned char *tmpUV = (unsigned char *)malloc(cutW*cutH/2);

         for(i=startH; i<cutH+startH; i++) {
           // 逐行拷贝Y分量，共拷贝cutW*cutH
           memcpy(tmpY+j*cutW, srcYuv+startW+i*srcW, cutW);
           j++;
         }
         for(i=startH/2; i<(cutH+startH)/2; i++) {
           //逐行拷贝UV分量，共拷贝cutW*cutH/2
           memcpy(tmpUV+k*cutW, srcYuv+startW+srcW*srcH+i*srcW, cutW);
           k++;
         }
         //将拷贝好的Y，UV分量拷贝到目标内存中
         memcpy(tarYuv, tmpY, cutW*cutH);
         memcpy(tarYuv+cutW*cutH, tmpUV, cutW*cutH/2);
         free(tmpY);
         free(tmpUV);


}


void ModuleMppDec::setAlign(DecodeType decodeType)
{
    decode_type = decodeType;
}

ModuleMedia::ConsumeResult ModuleMppDec::doConsume(shared_ptr<MediaBuffer> input_buffer, shared_ptr<MediaBuffer> output_buffer)
{
    if(input_buffer.get()->getData() == nullptr)
    {
          return CONSUME_FAILED;
    }
    if(input_buffer->getMediaBufferType() != BUFFER_TYPE_VIDEO)
    {
           return CONSUME_SKIP;
    }

    int ret = CONSUME_SUCCESS;
    do{
        ret = dec.get()->Vdec_SendPkt((RK_U8 *)input_buffer.get()->getData(),input_buffer.get()->getSize(),input_buffer.get()->getDUstimestamp());
        if(ret == RK_SUCCESS)
        {
            return CONSUME_SKIP;
        }
        else {
            if(ret == RK_ERR_VDEC_BUF_FULL)
            {
                this_thread::sleep_for(chrono::milliseconds(25));
                continue;
            }
            else {
               return CONSUME_FAILED;
            }
        }
    }while(1);
    return CONSUME_SUCCESS;
}

ModuleMedia::ProduceResult ModuleMppDec::doProduce(shared_ptr<MediaBuffer> buffer)
{
    VIDEO_FRAME_INFO_S pFrame;
    memset(&pFrame, 0, sizeof(VIDEO_FRAME_INFO_S));

//    int ret = RK_VDEC_GetAFrame(&m_VDecCtx,&pFrame);
    int ret = dec.get()->Vdec_GetOneFrame(&pFrame);
    if(ret == 0)
    {

        printf("RK_ERR_VDEC_BUF_EMPTY\n");
        return PRODUCE_EMPTY;

    }
    else if(ret == -1)
    {

        printf("RK_VDEC_GetAFrame error!\n");
        return PRODUCE_FAILED;
    }
    RK_U8 *data = dec.get()->Vdec_MB_Handle2VirAddr(&pFrame);
    if(buffer.get()->getData() == nullptr)
    {
        buffer.get()->allocBuffer(pFrame.stVFrame.u32Width * pFrame.stVFrame.u32Height * 3 /2);
        if(pFrame.stVFrame.u32VirWidth > pFrame.stVFrame.u32Width || pFrame.stVFrame.u32VirHeight >  pFrame.stVFrame.u32Height)
        {
            cutYuv((RK_U8 *)buffer.get()->getData(),data,0,0,pFrame.stVFrame.u32Width,pFrame.stVFrame.u32Height,pFrame.stVFrame.u32VirWidth,pFrame.stVFrame.u32VirWidth);
        }
        else {
            memcpy(buffer.get()->getData(),data,pFrame.stVFrame.u32Width * pFrame.stVFrame.u32Height * 3 /2);
        }
        ImagePara param(pFrame.stVFrame.u32Width,pFrame.stVFrame.u32Height,0,0,V4L2_PIX_FMT_NV12);
        buffer.get()->setImagePara(param);
    }

    dec.get()->Vdec_ReleaseFrame(&pFrame);
    return PRODUCE_SUCCESS;
}

int ModuleMppDec::initBuffer()
{

}

void ModuleMppDec::bufferReleaseCallBack(shared_ptr<MediaBuffer> buffer)
{
      if(buffer.get()->getData() != nullptr)
      {
          free(buffer.get()->getData());
          buffer.get()->setData(nullptr);
          buffer.get()->setSize(0);
      }
}




void ModuleMppDec::reset()
{
    dec.get()->DeInit();
}

ModuleMppDec::ModuleMppDec()
{
   dec = make_shared<MppDecoder>();
   name = "ModuleMppDec";
}

ModuleMppDec::ModuleMppDec(const ImagePara &input_para)
{
     dec = make_shared<MppDecoder>();
     dec.get()->SetImgWidth(input_para.width);
     dec.get()->SetImgHeight(input_para.height);
     name = "ModuleMppDec";
}

ModuleMppDec::ModuleMppDec(const ImagePara &input_para, DecodeType type)
{
    dec = make_shared<MppDecoder>();
    dec.get()->SetImgWidth(input_para.width);
    dec.get()->SetImgHeight(input_para.height);
    if(type == DECODE_TYPE_H264)
    {
       dec.get()->SetVdecType(RK_VIDEO_ID_AVC);
    }
    else if(type == DECODE_TYPE_H265)
    {
        dec.get()->SetVdecType(RK_VIDEO_ID_HEVC);
    }
    else if(type == DECODE_TYPE_MJPEG)
    {
        dec.get()->SetVdecType(RK_VIDEO_ID_MJPEG);
    }
    name = "ModuleMppDec";
}

ModuleMppDec::~ModuleMppDec()
{

}

void ModuleMppDec::setNeedSplit(uint32_t split)
{
    need_split = split;
}

int ModuleMppDec::init()
{

   shared_ptr<ModuleMedia> productor = this->getProductor();
   if(productor != nullptr)
   {
       ImagePara outputPara;
       outputPara.width =  productor.get()->getOutputImagePara().width;
       outputPara.height = productor.get()->getOutputImagePara().height;
       outputPara.hstride = productor.get()->getOutputImagePara().hstride;
       outputPara.vstride = productor.get()->getOutputImagePara().vstride;
       outputPara.v4l2Fmt = V4L2_PIX_FMT_NV12;
       this->setOutputImagePara(outputPara);
       dec.get()->SetImgWidth(outputPara.width);
       dec.get()->SetImgHeight(outputPara.height);

       if(decode_type == DECODE_TYPE_H264)
       {
         dec.get()->SetVdecType(RK_VIDEO_ID_AVC);


       }
       else if(decode_type == DECODE_TYPE_H265)
       {
            dec.get()->SetVdecType(RK_VIDEO_ID_HEVC);
       }
       else if(decode_type == DECODE_TYPE_MJPEG)
       {
           dec.get()->SetVdecType(RK_VIDEO_ID_MJPEG);
       }
       else {
          dec.get()->SetVdecType(RK_VIDEO_ID_AutoDetect);
       }
       media_type = BUFFER_TYPE_VIDEO;
       bool ret =  dec.get()->Init();

       return (ret ? 0:-1);
   }
   return false;

}

//shared_ptr<MediaBuffer> ModuleMppDec::getBufferFromIndex(uint16_t index)
//{
////    shared_ptr<MediaBuffer> buffer = make_shared<MediaBuffer>();
////    ProduceResult ret = doProduce(buffer);
////    if(ret == PRODUCE_SUCCESS)
////    {
////        return buffer;
////    }
////    else {
////        return nullptr;
////    }
//}
