#include "module_inference.hpp"


ModuleInference::ModuleInference()
{
    rmi = new rknnModelInference();
    name = "ModuleInference";
}

ModuleInference::ModuleInference(const ImagePara &input_para)
{
    setInputImagePara(input_para);
    rmi = new rknnModelInference();
    name = "ModuleInference";
}

ModuleInference::~ModuleInference()
{
    if(rmi)
    {
        delete rmi;
        rmi = nullptr;
    }
}

void ModuleInference::setInferenceInterval(uint32_t frame_count)
{
    interval = frame_count;
}

int ModuleInference::setModelData(void *model, size_t model_size)
{
    rmi->SetModelData((char *)model);
    return 0;
}

int ModuleInference::setLabelsData(void *labels)
{
    rmi->SetLabelsData((char *)labels);
}

int ModuleInference::removeModel()
{
    rmi->DeinitYolov11Model();
    return 0;
}

void ModuleInference::setInputImageCrop(const ImageCrop &corp)
{

}

int ModuleInference::init()
{
    shared_ptr<ModuleMedia> productor =  getProductor();
    if(productor != nullptr)
    {
       ImagePara inputPara = productor->getOutputImagePara();
       this->setOutputImagePara(inputPara);
    }

    if(rmi->InitYolov11Model())
    {
        return 0;
    }
    else {
       return -1;
    }
}

ImageCrop ModuleInference::getInputImageCrop()
{

}

ImageCrop ModuleInference::getOutputImageCrop()
{

}

std::vector<rknn_tensor_mem *> *ModuleInference::getOutputMemPtr()
{

}

std::vector<rknn_tensor_attr *> *ModuleInference::getOutputAttrPtr()
{

}

std::vector<rknn_tensor_mem *> &ModuleInference::getOutputMemRef()
{

}

std::vector<rknn_tensor_attr *> &ModuleInference::getOutputAttrRef()
{

}

int ModuleInference::PostProcess(shared_ptr<MediaBuffer> imgBuffer)
{
    if(imgBuffer == nullptr)
    {
        return -1;
    }
    if(imgBuffer.get()->getData() == nullptr)
    {
       return -1;
    }
    ImagePara para = imgBuffer.get()->getImagePara();
    image_format_t fmt;
    if(para.v4l2Fmt == V4L2_PIX_FMT_NV12)
    {
        fmt = IMAGE_FORMAT_YUV420SP_NV12;
    }
    else if(para.v4l2Fmt  == V4L2_PIX_FMT_NV21)
    {
        fmt = IMAGE_FORMAT_YUV420SP_NV21;
    }
    else if(para.v4l2Fmt == V4L2_PIX_FMT_RGB24)
    {
        fmt = IMAGE_FORMAT_RGB888;
    }
    else if(para.v4l2Fmt == V4L2_PIX_FMT_RGBA32)
    {
        fmt = IMAGE_FORMAT_RGBA8888;
    }
    else {
        return -1;
    }
    if(!rmi->Post_Process(para.width,para.height,(uint8_t *)imgBuffer.get()->getData(),fmt))
    {
        return -1;
    }
    return 0;
}

ModuleMedia::ConsumeResult ModuleInference::doConsume(shared_ptr<MediaBuffer> input_buffer, shared_ptr<MediaBuffer> output_buffer)
{

      if(output_buffer->getData() == nullptr)
      {
          output_buffer->allocBuffer(input_buffer->getSize());
          memcpy(output_buffer->getData(),input_buffer->getData(),input_buffer->getSize());
      }
      output_buffer->setImagePara(input_buffer->getImagePara());

      this->setOutputImagePara(input_buffer->getImagePara());


    return CONSUME_SUCCESS;
}

void ModuleInference::reset()
{

}
