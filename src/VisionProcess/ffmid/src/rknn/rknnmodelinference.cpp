#include <stdio.h>
#include <string.h>
#include "rknnmodelinference.h"
#include "image_utils.h"
#include "file_utils.h"
#include "image_drawing.h"

rknnModelInference::rknnModelInference()
{

}

rknnModelInference::~rknnModelInference()
{

}

void rknnModelInference::SetModelData(char *model)
{
    m_ModelPath = model;
}

void rknnModelInference::SetLabelsData(char *labels)
{
    m_LabelsPath = labels;
}

bool rknnModelInference::InitYolov11Model()
{
    if(m_ModelPath == nullptr)
    {
        printf("The model data is null!\n");
        return false;
    }
    if(m_LabelsPath == nullptr)
    {
        printf("The labels data is null!\n");
        return false;
    }
    memset(&m_RknnAppCtx, 0, sizeof(rknn_app_context_t));
    if(init_post_process(m_LabelsPath) == -1)
    {
        printf("Load labels failed!\n");
        return false;
    }
    if(init_yolo11_model(m_ModelPath, &m_RknnAppCtx) == -1)
    {
        printf("Init rknn failed!\n");
        deinit_post_process();
        return false;

    }
    return true;
}

int rknnModelInference::Post_Process(int imgWidth,int imgHeight,uint8_t*videoSrc,int srcFmt)
{
    int width,height,width_stride,height_stride;

    width = imgWidth;
    height = imgHeight;
    width_stride = imgWidth;
    height_stride = imgHeight;

    image_buffer_t srcImg;
    srcImg.width = imgWidth;
    srcImg.height = imgHeight;
    srcImg.width_stride = width_stride;
    srcImg.height_stride = height_stride;
    srcImg.format = (image_format_t)srcFmt;
    srcImg.size = get_image_size(&srcImg);
    srcImg.virt_addr = videoSrc;

    object_detect_result_list od_results;

    int ret = inference_yolo11_model(&m_RknnAppCtx, &srcImg, &od_results);
    if (ret != 0)
    {
        printf("inference_yolo11_model fail! ret=%d\n", ret);
        return ret ;
    }

    // 画框和概率
    char text[256];
    for (int i = 0; i < od_results.count; i++)
    {
        object_detect_result *det_result = &(od_results.results[i]);
        printf("%s @ (%d %d %d %d) %.3f\n", coco_cls_to_name(det_result->cls_id),
               det_result->box.left, det_result->box.top,
               det_result->box.right, det_result->box.bottom,
               det_result->prop);
        int x1 = det_result->box.left;
        int y1 = det_result->box.top;
        int x2 = det_result->box.right;
        int y2 = det_result->box.bottom;

        draw_rectangle(&srcImg, x1, y1, x2 - x1, y2 - y1, COLOR_BLUE, 3);

        sprintf(text, "%s %.1f%%", coco_cls_to_name(det_result->cls_id), det_result->prop * 100);
        draw_text(&srcImg, text, x1, y1 - 20, COLOR_RED, 10);
    }
    return 0;
}

void rknnModelInference::DeinitYolov11Model()
{
    int ret = release_yolo11_model(&m_RknnAppCtx);
    if (ret != 0)
    {
        printf("release_yolov11_model fail! ret=%d\n", ret);
    }
    deinit_post_process();

}
