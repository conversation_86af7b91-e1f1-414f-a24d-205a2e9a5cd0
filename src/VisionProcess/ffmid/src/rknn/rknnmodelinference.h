#ifndef RKNNMODELINFERENCE_H
#define RKNNMODELINFERENCE_H
#include "rknpu2/yolo11.h"

class rknnModelInference
{
public:
    rknnModelInference();

    ~rknnModelInference();

    void SetModelData(char *model);

    void SetLabelsData(char *labels);

    bool InitYolov11Model();

    int Post_Process(int imgWidth,int imgHeight,uint8_t*videoSrc,int srcFmt);

    void DeinitYolov11Model();


private:
    rknn_app_context_t m_RknnAppCtx;

    char* m_ModelPath = nullptr;

    char* m_LabelsPath = nullptr;
};

#endif // RKNNMODELINFERENCE_H
