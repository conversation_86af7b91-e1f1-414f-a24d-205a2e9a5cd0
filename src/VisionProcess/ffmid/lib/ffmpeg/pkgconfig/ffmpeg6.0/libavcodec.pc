prefix=/home/<USER>/ffmpeg_build
exec_prefix=${prefix}
libdir=/home/<USER>/ffmpeg_build/lib
includedir=/home/<USER>/ffmpeg_build/include

Name: libavcodec
Description: FFmpeg codec library
Version: 60.31.102
Requires: 
Requires.private: libswresample >= 4.12.100, libavutil >= 58.29.100
Conflicts:
Libs: -L${libdir}  -lavcodec 
Libs.private: -L/home/<USER>/ffmpeg_build/lib -lvpx -lm -lpthread -L/home/<USER>/ffmpeg_build/lib -lvpx -lm -lpthread -L/home/<USER>/ffmpeg_build/lib -lvpx -lm -lpthread -L/home/<USER>/ffmpeg_build/lib -lvpx -lm -lpthread -pthread -lm -latomic -llzma -L/usr/local/lib -lfdk-aac -lm -lmp3lame -lm -L/home/<USER>/ffmpeg_build/lib -lopus -lm -lvorbis -lm -logg -lvorbisenc -lvorbis -lm -logg -L/home/<USER>/ffmpeg_build/lib -lx264 -lpthread -lm -ldl -L/home/<USER>/ffmpeg_build/lib -lx265 -lstdc++ -lm -lgcc_s -lgcc -lgcc_s -lgcc -lrt -ldl -lnuma -lz
Cflags: -I${includedir}
