prefix=/home/<USER>/ffmpeg_build
exec_prefix=${prefix}
libdir=/home/<USER>/ffmpeg_build/lib
includedir=/home/<USER>/ffmpeg_build/include

Name: libavfilter
Description: FFmpeg audio/video filtering library
Version: 9.12.100
Requires: 
Requires.private: libswscale >= 7.5.100, libpostproc >= 57.3.100, libavformat >= 60.16.100, libavcodec >= 60.31.102, libswresample >= 4.12.100, libavutil >= 58.29.100
Conflicts:
Libs: -L${libdir}  -lavfilter 
Libs.private: -pthread -lm -latomic -lass -lm -lharfbuzz -lm -lglib-2.0 -pthread -lpcre -pthread -lgraphite2 -lfontconfig -luuid -lexpat -lfribidi -lfreetype -lpng16 -lm -lz -lm -lz -lfreetype -lpng16 -lm -lz -lm -lz
Cflags: -I${includedir}
