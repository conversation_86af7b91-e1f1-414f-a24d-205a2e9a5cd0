prefix=/home/<USER>/ffmpeg_build
exec_prefix=${prefix}
libdir=/home/<USER>/ffmpeg_build/lib
includedir=/home/<USER>/ffmpeg_build/include

Name: libavfilter
Description: FFmpeg audio/video filtering library
Version: 7.57.100
Requires: 
Requires.private: libswscale >= 5.5.100, libpostproc >= 55.5.100, libavformat >= 58.29.100, libavcodec >= 58.54.100, libswresample >= 3.5.100, libavutil >= 56.31.100
Conflicts:
Libs: -L${libdir}  -lavfilter 
Libs.private: -pthread -lm -lass -lm -lharfbuzz -lm -lglib-2.0 -pthread -lpcre -pthread -lgraphite2 -lfontconfig -luuid -lexpat -lfribidi -lfreetype -lpng16 -lm -lz -lm -lz -lfreetype -lpng16 -lm -lz -lm -lz
Cflags: -I${includedir}
