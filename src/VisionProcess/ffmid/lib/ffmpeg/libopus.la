# libopus.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6 Debian-2.4.6-14
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libopus.so.0'

# Names of this library.
library_names='libopus.so.0.10.1 libopus.so.0 libopus.so'

# The name of the static archive.
old_library='libopus.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=''

# Libraries that this one depends upon.
dependency_libs=' -lm'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libopus.
current=10
age=10
revision=1

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/ffmpeg_build/lib'
