//
// Created by <PERSON><PERSON><PERSON><PERSON> on 7/16/24.
//

#ifndef SMARTTRAFFICNEXUS_VIDEOPROCESS_HPP
#define SMARTTRAFFICNEXUS_VIDEOPROCESS_HPP

#include "module/vi/module_rtspClient.hpp"
#include "module/vp/module_mppdec.hpp"
#include "module/vp/module_inference.hpp"
#include "module/vp/module_rga.hpp"
#include "typedef.hpp"
#include <iostream>
#include "VideoPostProcess.hpp"
//#include "utils/RedisClient.hpp"
#include "CRedisClient.h"
#include <spdlog/spdlog.h>

static int frame_threshold = 5;
static float iou_threshold = 0.5;
static float center_dist_threshold = 50.0;

class VideoProcess {
public:
    struct External_ctx {
        shared_ptr<ModuleMedia> module;
        std::vector<rknn_tensor_attr*> output_attrs;
        std::vector<rknn_tensor_mem*> output_mems;
        std::deque<std::vector<detect_result_t>> recent_detections;
        std::shared_ptr<VideoStreamInfo> info;
        VideoProcess* videoProcess;
    };
    explicit VideoProcess(std::shared_ptr<VideoStreamInfo> info, shared_ptr<CRedisClient> redisClient);
    bool Init();

    void StartProcess() const;
    void StopProcess() const;

    static void onInferenceCallback(void* _ctx, const shared_ptr<MediaBuffer>& buffer);
    static void onDecodeCallback(void* _ctx, const shared_ptr<MediaBuffer>& buffer);
private:
    std::shared_ptr<ModuleRtspClient> rtsp_c;
    std::shared_ptr<ModuleMppDec>dec;
    std::shared_ptr<ModuleInference>infer;
    std::shared_ptr<ModuleRga>dec_rga;
    std::shared_ptr<ModuleRga>inf_rga;
    std::shared_ptr<VideoStreamInfo> info;

    std::string video_url;
    std::string model_path;
    External_ctx ctx1;
    ImagePara input_para;
    ImagePara output_para;
    bool isInited = false;
    std::shared_ptr<CRedisClient> redis_client;

    static std::string generateId();
    std::string key;

    void Detect2Redis(const string &json) const;
    static void process_detections(External_ctx *ctx, const detect_result_group_t &detect_result_group,
                                   cv::Mat &imgRgb);
    static void count_similar_detections(External_ctx* ctx, detect_result_t& current_detection);
};

#endif //SMARTTRAFFICNEXUS_VIDEOPROCESS_HPP
