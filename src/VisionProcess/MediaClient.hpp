//
// Created by <PERSON><PERSON><PERSON><PERSON> on 7/10/24.
//

#ifndef SMARTTRAFFICNEXUS_MEDIACLIENT_HPP
#define SMARTTRAFFICNEXUS_MEDIACLIENT_HPP

#include <iostream>
#include <string>
#include <vector>
#include "typedef.hpp"
//#include "utils/ThreadPool.h"
//#include "utils/RedisClient.hpp"

class VideoProcess;

class MediaClient {
public:
    MediaClient(const std::vector<VideoStreamInfo> &vsi, const std::string &redisHost, int redisPort,
                const std::string &redisPwd);
    MediaClient() {}
    ~MediaClient();

    static void InitClient(const std::vector<VideoStreamInfo> &vsi, const std::string &redisHost, int redisPort,
                           const std::string &redisPwd);
    static void StartProcess();
    static void StopProcess();
};


#endif //SMARTTRAFFICNEXUS_MEDIACLIENT_HPP
