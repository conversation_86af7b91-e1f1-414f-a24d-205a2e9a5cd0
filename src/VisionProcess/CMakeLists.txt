# 查找OPENCV库

if(NOT (CMAKE_SYSTEM_PROCESSOR MATCHES "arm" OR CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64"))
    message(STATUS "Skipping VisionProcess project build on non-ARM platform (${CMAKE_SYSTEM_PROCESSOR})")
    return()
endif()

message(STATUS "Current Processor is ${CMAKE_SYSTEM_PROCESSOR}")
if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm" OR CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
    message(STATUS "VISION Current platform is ARM")
    set(OpenCV_DIR /opt/destdir/opencv/lib/cmake/opencv4)
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64" OR CMAKE_SYSTEM_PROCESSOR MATCHES "i686")
    message(STATUS "VISION Current platform is x86")

    set(OpenCV_DIR ${CMAKE_SYSROOT}/opt/destdir/opencv/lib/cmake/opencv4/)
else()
    message(STATUS "Current platform is neither ARM nor x86")
endif()

find_package(OpenCV 4 REQUIRED PATHS /opt/xiaolu/opencv/lib/cmake/opencv4)
message(STATUS "OpenCV_INCLUDE_DIRS: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OpenCV_LIBS: ${OpenCV_LIBS}")


# 获取当前时间
string(TIMESTAMP BUILD_TIME "%Y-%m-%d %H:%M:%S")


#configure_file(
#        ${PROJECT_SOURCE_DIR}/src/VisionProcess/vision_version.h.in
#        ${PROJECT_SOURCE_DIR}/src/VisionProcess/vision_version.h
#)


include_directories(
        ${PROJECT_SOURCE_DIR}/src
        ${PROJECT_SOURCE_DIR}/third_party/nlohmann_jsoncpp/include/
        ${PROJECT_SOURCE_DIR}/third_party/spdlog/include/
        #${PROJECT_SOURCE_DIR}/src/VisionProcess/deepsort/include/
        ${OpenCV_INCLUDE_DIRS}/
)
# 收集源文件
file(GLOB_RECURSE STNVisionProcess_SOURCES
        "${PROJECT_SOURCE_DIR}/src/VisionProcess/*.cpp"
        "${PROJECT_SOURCE_DIR}/src/VisionProcess/*.cc"
        "${PROJECT_SOURCE_DIR}/src/VisionProcess/*.c"
        "${PROJECT_SOURCE_DIR}/src/VisionProcess/*.hpp"
)
# 排除 exclude_this_dir 目录
list(FILTER STNVisionProcess_SOURCES EXCLUDE REGEX "src/VisionProcess/deepsort/.*")


# 根据主机名判断是否包含FFMedia库
execute_process(
    COMMAND hostname
    OUTPUT_VARIABLE HOST_NAME
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
message(STATUS "Current hostname is: ${HOST_NAME}")

if(HOST_NAME MATCHES "firefly")
    message(STATUS "Hostname contains 'firefly', including FFMedia library and excluding ffmid directory")
    # 排除 ffmid 目录
    list(FILTER STNVisionProcess_SOURCES EXCLUDE REGEX "src/VisionProcess/ffmid/.*")
    
    set(FFMEDIA_INCLUDES_DIR ../../third_party/ffmedia/v2.3.1/include)
    set(FFMEDIA_LIBS_DIR ../../third_party/ffmedia/v2.3.1/lib)
    set(FFMEDIA_LIBS ff_media)
    message(STATUS "FF_MEDIA_LIBRARY ${FFMEDIA_LIBS_DIR}")
    include_directories(${FFMEDIA_INCLUDES_DIR})
    link_directories(${FFMEDIA_LIBS_DIR})
    add_definitions(-DUSE_FFMEDIA)
else()
    message(STATUS "Hostname does not contain 'firefly', FFMedia library will not be included, but ffmid directory will be used")
    # 包含 ffmid 目录
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/src/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/src/Mutex/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/include/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/include/base/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/include/ffmpeg/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/include/jpeg_turbo/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/include/librga/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/include/mpi/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/include/stb_image/)
    
    # Only include RKNN-related directories on ARM platforms
    if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm" OR CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
        include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/src/rknn/)
        include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/include/rknpu2/)
    endif()
    
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/src/rkrga/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/src/rtsp/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/src/utils/)
    include_directories(${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/src/vdec/)
    
    # Add library directories for external libraries
    link_directories(
            ${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/lib/ffmpeg
            ${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/lib/mpi
            ${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/lib/librga
            ${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/lib/jpeg_turbo
    )
    
    # Only include RKNN-related library directories on ARM platforms
    if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm" OR CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
        link_directories(
            ${PROJECT_SOURCE_DIR}/src/VisionProcess/ffmid/lib/rknpu2
        )
    endif()
    
    # Add -rpath-link option to linker flags
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-rpath-link=/opt/destdir/ffmedia_jw/bin/Output")
    set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,-rpath-link=/opt/destdir/ffmedia_jw/bin/Output")
endif()

add_library(STNVisionProcess SHARED ${STNVisionProcess_SOURCES})

add_executable(STNVisionProcess_Test
    ${PROJECT_SOURCE_DIR}/src/VisionProcess/main.cpp
)

# 设置json库
set(JSON_INCLUDE_DIR ${PROJECT_SOURCE_DIR}/third_party/nlohmann_jsoncpp/include)


#set(RGA_INCLUDE_DIR ${PROJECT_SOURCE_DIR}/third_party/rga/include)
#set(RGA_LIB rga)



set(PUB_LIB_PATH "/opt/xiaolu/pubLib")
set(REDIS_ROOT "${PUB_LIB_PATH}/redis")
# 添加 Redis 头文件路径
include_directories(${REDIS_ROOT}/include)
# 添加 Redis 库文件路径
link_directories(${REDIS_ROOT}/lib ${PUB_LIB_PATH}/hiredis/lib)


# 链接第三方库
if(HOST_NAME MATCHES "firefly")
    target_include_directories(STNVisionProcess PUBLIC ${FFMEDIA_INCLUDES_DIR} ${OpenCV_INCLUDE_DIRS} ${JSON_INCLUDE_DIR})
    target_link_libraries(STNVisionProcess PRIVATE ${FFMEDIA_LIBS} ${OpenCV_LIBS} event)
    target_link_directories(STNVisionProcess PRIVATE ${REDIS_ROOT}/lib ${FFMEDIA_LIBS_DIR})

    target_include_directories(STNVisionProcess_Test PUBLIC ${FFMEDIA_INCLUDES_DIR} ${OpenCV_INCLUDE_DIRS} ${JSON_INCLUDE_DIR})
    target_link_directories(STNVisionProcess_Test PRIVATE ${REDIS_ROOT}/lib ${FFMEDIA_LIBS_DIR})
    target_link_libraries(STNVisionProcess_Test PRIVATE ${FFMEDIA_LIBS} ${OpenCV_LIBS} STNVisionProcess event RedisClient)
else()
    target_include_directories(STNVisionProcess PUBLIC ${OpenCV_INCLUDE_DIRS} ${JSON_INCLUDE_DIR})
    
    # Define platform-specific libraries
    set(COMMON_LIBS ${OpenCV_LIBS} event avutil avdevice avformat swresample avcodec rockit rga turbojpeg)
    
    # Add RKNN libraries only on ARM platforms
    if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm" OR CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
        set(PLATFORM_SPECIFIC_LIBS rknnrt)
    else()
        set(PLATFORM_SPECIFIC_LIBS "")
    endif()
    
    target_link_libraries(STNVisionProcess PRIVATE 
        ${COMMON_LIBS}
        ${PLATFORM_SPECIFIC_LIBS}
    )
    target_link_directories(STNVisionProcess PRIVATE ${REDIS_ROOT}/lib)

    target_include_directories(STNVisionProcess_Test PUBLIC ${OpenCV_INCLUDE_DIRS} ${JSON_INCLUDE_DIR})
    target_link_directories(STNVisionProcess_Test PRIVATE ${REDIS_ROOT}/lib)
    target_link_libraries(STNVisionProcess_Test PRIVATE 
        ${OpenCV_LIBS} 
        STNVisionProcess 
        event 
        RedisClient
        avutil avdevice avformat swresample avcodec
        rockit
        rga
        ${PLATFORM_SPECIFIC_LIBS}
        turbojpeg
    )
endif()