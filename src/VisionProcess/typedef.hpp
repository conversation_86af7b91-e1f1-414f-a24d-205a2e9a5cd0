//
// Created by x<PERSON>ol<PERSON> on 7/18/24.
//

#ifndef SMARTTRAFFICNEXUS_TYPEDEF_HPP
#define SMARTTRAFFICNEXUS_TYPEDEF_HPP

#include <opencv2/core/core.hpp>

struct Window {
    uint32_t x1;
    uint32_t y1;
    uint32_t x2;
    uint32_t y2;
};

struct VideoStreamInfo {
    std::string id;
    std::string localUrl;
    Window window;
    std::string CameraId;
    std::vector<cv::Point>roi;
    std::string remark;
};

#endif //SMARTTRAFFICNEXUS_TYPEDEF_HPP
