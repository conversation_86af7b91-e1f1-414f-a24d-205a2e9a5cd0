//
// Created by xia<PERSON><PERSON> on 7/10/24.
//
//#include "typedef.hpp"
#include <csignal>

#include "MediaClient.hpp"
#include "spdlog/spdlog.h"
#include <string>
//#include "json.hpp"

auto rtspServerStreamCount =  8;
int maxVideoCount = 48;
extern volatile sig_atomic_t gVisionStopFlag;

int main(const int argc, char* argv[]) {
    spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%s:%#] - %v");
    SPDLOG_INFO("hello {}!", argv[0]);
    if (argc < 2) {
        SPDLOG_ERROR("argumets less than 2, {} 'num of videos'", argv[0]);
        return -1;
    }
    std::vector<VideoStreamInfo> infos;
    VideoStreamInfo info;
    int videoCount = std::stoi(argv[1]);


    if (videoCount > maxVideoCount) {
        SPDLOG_ERROR("num of videos must less than {}.", maxVideoCount);
        return -1;
    }
    SPDLOG_INFO("num of videos {}", videoCount);
    std::string roadArray[] = {"XAL_XFL_SCW", "XAL_XFL_NCW", "XAL_XFL_WCW"};
    for (int i = 0; i < videoCount; i++) {
        int streamIndex = (i % rtspServerStreamCount) + 1;
        std::string localUrl = "rtsp://192.168.6.136:8554/cw" + std::to_string(streamIndex);
        SPDLOG_INFO("streamUrl: {}", localUrl);
        info.localUrl = localUrl;
        info.window = {613, 329, 1658, 518};
        info.roi = {{629, 336}, {624,487}, {1635,480}, {1482,331}};
        info.CameraId = "0";
        // 假设 roadArray 是一个 vector，info 是包含 id 成员的结构体
        auto arraySize = std::size(roadArray);
        SPDLOG_INFO("roadArraySize: {}", arraySize);
        if (videoCount > arraySize) {
            // 如果 videoCount 大于 roadArray 的大小，添加一个序号
            // 根据 roadArray 的大小来取模
            size_t index = i % arraySize;
            info.id = roadArray[index] + "_" + std::to_string(i / arraySize);  // 加上一个序号
            SPDLOG_INFO("info.id.1: {}", info.id);
        } else {
            // 正常情况下，按 videoCount 取模
            info.id = roadArray[i % videoCount];
            SPDLOG_INFO("info.id.2: {}", info.id);
        }
        infos.emplace_back(info);
    }
    MediaClient::InitClient(infos, "localhost", 6379, "jyg2021");
    //MediaClient client(infos, "**************", 8389, "cg@1q2w3e");
    MediaClient::StartProcess();

    while (!gVisionStopFlag) {
        sleep(1);
    }

    MediaClient::StopProcess();
    sleep(3);
    return 0;
}