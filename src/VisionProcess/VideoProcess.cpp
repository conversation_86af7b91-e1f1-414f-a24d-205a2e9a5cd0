//
// Created by x<PERSON><PERSON><PERSON> on 7/16/24.
//

#include "VideoProcess.hpp"

#include <csignal>

#include "VideoPostProcess.hpp"
#include <utility>
#include <iostream>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgproc.hpp>
#include <spdlog/spdlog.h>

// 全局或者静态的标志位，用来检测 SIGINT 信号
extern volatile sig_atomic_t gVisionStopFlag;


// 处理 SIGINT 信号的函数
void handleSignal(int signal) {
    if (signal == SIGINT) {
        SPDLOG_INFO("Received Ctrl+C signal, shutting down...");
        gVisionStopFlag = 1;  // 设置标志位，通知程序退出
    }
}


void setupSignalHandler() {
    // 创建 sigaction 结构体
    struct sigaction sa{};

    // 将 sa 中的所有字段置为 0
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;  // 默认行为，表示处理信号时不做额外处理
    sa.sa_handler = handleSignal;  // 设置信号处理函数

    // 注册 SIGINT 信号的处理程序
    if (sigaction(SIGINT, &sa, nullptr) == -1) {
        SPDLOG_ERROR("Error setting up signal handler");
        throw std::runtime_error("Failed to set up SIGINT handler.");
    }
}



VideoProcess::VideoProcess(std::shared_ptr<VideoStreamInfo> info, shared_ptr<CRedisClient> redisClient)
    : info(std::move(info)),
      video_url(this->info->localUrl),
      model_path("./model/RK3588/yolov5s-640-640.rknn"), ctx1(),
      //model_path("./model/yolo11.rknn"), ctx1(),
      isInited(false),
      redis_client(std::move(redisClient)),
      key("recognition[" + this->info->id + "]") {
    if (redis_client != nullptr) {
        redis_client->selectDB(2);
        redis_client->del(key);
    } else {
        throw std::runtime_error("RedisClient is nullptr!");
    }
}

void VideoProcess::StartProcess() const {
    if (!isInited) {
        SPDLOG_ERROR("Init failed\n");
        return;
    }

    // 注册 SIGINT 处理函数
    setupSignalHandler();

    try {
        this->rtsp_c->start();
        // 循环等待直到检测到 SIGINT 信号
        while (!gVisionStopFlag) {
            sleep(1);
        }
        StopProcess();
    } catch (const std::exception &e) {
        SPDLOG_ERROR("Failed to start RTSP client: {}", e.what());
        throw std::runtime_error("StartProcess failed.");
    }

}

void VideoProcess::StopProcess() const {
    if (this->rtsp_c != nullptr) {
        this->rtsp_c->stop();
        SPDLOG_INFO("Stopping RTSP client...");
    }
}

bool VideoProcess::Init() {
    //初始化rtsp client
    char* char_url = const_cast<char*>(this->video_url.c_str());
    rtsp_c = std::make_shared<ModuleRtspClient>(char_url, RTSP_STREAM_TYPE_TCP);
    if (rtsp_c->init() < 0) {
        SPDLOG_ERROR("rtsp client init failed\n");
        isInited = false;
        return false;
    }
    //初始化decoder
    dec = make_shared<ModuleMppDec>();
    dec->setProductor(rtsp_c);
    //dec->setOutputDataCallback(this, VideoProcess::callback_dec);
    if (dec->init() < 0) {
        SPDLOG_ERROR("dec init failed\n");
        isInited = false;
        return false;
    }
    //初始化裁切操作

    /*
     *XXX do not need.
    ImagePara dec_input_para = this->dec->getOutputImagePara();
    dec_input_para.height = info->window.h;
    dec_input_para.width = info->window.w;
    dec_input_para.hstride = info->window.w;
    dec_input_para.vstride = info->window.h;
    this->dec_rga = make_shared<ModuleRga>(dec_input_para, RGA_ROTATE_NONE);
    this->dec_rga->setDstPara(dec_input_para.v4l2Fmt, \
                              info->window.x, info->window.y, info->window.w, info->window.h, \
                              info->window.w, info->window.h);
    this->dec_rga->setProductor(dec);
    this->dec_rga->setOutputDataCallback(nullptr, VideoProcess::callback_dec_rga);
    if (this->dec_rga->init() < 0) {
        ff_error("dec rga init failed\n");
        isInited = false;
        return false;
    }
    */

    //初始化inference
    infer = make_shared<ModuleInference>();
    infer->setProductor(dec);
    //infer->setInferenceInterval(6);
    if (infer->setModelData(const_cast<char *>(this->model_path.c_str()), 0) < 0) {
        SPDLOG_ERROR("infer set model failed\n");
        isInited = false;
        return false;
    }
#if 0
    std::string label_path = "./model/label_cw.txt";
    if (infer->setLabelsData(const_cast<char *>(label_path.c_str())) < 0) {
        SPDLOG_ERROR("infer set model label failed\n");
        isInited = false;
        return false;
    }
#endif

    /*
    uint32_t x1, y1, x2, y2 = 0;
    mapPoint1080pTo640(info->window.x1, info->window.y1, x1, y1);
    mapPoint1080pTo640(info->window.x2, info->window.y2, x2, y2);
    auto imageCrop = ImageCrop{x1, y1, x2-x1, y2-y1};
    */

    auto imageCrop = ImageCrop{info->window.x1, info->window.y1, \
    info->window.x2-info->window.x1, info->window.y2-info->window.y1};

    infer->setInputImageCrop(imageCrop);
    if (infer->init() < 0) {
        SPDLOG_ERROR("infer init failed\n");
        isInited = false;
        return false;
    }

    //初始化推理ctx
    ctx1.module = infer;
    ctx1.output_attrs = infer->getOutputAttrRef();
    ctx1.output_mems = infer->getOutputMemRef();
    ctx1.info = info;
    ctx1.videoProcess = this;
    infer->setOutputDataCallback(&ctx1, VideoProcess::onInferenceCallback);
    if (infer->init() < 0) {
        SPDLOG_ERROR("infer init failed\n");
        isInited = false;
        return false;
    }
    isInited = true;
    return true;
}

void VideoProcess::onInferenceCallback(void *_ctx, const shared_ptr<MediaBuffer>& buffer) {

    auto* ctx = static_cast<External_ctx*>(_ctx);
    const shared_ptr<VideoBuffer> buf = static_pointer_cast<VideoBuffer>(buffer);

    if (void* ptr = buf->getActiveData(); ptr != nullptr) {
        const uint32_t width = buf->getImagePara().hstride;
        const uint32_t height = buf->getImagePara().vstride;
        cv::Mat imgRgb(cv::Size(static_cast<int>(width), static_cast<int>(height)), CV_8UC3, ptr);

        /*
        ff_error("imgRgb size %d, %d\n", width, height);
        std::string file_name = "./output/raw_" + std::to_string(num_jpg++) + ".jpg";
        bool success = cv::imwrite(file_name, imgRgb);
        if (!success) {
            ff_error("cv write file %s failed\n", file_name.c_str());
        } else {
            ff_error("cv write file %s success!\n", file_name.c_str());
        }
        */

        detect_result_group_t detect_result_group;
        std::vector<float> out_scales;
        std::vector<int32_t> out_zps;

        for (const auto &output_attr: ctx->output_attrs) {
            out_scales.push_back(output_attr->scale);
            out_zps.push_back(output_attr->zp);
        }

        post_process(static_cast<int8_t *>(ctx->output_mems[0]->virt_addr),
                     static_cast<int8_t *>(ctx->output_mems[1]->virt_addr),
                     static_cast<int8_t *>(ctx->output_mems[2]->virt_addr),
                     static_cast<int>(height), static_cast<int>(width),
                     BOX_THRESH, NMS_THRESH,
                     1, 1, out_zps, out_scales,
                     &detect_result_group);

        if (detect_result_group.count > 0) {
            //buf->flushDrmBuf();
            process_detections(ctx, detect_result_group, imgRgb);
            /*
            ff_error("-----------------\n");
            char text[256];
            for (int i = 0; i < detect_result_group.count; i++) {
                detect_result_t *det_result = &(detect_result_group.results[i]);
                sprintf(text, "%s %.1f%%", det_result->name, det_result->prop * 100);
                int x1 = det_result->box.left;
                int y1 = det_result->box.top;
                int x2 = det_result->box.right;
                int y2 = det_result->box.bottom;
                //ff_error("%s found, [%d, %d, %d, %d]\n", det_result->name, x1, y1, x2, y2);
                rectangle(imgRgb, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(255, 0, \
                0, 255), 3);
                putText(imgRgb, text, cv::Point(x1, y1 + 12),  cv::FONT_HERSHEY_SIMPLEX, 0.5, \
                cv::Scalar(0, 0, 0), 1);
                std::string file_name = "./output/test_" + std::to_string(num_jpg++) + ".jpg";
                bool success = cv::imwrite(file_name, imgRgb);
                if (!success) {
                    ff_error("cv write file %s failed\n", file_name.c_str());
                } else {
                    ff_error("cv write file %s success!\n", file_name.c_str());
                }
            }
            */
        }
    } else {
        SPDLOG_ERROR("ptr is null\n");
    }
}

void VideoProcess::onDecodeCallback(void *_ctx, const shared_ptr<MediaBuffer> &buffer) {
    static int num_jpg = 0;
    auto* ctx = static_cast<VideoProcess*>(_ctx);
    shared_ptr<VideoBuffer> buf = static_pointer_cast<VideoBuffer>(buffer);
    void* ptr = buf->getActiveData();
    if (ptr != nullptr) {
        uint32_t width = buf->getImagePara().hstride;
        uint32_t height = buf->getImagePara().vstride;
        //buf->invalidateDrmBuf();
        cv::Mat yuv(height + height / 2, width, CV_8UC1, ptr);
        //cv::Mat bgr(height, width, CV_8UC3);
        cv::Mat mat(cv::Size(width, height), CV_8UC3);
        // 将NV12转换为BGR
        cv::cvtColor(yuv, mat, cv::COLOR_YUV2BGR_NV12);

        std::vector<std::vector<cv::Point>> contours;
        contours.push_back(ctx->info->roi);
        cv::polylines(mat, contours, true, cv::Scalar(0, 255, 0), 2); // 用绿色绘制多边形
        std::string file_name = "./output/dec_" + std::to_string(num_jpg++) + ".jpg";
        bool success = cv::imwrite(file_name, mat);
        if (!success) {
            ff_error("cv write file %s failed\n", file_name.c_str());
        } else {
            ff_error("cv write file %s success!\n", file_name.c_str());
        }
    } else {
        ff_error("%s called, ptr is null\n", __FUNCTION__);
    }
}

void VideoProcess::process_detections(External_ctx *ctx, const detect_result_group_t &detect_result_group,
                                      cv::Mat &imgRgb) {

    std::vector<detect_result_t> current_detections(detect_result_group.results, detect_result_group.results + \
                                                    detect_result_group.count);

    ctx->recent_detections.push_back(current_detections);

    if (ctx->recent_detections.size() > frame_threshold) {
        ctx->recent_detections.pop_front();
    }

    if (ctx->recent_detections.size() == frame_threshold) {

        for (auto &current_detection: ctx->recent_detections.back()) {
            count_similar_detections(ctx, current_detection);
        }

        std::string json_str = detections2json(ctx->recent_detections.back(), ctx->info->CameraId);
        if (!json_str.empty()) {
            ctx->videoProcess->Detect2Redis(json_str);
        }
    }
}

void VideoProcess::count_similar_detections(External_ctx *ctx, detect_result_t &current_detection) {

    // 排除队尾最后一个元素剩余的识别结果
    std::vector<std::vector<detect_result_t> > left_detections(ctx->recent_detections.begin(),
                                                               ctx->recent_detections.end() - 2);

    for (const auto& rcent_detections: left_detections) {
        for (const auto& past_detection: rcent_detections) {
            if (current_detection.name != "" && past_detection.name != "" && std::string(current_detection.name) ==
            "person" && strcmp( current_detection.name, past_detection.name) == 0) {
                auto iou = IoU(current_detection.box, past_detection.box);
                auto cDis = center_distance(current_detection.box, past_detection.box);
                if (iou > iou_threshold || cDis < center_dist_threshold) {
                    current_detection.count++;
                    if (current_detection.count >= frame_threshold) {
                        return;
                    }
                }
            }
        }
    }
}

void VideoProcess::Detect2Redis(const std::string& json) const {
    //std::string id = generateId();
    redis_client->xadd_withtrim(key, "*", "data", json);
}


std::string VideoProcess::generateId() {
    // 获取当前时间戳
    auto timestamp = get_timestamp();

    // 获取当前序列号
    //std::string sequenceKey = "sequence:" + std::to_string(timestamp);
    //int sequence =  redis_client->incr(sequenceKey);

    // 生成 ID
    std::stringstream ss;
    ss << timestamp;
    return ss.str();
}