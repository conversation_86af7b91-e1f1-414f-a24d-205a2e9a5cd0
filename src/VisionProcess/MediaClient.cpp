//
// Created by xia<PERSON><PERSON> on 7/10/24.
//

#include "MediaClient.hpp"
#include "module/vi/module_rtspClient.hpp"
#include "utils/ThreadPool.h"
//#include "utils/RedisClient.hpp"
#include "CRedisClient.h"
#include "VideoProcess.hpp"
#include <algorithm>
#include <csignal>
#include "spdlog/spdlog.h"
#include "utils/version_info.hpp"

std::vector<std::shared_ptr<VideoProcess>> videoProcesses;
ThreadPool *threadPool;
std::shared_ptr<CRedisClient> redisClient;
// 全局或者静态的标志位，用来检测 SIGINT 信号
volatile sig_atomic_t gVisionStopFlag = 0;

void MediaClient::InitClient(const vector<VideoStreamInfo>& vsi, const std::string& redisHost, int redisPort, const std::string& redisPwd) {
    spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%s:%#] %v");
    SPDLOG_INFO("Vision Library Build Info: \n{}", version::getVersionInfo());
    try {
        //init redis client
        SPDLOG_INFO("RedisClient initialize for host. {}:{}:{}", redisHost, redisPort, redisPwd);
        //redisClient = std::make_shared<RedisClient>(redisHost, redisPort, redisPwd);
        redisClient = std::make_shared<CRedisClient>(redisHost, redisPort, redisPwd);

        for (auto &videoStream: vsi) {
            videoProcesses.emplace_back(
                    make_shared<VideoProcess>(make_shared<VideoStreamInfo>(videoStream), redisClient));
        }

        size_t hardware_concurrency = std::thread::hardware_concurrency();
        if (hardware_concurrency == 0 ) {
            hardware_concurrency = 4;
        }
        size_t threadNum = std::min(vsi.size(), hardware_concurrency);
        if (vsi.size() > threadNum) {
            threadNum = vsi.size();
        }
        SPDLOG_INFO("Thread num {}", threadNum);
        //threadPool = std::make_shared<ThreadPool>(threadNum);
        threadPool = new ThreadPool(threadNum);
    } catch (const std::exception& e){
        SPDLOG_ERROR("MediaClient create failed. {}\n", e.what());
    }
}

MediaClient::~MediaClient() {
    StopProcess();
    delete threadPool;
}


void MediaClient::StartProcess() {
    for (auto &videoProcess: videoProcesses) {
        if (videoProcess->Init()) {
            threadPool->enqueue(&VideoProcess::StartProcess, videoProcess);
        } else {
            SPDLOG_ERROR("Video Process init failed.");
        }
    }
}

void MediaClient::StopProcess() {
    gVisionStopFlag = 1;
#if 0
    for (auto &videoProcess: videoProcesses) {
        if (videoProcess->Init()) {
            videoProcess->StopProcess();
        } else {
            SPDLOG_ERROR("Video Process stop failed.");
        }
    }
#endif
}


MediaClient::MediaClient(const vector<VideoStreamInfo>& vsi, const std::string& redisHost, int redisPort, const std::string& redisPwd) {
    InitClient(vsi, redisHost, redisPort, redisPwd);
}
