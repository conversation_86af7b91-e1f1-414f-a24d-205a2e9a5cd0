//
// Created by x<PERSON><PERSON><PERSON> on 7/10/24.
//

#include <cmath>
#include <cstdint>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <sys/time.h>

#include <set>
#include <vector>
#include "VideoPostProcess.hpp"

#include <sys/stat.h>

#include "json.hpp"
#include "VideoProcess.hpp"

#define LABEL_NALE_TXT_PATH "./model/coco_80_labels_list.txt"
//#define LABEL_NALE_TXT_PATH "./model/label_cw.txt"

static char* labels[OBJ_CLASS_NUM];

const int anchor0[6] = {10, 13, 16, 30, 33, 23};
const int anchor1[6] = {30, 61, 62, 45, 59, 119};
const int anchor2[6] = {116, 90, 156, 198, 373, 326};

inline static int clamp(const float val, const int min, const int max) {
    return static_cast<int>(val > static_cast<float>(min)
                                ? (val < static_cast<float>(max) ? val : static_cast<float>(max))
                                : static_cast<float>(min));
}

char* readLine(FILE* fp, char* buffer, int* len)
{
    int    ch;
    int    i        = 0;
    size_t buff_len = 0;

    buffer = static_cast<char *>(malloc(buff_len + 1));
    if (!buffer)
        return nullptr; // Out of memory

    while ((ch = fgetc(fp)) != '\n' && ch != EOF) {
        buff_len++;
        void* tmp = realloc(buffer, buff_len + 1);
        if (tmp == nullptr) {
            free(buffer);
            return nullptr; // Out of memory
        }
        buffer = static_cast<char *>(tmp);

        buffer[i] = static_cast<char>(ch);
        i++;
    }
    buffer[i] = '\0';

    *len = static_cast<int>(buff_len);

    // Detect end
    if (ch == EOF && (i == 0 || ferror(fp))) {
        free(buffer);
        return nullptr;
    }
    return buffer;
}

int readLines(const char* fileName, char* lines[], int max_line)
{
    FILE* file = fopen(fileName, "r");
    char* s = nullptr;
    int   i = 0;
    int   n = 0;

    if (file == nullptr) {
        printf("Open %s fail!\n", fileName);
        return -1;
    }

    while ((s = readLine(file, s, &n)) != nullptr) {
        lines[i++] = s;
        if (i >= max_line)
            break;
    }
    fclose(file);
    return i;
}

int loadLabelName(const char* locationFilename, char* label[])
{
    printf("loadLabelName %s\n", locationFilename);

    if (-1 == readLines(locationFilename, label, OBJ_CLASS_NUM))
        return -1;

    return 0;
}

static float CalculateOverlap(float xmin0, float ymin0, float xmax0, float ymax0, float xmin1, float ymin1, float xmax1,
                              float ymax1)
{
    float w = fmax(0.f, fmin(xmax0, xmax1) - fmax(xmin0, xmin1) + 1.0);
    float h = fmax(0.f, fmin(ymax0, ymax1) - fmax(ymin0, ymin1) + 1.0);
    float i = w * h;
    float u = (xmax0 - xmin0 + 1.0) * (ymax0 - ymin0 + 1.0) + (xmax1 - xmin1 + 1.0) * (ymax1 - ymin1 + 1.0) - i;
    return u <= 0.f ? 0.f : (i / u);
}

static int nms(const int validCount, const std::vector<float> &outputLocations, const std::vector<int> &classIds,
               std::vector<int> &order, const int filterId, const float threshold)
{
    for (int i = 0; i < validCount; ++i) {
        if (order[i] == -1 || classIds[i] != filterId) {
            continue;
        }
        int n = order[i];
        for (int j = i + 1; j < validCount; ++j) {
            int m = order[j];
            if (m == -1 || classIds[i] != filterId) {
                continue;
            }
            float xmin0 = outputLocations[n * 4 + 0];
            float ymin0 = outputLocations[n * 4 + 1];
            float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
            float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];

            float xmin1 = outputLocations[m * 4 + 0];
            float ymin1 = outputLocations[m * 4 + 1];
            float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
            float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];

            if (float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1); iou > threshold) {
                order[j] = -1;
            }
        }
    }
    return 0;
}

static int quick_sort_indice_inverse(std::vector<float>& input, const int left, const int right, std::vector<int>& indices)
{
    int   low  = left;
    int   high = right;
    if (left < right) {
        int key_index = indices[left];
        float key = input[left];
        while (low < high) {
            while (low < high && input[high] <= key) {
                high--;
            }
            input[low]   = input[high];
            indices[low] = indices[high];
            while (low < high && input[low] >= key) {
                low++;
            }
            input[high]   = input[low];
            indices[high] = indices[low];
        }
        input[low]   = key;
        indices[low] = key_index;
        quick_sort_indice_inverse(input, left, low - 1, indices);
        quick_sort_indice_inverse(input, low + 1, right, indices);
    }
    return low;
}

static float sigmoid(const float x) { return 1.0 / (1.0 + expf(-x)); }

static float unsigmoid(const float y) { return static_cast<float>(-1.0 * logf((1.0 / y) - 1.0)); }

inline static int32_t clip(const float val, const float min, const float max)
{
    const float f = val <= min ? min : (val >= max ? max : val);
    return static_cast<int32_t>(f);
}

static int8_t qnt_f32_to_affine(const float f32, const int32_t zp, const float scale)
{
    const float  dst_val = (f32 / scale) + static_cast<float>(zp);
    const auto res     = static_cast<int8_t>(clip(dst_val, -128, 127));
    return res;
}

static float deqnt_affine_to_f32(const int8_t qnt, const int32_t zp, const float scale) {
    return (static_cast<float>(qnt) - static_cast<float>(zp)) * scale;
}

static int process(int8_t *input, const int *anchor, const int grid_h, const int grid_w, const int height,
                   const int width, const int stride,
                   std::vector<float> &boxes, std::vector<float> &objProbs, std::vector<int> &classId,
                   const float threshold,
                   const int32_t zp, const float scale)
{
    int    validCount = 0;
    const int    grid_len   = grid_h * grid_w;
    const float  thres      = unsigmoid(threshold);
    const int8_t thres_i8 = qnt_f32_to_affine(thres, zp, scale);

    for (int a = 0; a < 3; a++) {
        for (int i = 0; i < grid_h; i++) {
            for (int j = 0; j < grid_w; j++) {
                int8_t box_confidence = input[(PROP_BOX_SIZE * a + 4) * grid_len + i * grid_w + j];
                if (box_confidence >= thres_i8) {
                    int     offset = (PROP_BOX_SIZE * a) * grid_len + i * grid_w + j;
                    int8_t* in_ptr = input + offset;
                    auto   box_x  = static_cast<float>(sigmoid(deqnt_affine_to_f32(*in_ptr, zp, scale)) * 2.0 - 0.5);
                    float   box_y  = sigmoid(deqnt_affine_to_f32(in_ptr[grid_len], zp, scale)) * 2.0 - 0.5;
                    float   box_w  = sigmoid(deqnt_affine_to_f32(in_ptr[2 * grid_len], zp, scale)) * 2.0;
                    float   box_h  = sigmoid(deqnt_affine_to_f32(in_ptr[3 * grid_len], zp, scale)) * 2.0;
                    box_x          = (box_x + j) * (float)stride;
                    box_y          = (box_y + i) * (float)stride;
                    box_w          = box_w * box_w * (float)anchor[a * 2];
                    box_h          = box_h * box_h * (float)anchor[a * 2 + 1];
                    box_x -= (box_w / 2.0);
                    box_y -= (box_h / 2.0);

                    int8_t maxClassProbs = in_ptr[5 * grid_len];
                    int    maxClassId    = 0;

                    // XXX k=0 or k=1?
                    for (int k = 1; k < OBJ_CLASS_NUM; ++k) {
                        int8_t prob = in_ptr[(5 + k) * grid_len];
                        if (prob > maxClassProbs) {
                            maxClassId    = k;
                            maxClassProbs = prob;
                        }
                    }
                    if (maxClassProbs>thres_i8){
                        objProbs.push_back(
                            sigmoid(deqnt_affine_to_f32(maxClassProbs, zp, scale)) * sigmoid(
                                deqnt_affine_to_f32(box_confidence, zp, scale)
                            )
                        );
                        classId.push_back(maxClassId);
                        validCount++;
                        boxes.push_back(box_x);
                        boxes.push_back(box_y);
                        boxes.push_back(box_w);
                        boxes.push_back(box_h);
                    }
                }
            }
        }
    }
    return validCount;
}

int post_process(int8_t *input0, int8_t *input1, int8_t *input2, int model_in_h, int model_in_w, float conf_threshold,
                 float nms_threshold, float scale_w, float scale_h, std::vector<int32_t> &qnt_zps,
                 std::vector<float> &qnt_scales, detect_result_group_t *group)
{
    static int init = -1;
    if (init == -1) {
        int ret = 0;
        ret     = loadLabelName(LABEL_NALE_TXT_PATH, labels);
        if (ret < 0) {
            return -1;
        }

        init = 0;
    }
    memset(group, 0, sizeof(detect_result_group_t));

    std::vector<float> filterBoxes;
    std::vector<float> objProbs;
    std::vector<int>   classId;

    // stride 8
    int stride0     = 8;
    int grid_h0     = model_in_h / stride0;
    int grid_w0     = model_in_w / stride0;
    int validCount0 = 0;
    validCount0 = process(input0, (int *) anchor0, grid_h0, grid_w0, model_in_h, model_in_w, stride0,
                          filterBoxes, objProbs, classId, conf_threshold, qnt_zps[0], qnt_scales[0]);

    // stride 16
    int stride1     = 16;
    int grid_h1     = model_in_h / stride1;
    int grid_w1     = model_in_w / stride1;
    int validCount1 = 0;
    validCount1 = process(input1, (int*)anchor1, grid_h1, grid_w1, model_in_h, model_in_w, stride1,
                          filterBoxes, objProbs, classId, conf_threshold, qnt_zps[1], qnt_scales[1]);

    // stride 32
    int stride2     = 32;
    int grid_h2     = model_in_h / stride2;
    int grid_w2     = model_in_w / stride2;
    int validCount2 = 0;
    validCount2 = process(input2, (int*)anchor2, grid_h2, grid_w2, model_in_h, model_in_w, stride2,
                          filterBoxes, objProbs, classId, conf_threshold, qnt_zps[2], qnt_scales[2]);

    int validCount = validCount0 + validCount1 + validCount2;
    // no object detect
    if (validCount <= 0) {
        return 0;
    }

    std::vector<int> indexArray;
    for (int i = 0; i < validCount; ++i) {
        indexArray.push_back(i);
    }

    quick_sort_indice_inverse(objProbs, 0, validCount - 1, indexArray);

    std::set<int> class_set(std::begin(classId), std::end(classId));

    for (auto c : class_set) {
        nms(validCount, filterBoxes, classId, indexArray, c, nms_threshold);
    }

    int last_count = 0;
    group->count   = 0;
    /* box valid detect target */
    for (int i = 0; i < validCount; ++i) {
        if (indexArray[i] == -1 || last_count >= OBJ_NUMB_MAX_SIZE) {
            continue;
        }
        int n = indexArray[i];

        float x1       = filterBoxes[n * 4 + 0];
        float y1       = filterBoxes[n * 4 + 1];
        float x2       = x1 + filterBoxes[n * 4 + 2];
        float y2       = y1 + filterBoxes[n * 4 + 3];
        int   id       = classId[n];
        float obj_conf = objProbs[i];

        group->results[last_count].box.left   = (int)(clamp(x1, 0, model_in_w) / scale_w);
        group->results[last_count].box.top    = (int)(clamp(y1, 0, model_in_h) / scale_h);
        group->results[last_count].box.right  = (int)(clamp(x2, 0, model_in_w) / scale_w);
        group->results[last_count].box.bottom = (int)(clamp(y2, 0, model_in_h) / scale_h);
        group->results[last_count].prop       = obj_conf;
        group->results[last_count].count      = 1;
        char* label                           = labels[id];
        strncpy(group->results[last_count].name, label, OBJ_NAME_MAX_SIZE);

        // printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left,
        // group->results[last_count].box.top,
        //        group->results[last_count].box.right, group->results[last_count].box.bottom, label);
        last_count++;
    }
    group->count = last_count;

    return 0;
}

void deinitPostProcess()
{
    for (int i = 0; i < OBJ_CLASS_NUM; i++) {
        if (labels[i] != nullptr) {
            free(labels[i]);
            labels[i] = nullptr;
        }
    }
}

void mapPoint1080pTo640(uint32_t x_original, uint32_t y_original, uint32_t &x_new, uint32_t &y_new)
{
    float scale_x = 640.0f / 1920.0f;
    float scale_y = 360.0f / 1080.0f;

    float x_new_float = static_cast<float>(x_original) * scale_x;
    float y_new_float = static_cast<float>(y_original) * scale_y;

    x_new = static_cast<uint32_t>(x_new_float);
    y_new = static_cast<uint32_t>(y_new_float);
}

float IoU(const BOX_RECT& box1, const BOX_RECT& box2) {

    int x1 = std::max(box1.left, box2.left);
    int y1 = std::max(box1.top, box2.top);
    int x2 = std::min(box1.right, box2.right);
    int y2 = std::min(box1.bottom, box2.bottom);

    int interArea = std::max(0, x2 - x1) * std::max(0, y2 - y1);
    int box1Area = (box1.right - box1.left) * (box1.bottom - box1.top);
    int box2Area = (box2.right - box2.left) * (box2.bottom - box2.top);

    float iou = static_cast<float>(interArea) / static_cast<float>(box1Area + box2Area - interArea);

    return iou;
}


// 计算边界框的中心点距离
float center_distance(const BOX_RECT& box1, const BOX_RECT& box2) {
    auto center_x1 = static_cast<float>((box1.left + box1.right) / 2.0);
    auto center_y1 = static_cast<float>((box1.top + box1.bottom) / 2.0);
    auto center_x2 = static_cast<float>((box2.left + box2.right) / 2.0);
    auto center_y2 = static_cast<float>((box2.top + box2.bottom) / 2.0);

    return static_cast<float>(
        std::sqrt(std::pow(center_x1 - center_x2, 2) + std::pow(center_y1 - center_y2, 2)));
}

// 判断点是否在多边形内
bool is_point_in_polygon(const cv::Point& point, const std::vector<cv::Point>& polygon) {
    return cv::pointPolygonTest(polygon, point, false) >= 0;
}

// 判断检测结果是否在ROI内
bool is_detection_in_roi(const cv::Point& detection_center, const std::vector<cv::Point>& roi) {
    return is_point_in_polygon(detection_center, roi);
}

// 计算检测结果的中心点，并进行坐标映射
cv::Point get_center_point_and_scale(const BOX_RECT&box, float width_scale, float height_scale) {
    int center_x = static_cast<int>((box.left + box.right) / 2.0 * width_scale);
    int center_y = static_cast<int>((box.top + box.bottom) / 2.0 * height_scale);
    return {center_x, center_y};
}

// 绘制 ROI 和检测结果的函数
void draw_roi_and_detections(cv::Mat &image, const std::vector<cv::Point> &roi_1080p, const BOX_RECT& detection,
                             float width_scale, float height_scale) {
    // 缩放后的ROI区域
    /*
    std::vector<cv::Point> scaled_roi;
    for (const auto &point : roi) {
        int scaled_x = static_cast<int>(point.x / width_scale);
        int scaled_y = static_cast<int>(point.y / height_scale);
        scaled_roi.push_back(cv::Point(scaled_x, scaled_y));
    }
     */

    // 将 cv::Point 类型的 roi_1080p 转换为 cv::Point2f
    std::vector<cv::Point2f> roi_1080p_2f;
    for (const auto& point : roi_1080p) {
        roi_1080p_2f.emplace_back(static_cast<float>(point.x), static_cast<float>(point.y));

    }

    // 定义目标640x640坐标系中的映射区域（对应的顶点）
    std::vector<cv::Point2f> roi_640x640 = {
            cv::Point2f(static_cast<float>(roi_1080p[0].x * 640.0 / 1920.0),
                        static_cast<float>(roi_1080p[0].y * 640.0 / 1080.0)),
            cv::Point2f(static_cast<float>(roi_1080p[1].x * 640.0 / 1920.0),
                        static_cast<float>(roi_1080p[1].y * 640.0 / 1080.0)),
            cv::Point2f(static_cast<float>(roi_1080p[2].x * 640.0 / 1920.0),
                        static_cast<float>(roi_1080p[2].y * 640.0 / 1080.0)),
            cv::Point2f(static_cast<float>(roi_1080p[3].x * 640.0 / 1920.0),
                        static_cast<float>(roi_1080p[3].y * 640.0 / 1080.0)),
    };

    // 计算投影变换矩阵
    cv::Mat transform_matrix = cv::getPerspectiveTransform(roi_1080p_2f, roi_640x640);

    // 应用投影变换，将1080p的ROI转换到640x640的坐标系
    std::vector<cv::Point2f> scaled_roi;
    cv::perspectiveTransform(roi_1080p_2f, scaled_roi, transform_matrix);

    // 绘制ROI区域
    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Point> roi_points(scaled_roi.begin(), scaled_roi.end());
    contours.push_back(roi_points);
    cv::polylines(image, contours, true, cv::Scalar(0, 255, 0), 2); // 用绿色绘制多边形

    // 绘制检测结果
    // 直接使用640x640的坐标系绘制矩形框
    cv::Rect rect(detection.left, detection.top, detection.right - detection.left, detection.bottom - detection.top);
    // 绘制矩形框
    cv::rectangle(image, rect, cv::Scalar(0, 0, 255), 2); // 用红色绘制检测框

    /*sprintf(text, "%s %.1f%%", current_detection.name, current_detection.prop * 100);
    int x1 = current_detection.box.left;
    int y1 = current_detection.box.top;
    int x2 = current_detection.box.right;
    int y2 = current_detection.box.bottom;
    ff_error("detect %s at [%d, %d, %d, %d] with prop %f\n", current_detection.name, current_detection.box.left,
             current_detection.box.top, current_detection.box.right, current_detection.box.bottom, current_detection.prop);
    rectangle(imgRgb, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(255, 0, \
                 0, 255), 3);
    putText(imgRgb, text, cv::Point(x1, y1 + 12),  cv::FONT_HERSHEY_SIMPLEX, 0.5, \
                 cv::Scalar(0, 0, 0), 1);*/
}

// 获取当前时间戳的浮点数表示
double get_timestamp() {
    // 获取当前时间点
    auto now = std::chrono::system_clock::now();

    // 获取自 epoch（1970-01-01 00:00:00 UTC）以来的时间点数
    auto duration = now.time_since_epoch();

    // 毫秒转换为秒为单位的浮点数
    auto timestamp = static_cast<double>(
                         std::chrono::duration_cast<std::chrono::milliseconds>(duration).count()) / 1000.0;
    return timestamp;
}



std::string detections2json(const std::vector<detect_result_t> &detections, const std::string& camIdx){
    // 创建一个 JSON 对象
    nlohmann::json root;

    // 获取当前时间戳
    auto timestamp = get_timestamp();

    // 创建一个存放人员检测信息的数组
    nlohmann::json persons = nlohmann::json::array();

    // 遍历检测结果
    for (const auto& detection : detections) {
        if (detection.count >= frame_threshold) {
            nlohmann::json person;
            person["id"] = static_cast<int>(&detection - &detections[0]);
            person["camIdx"] = camIdx;
            person["pos"] = {detection.box.left, detection.box.top, detection.box.right,
                             detection.box.bottom};
            // 将每个人的信息添加到数组中
            persons.push_back(person);
        }
    }

    if (persons.empty()) {
        return "";
    }

    // 将时间戳和人员数组添加到 JSON 对象中
    root["timestamp"] = timestamp;
    root["persons"] = persons;

    //创建最终的 JSON 结构
    //nlohmann::json finalJson;
    //将 JSON 对象序列化为字符串
    //finalJson["data"] = root.dump();

    return root.dump();
}
