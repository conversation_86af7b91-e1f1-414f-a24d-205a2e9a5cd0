<?xml version="1.0" encoding="UTF-8"?>
<!-- 系统配置配置 -->
<SysConfig>
    <!--
    日志配置
    debug = 0,
    info = 1,
    warn = 2,
    err = 3,
    -->
    <logLevel>1</logLevel>
    <!-- 消息中心配置 -->
    <msgCenterUrl>nats://msgbus:msg1q2w3e@**************:4222</msgCenterUrl>
    <!-- redis服务端地址，从服务器同步参数-->
    <redisServiceIp>**************</redisServiceIp>
    <redisServicePort>8389</redisServicePort>
    <redisServicePwd>cg@1q2w3e</redisServicePwd>
    <redisServiceGatherDb>0</redisServiceGatherDb>
    <redisServiceStnDb>1</redisServiceStnDb>
    <redisServiceResultDb>2</redisServiceResultDb>
    <!-- 本地redis 配置-->
    <localRedisIp>firefly.local</localRedisIp>
    <localRedisPort>6379</localRedisPort>
    <localRedisPwd>jyg2021</localRedisPwd>
    <localRedisGatherDb>0</localRedisGatherDb>
    <localRedisStnDb>1</localRedisStnDb>
    <localRedisResultDb>2</localRedisResultDb>
    <localHostIp>************</localHostIp>
    <!-- redis key 配置 -->
    <intersectionDevicesKey>intersection_device</intersectionDevicesKey>
    <originStateKey>origin_state</originStateKey>
    <algConfigKey>algConfig</algConfigKey>
    <sensorConfigKey>sensorConfig</sensorConfigKey>
    <signalConfigKey>signalConfig</signalConfigKey>

    <!-- 算法配置落盘目录 -->
    <algConfigFileDir>config/alg/</algConfigFileDir>
    <algPhaseErrorThreshold>3</algPhaseErrorThreshold>
</SysConfig>