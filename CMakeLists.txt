cmake_minimum_required(VERSION 3.10)
project(SmartTrafficNexus VERSION 1.0)



# 根据构建类型设置编译选项
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_compile_options(-g -O0)
    add_definitions(-DDEBUG_MODE)
    message(STATUS "Debug mode enabled")
elseif(CMAKE_BUILD_TYPE STREQUAL "Release")
    add_compile_options(-O3)
    add_definitions(-DNDEBUG)
endif()

set(CMAKE_VERBOSE_MAKEFILE ON)

#add_compile_options(-v)
#add_link_options(-v)



# 设置 toolchina
if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm" OR CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
    message(STATUS "Current platform is ARM")
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64" OR CMAKE_SYSTEM_PROCESSOR MATCHES "i686")
    message(STATUS "Current platform is x86")
    #include(toolchain_linux.cmake)
    #set(TARGET_SOC "rk3588")
else()
    message(STATUS "Current platform is neither ARM nor x86")
endif()


# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
#set(CMAKE_CXX_STANDARD_REQUIRED True)
#set(CMAKE_CXX_FLAGS "-Wno-pedantic")

# 添加 include 目录
include_directories(
        #${PROJECT_SOURCE_DIR}/include
        ${PROJECT_SOURCE_DIR}/src
        ${PROJECT_SOURCE_DIR}/third_party/nlohmann_jsoncpp/include/
        ${PROJECT_SOURCE_DIR}/third_party/spdlog/include/
        ${PROJECT_SOURCE_DIR}/third_party/tinyxml2/
)

#[[
find_package(Git QUIET)
if(GIT_FOUND)
    # 获取当前分支名称
    execute_process(
            COMMAND ${GIT_EXECUTABLE} rev-parse --abbrev-ref HEAD
            WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
            OUTPUT_VARIABLE GIT_BRANCH
            OUTPUT_STRIP_TRAILING_WHITESPACE
    )

    # 获取最近tag信息
    execute_process(
            COMMAND ${GIT_EXECUTABLE} describe --tags --abbrev=0
            WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
            OUTPUT_VARIABLE GIT_TAG
            OUTPUT_STRIP_TRAILING_WHITESPACE
            ERROR_QUIET  # 忽略没有tag时的错误
    )

    # 获取tag和commit之间的差值(用于版本号)
    execute_process(
            COMMAND ${GIT_EXECUTABLE} describe --tags --always
            WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
            OUTPUT_VARIABLE GIT_VERSION
            OUTPUT_STRIP_TRAILING_WHITESPACE
            ERROR_QUIET
    )
else()
    # 添加更健壮的错误处理
    if(NOT GIT_TAG)
        set(GIT_TAG "none")
    endif()
    if(NOT GIT_VERSION)
        set(GIT_VERSION ${GIT_HASH})
    endif()
endif()

# 获取当前日期时间
string(TIMESTAMP BUILD_DATE "%Y-%m-%d %H:%M:%S")
# 创建版本信息头文件
configure_file(
        ${PROJECT_SOURCE_DIR}/src/utils/version_info.hpp.in
        ${PROJECT_SOURCE_DIR}/src/utils/version_info.h
)
]]

# 源文件
file(GLOB SOURCES
    "src/main.cpp"
)

# 添加子目录
add_subdirectory(src)

# 创建可执行文件
#target_include_directories(${PROJECT_NAME} PUBLIC ${PROJECT_SOURCE_DIR}/src ${PROJECT_SOURCE_DIR}/include)
add_executable(${PROJECT_NAME} ${SOURCES})

# 设置输出目录
set(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/bin)
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/lib)
set(INCLUDE_DIR ${PROJECT_SOURCE_DIR}/include)


#file(GLOB_RECURSE HEADERS
#    "src/*.hpp"
#)
## 自定义目标用于复制头文件
#add_custom_target(copy_headers ALL
#        COMMENT "Copying header files to include directory"
#)
#
#foreach(HEADER ${HEADERS})
#    # 获取头文件的相对路径
#    file(RELATIVE_PATH REL_PATH ${PROJECT_SOURCE_DIR}/src ${HEADER})
#
#    # 计算目标头文件路径
#    set(DEST_PATH ${INCLUDE_DIR}/${REL_PATH})
#
#
#    # 创建目标目录
#    get_filename_component(DEST_DIR ${DEST_PATH} DIRECTORY)
#    file(MAKE_DIRECTORY ${DEST_DIR})
#
#    # 添加复制命令到自定义目标
#    add_custom_command(TARGET copy_headers POST_BUILD
#            COMMAND ${CMAKE_COMMAND} -E copy ${HEADER} ${DEST_PATH}
#            COMMENT "Copying ${HEADER} to ${DEST_PATH}"
#    )
#endforeach()





# 查找并链接 pthread 库（对于多线程支持）
find_package(Threads REQUIRED)





# 查找 hiredis 库
find_path(hiredis/hiredis.h PATHS /opt/xiaolu/pubLib/redis/include NO_DEFAULT_PATH)
message(STATUS "HIREDIS_INCLUDE_DIR: ${HIREDIS_INCLUDE_DIR}")
find_library(HIREDIS_LIBS RedisClient PATHS /opt/xiaolu/pubLib/redis/lib NO_DEFAULT_PATH)
message(STATUS "HIREDIS_LIBS ${HIREDIS_LIBS}")

# 查找 event 库
find_path(EVENT_INCLUDE_DIR event2/event.h)
find_library(EVENT_LIBS event PATHS ${CMAKE_SYSROOT}/usr/lib/aarch64-linux-gnu)
message(STATUS "EVENT_INCLUDE_DIR: ${EVENT_INCLUDE_DIR}")
message(STATUS "EVENT_LIBS: ${EVENT_LIBS}")

# 查找 Python3 的解释器、开发库和头文件
#find_package(Python3 REQUIRED COMPONENTS Interpreter Development)

target_include_directories(${PROJECT_NAME} PRIVATE ${Python3_INCLUDE_DIRS} ${HIREDIS_INCLUDE_DIR})
target_link_libraries(${PROJECT_NAME} PRIVATE STNCore STNUtils Threads::Threads ${HIREDIS_LIBS} ${EVENT_LIBS}
        ${Python3_LIBRARIES})


# 查找 nats库和头文件
find_path(NATS_INCLUDE_DIR
        NAMES nats/nats.h
        PATHS /opt/xiaolu/pubLib/nats_clt/include
        NO_DEFAULT_PATH
)

find_library(NATS_LIBRARIES
        NAMES libnats.so.3.9.0
        PATHS /opt/xiaolu/pubLib/nats_clt/lib
        NO_DEFAULT_PATH
)
message(STATUS "NATS include dirs:" ${NATS_INCLUDE_DIR})
message(STATUS "NATS library dirs:" ${NATS_LIBRARIES})

# 检查是否找到头文件和库
if (NATS_INCLUDE_DIR AND NATS_LIBRARIES)
    include_directories(${NATS_INCLUDE_DIR})
    target_link_libraries(${PROJECT_NAME} PRIVATE ${NATS_LIBRARIES})
endif()





# 设置编译选项
# target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra -pedantic -Wunused-parameter)

# 添加测试
#enable_testing()
#add_subdirectory(tests)

# 添加工具
add_subdirectory(tools)

# 安装规则
set(CMAKE_INSTALL_PREFIX ${PROJECT_SOURCE_DIR})

# 添加主项目的自动安装
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_INSTALL_PREFIX}/bin)
set(LIBRARY_OUTPUT_PATH ${CMAKE_INSTALL_PREFIX}/lib)

add_custom_target(main_auto_install
    COMMAND ${CMAKE_COMMAND} -DCOMPONENT=Runtime -P ${CMAKE_BINARY_DIR}/cmake_install.cmake
    COMMENT "自动安装主程序到 ${CMAKE_INSTALL_PREFIX}"
)
add_dependencies(main_auto_install ${PROJECT_NAME})

# 将自动安装添加为ALL目标的依赖，使其在构建时自动执行
add_custom_target(auto_install_all ALL)
add_dependencies(auto_install_all main_auto_install)

# 打印一些配置信息
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Auto installation will be performed to: ${CMAKE_INSTALL_PREFIX}")