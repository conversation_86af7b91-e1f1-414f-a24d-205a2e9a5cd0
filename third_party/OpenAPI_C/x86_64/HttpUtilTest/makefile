#This file is generated by VisualGDB from your MSBuild project.
#Do not edit it manually as it will be regenerated on each build.
#You can build this project manually by running the following command line:
#	make -f <path to this file>
#The 'prepare' target needs to be built before the 'build' target to ensure all
#response files are up-to-date

ifneq ($(VERBOSE),1)
_V := @
endif

all:
	$(_V)$(MAKE) -f $(firstword $(MAKEFILE_LIST)) prepare
	$(_V)$(MAKE) -f $(firstword $(MAKEFILE_LIST)) build

ProvideDirectories:
	@mkdir -p VisualGDB/Release/

PREPARE_TARGETS := ProvideDirectories

#ClCompile - 2 files

UpdateRSP/VisualGDB/Release/HttpUtilTest.o:
	$(_V)echo "-ggdb -O0 -DNDEBUG=1 -DRELEASE=1 -ffunction-sections -fdata-sections" > VisualGDB/Release/HttpUtilTest.gcc.rsp-new
	$(_V)cmp -s VisualGDB/Release/HttpUtilTest.gcc.rsp-new VisualGDB/Release/HttpUtilTest.gcc.rsp || cp VisualGDB/Release/HttpUtilTest.gcc.rsp-new VisualGDB/Release/HttpUtilTest.gcc.rsp

UpdateRSP/VisualGDB/Release/stdafx.o:
	$(_V)echo "-ggdb -O0 -DNDEBUG=1 -DRELEASE=1 -ffunction-sections -fdata-sections" > VisualGDB/Release/stdafx.gcc.rsp-new
	$(_V)cmp -s VisualGDB/Release/stdafx.gcc.rsp-new VisualGDB/Release/stdafx.gcc.rsp || cp VisualGDB/Release/stdafx.gcc.rsp-new VisualGDB/Release/stdafx.gcc.rsp

-include VisualGDB/Release/HttpUtilTest.dep
VisualGDB/Release/HttpUtilTest.o: HttpUtilTest.cpp VisualGDB/Release/HttpUtilTest.gcc.rsp
	@echo HttpUtilTest.cpp
	$(_V)g++ @VisualGDB/Release/HttpUtilTest.gcc.rsp -c "HttpUtilTest.cpp" -o "VisualGDB/Release/HttpUtilTest.o" -MD -MP -MF "VisualGDB/Release/HttpUtilTest.dep"

-include VisualGDB/Release/stdafx.dep
VisualGDB/Release/stdafx.o: stdafx.cpp VisualGDB/Release/stdafx.gcc.rsp
	@echo stdafx.cpp
	$(_V)g++ @VisualGDB/Release/stdafx.gcc.rsp -c "stdafx.cpp" -o "VisualGDB/Release/stdafx.o" -MD -MP -MF "VisualGDB/Release/stdafx.dep"

PREPARE_TARGETS += UpdateRSP/VisualGDB/Release/HttpUtilTest.o UpdateRSP/VisualGDB/Release/stdafx.o
CLCOMPILE_TARGETS += VisualGDB/Release/HttpUtilTest.o VisualGDB/Release/stdafx.o


UpdateRSP/../bin/LINUX/HttpUtilTest.link.rsp:
	$(_V)mkdir -p "../bin/LINUX"
	$(_V)echo "-o ../bin/LINUX/HttpUtilTest -L../public/lib/linux/Release/ -Wl,-gc-sections  -Wl,--start-group VisualGDB/Release/HttpUtilTest.o VisualGDB/Release/stdafx.o -lHttpUtil -lhlog -lHCNetUtils -lcrypto -lssl -lhpr -lCsfTraceChain -Wl,--rpath='\$$ORIGIN' -Wl,--end-group" > ../bin/LINUX/HttpUtilTest.link.rsp-new
	$(_V)cmp -s ../bin/LINUX/HttpUtilTest.link.rsp-new ../bin/LINUX/HttpUtilTest.link.rsp || cp ../bin/LINUX/HttpUtilTest.link.rsp-new ../bin/LINUX/HttpUtilTest.link.rsp

../bin/LINUX/HttpUtilTest: ../bin/LINUX/HttpUtilTest.link.rsp VisualGDB/Release/HttpUtilTest.o VisualGDB/Release/stdafx.o
	@echo "Linking ../bin/LINUX/HttpUtilTest..."
	$(_V)g++ @../bin/LINUX/HttpUtilTest.link.rsp

PREPARE_TARGETS += UpdateRSP/../bin/LINUX/HttpUtilTest.link.rsp
FINAL_TARGETS += ../bin/LINUX/HttpUtilTest


.PHONY: $(PREPARE_TARGETS) prepare build all
prepare: $(PREPARE_TARGETS)
build: $(FINAL_TARGETS)
