// HttpUtilTest.cpp : �������̨Ӧ�ó������ڵ㡣
//

#include "stdafx.h"
#include "./../public/include/HttpUtil/HttpUtil.h"
#include <string>
#include <iostream>
//#include <Windows.h>

using namespace httpUtil;

//std::string szUrl = "https://172.7.13.242:443/artemis/api/resource/v1/cameras/indexCode";
//std::string szUrl = "https://************:443/artemis/api/resource/v1/cameras/indexCode";
std::string szUrl = "https://**********:443/artemis/api/resource/v1/cameras";
//std::string szUrl = "https://************:443/artemis/api/video/v1/cameras/previewURLs";
//std::string szBody = "{\"cameraIndexCode\":\"b79c98bce8a344beb90a1f6912529d21\"}";
std::string szBody = "{\"pageNo\": 1,\"pageSize\": 20,\"treeCode\": \"0\"}";
std::string appkey = "22918394";
std::string secret = "FiPnSrHq5nUVh63XwaG9";


int main()
{	
	int dataLen = 0;
	char* data = HTTPUTIL_Post(szUrl.c_str(), szBody.c_str(), appkey.c_str(), secret.c_str(), 15, &dataLen);
	if (NULL == data)
	{
		std::cout << "HttpPost fail, status is " << HTTPUTIL_GetLastStatus() << std::endl;
	}
	else
	{
		std::string rsp = std::string(data, dataLen);		
		std::cout << rsp << std::endl;
		HTTPUTIL_Free(data);
	}
	
    return 0;
}

