
##hlog.async=false
##hlog.secret.show=true
##hlog.secret.encrypt=true

#HttpUtil
log4j.logger.HttpUtil = TRACE, HttpUtil
log4j.appender.HttpUtil=org.apache.log4j.RollingFileAppender
log4j.appender.HttpUtil.File=./log/HttpUtil.log
log4j.appender.HttpUtil.MaxFileSize=25MB
log4j.appender.HttpUtil.MaxBackupIndex=10
log4j.appender.HttpUtil.Append=true
log4j.appender.HttpUtil.Threshold=TRACE
log4j.appender.HttpUtil.layout=org.apache.log4j.PatternLayout
log4j.appender.HttpUtil.layout.ConversionPattern=%d %-p %.16c [%t] %m%n